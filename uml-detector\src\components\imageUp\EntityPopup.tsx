// EntityPopup.tsx
import React from 'react';
import { getEntityPopupStyles } from './EntityPopupStyles';
import HistoryAnalysisSection from './HistoryAnalysisSection';

import { ClassData } from '../../services/HistoryAnalysisService';
import { useLanguage } from '../../context/LanguageContext';

interface EntityPopupProps {
  darkMode: boolean;
  entityName: string;
  position: { x: number; y: number };
  onClose: () => void;
  onModify: (selectedAttributes: string[], selectedMethods: string[]) => void;
  memoryAttributes: string[]; // Attributs de la classe depuis memoire.txt
  memoryMethods: string[];    // Méthodes de la classe depuis memoire.txt
  extractedAttributes: string[]; // Attributs extraits du texte actuel
  extractedMethods: string[];    // Méthodes extraites du texte actuel
  currentDiagramText?: string; // Texte du diagramme actuel pour exclure de l'analyse historique
}

const EntityPopup: React.FC<EntityPopupProps> = ({
  darkMode,
  entityName,
  position,
  onClose,
  onModify,
  memoryAttributes = [],
  memoryMethods = [],
  extractedAttributes = [],
  extractedMethods = [],
  currentDiagramText
}) => {
  const { t } = useLanguage();
  // État pour les checkboxes
  const [selectedAttributes, setSelectedAttributes] = React.useState<string[]>([]);
  const [selectedMethods, setSelectedMethods] = React.useState<string[]>([]);

  // États pour l'analyse historique - Résolution de conflits supprimée

  // Données de classe actuelle pour l'analyse historique
  const currentClassData: ClassData = React.useMemo(() => ({
    name: entityName,
    attributes: [...memoryAttributes, ...extractedAttributes],
    methods: [...memoryMethods, ...extractedMethods]
  }), [entityName, memoryAttributes, extractedAttributes, memoryMethods, extractedMethods]);

  // Filtrer pour n'afficher que les attributs et méthodes qui ne sont pas dans le texte extrait
  // Modifier la logique de filtrage pour être insensible à la casse
  const uniqueAttributes = memoryAttributes.filter(attr => {
    // Extraire le nom de l'attribut sans le type ou la visibilité
    const attrName = attr.replace(/^[+-]?\s*/, '').split(':')[0].trim().toLowerCase();
    
    return !extractedAttributes.some(extractedAttr => {
      // Extraire le nom de l'attribut extrait
      const extractedAttrName = extractedAttr.replace(/^[+-]?\s*/, '').split(':')[0].trim().toLowerCase();
      return extractedAttrName === attrName;
    });
  });

  const uniqueMethods = memoryMethods.filter(method => {
    // Extraire le nom de la méthode sans les paramètres
    const methodName = method.replace(/^[+-]?\s*/, '').split('(')[0].trim().toLowerCase();

    return !extractedMethods.some(extractedMethod => {
      // Extraire le nom de la méthode extraite
      const extractedMethodName = extractedMethod.replace(/^[+-]?\s*/, '').split('(')[0].trim().toLowerCase();
      return extractedMethodName === methodName;
    });
  });

  // DEBUG: Afficher toutes les données reçues par EntityPopup
  console.log("🎯 DEBUG EntityPopup - Données reçues pour", entityName);
  console.log("📥 memoryAttributes reçus:", memoryAttributes);
  console.log("📥 memoryMethods reçus:", memoryMethods);
  console.log("📥 extractedAttributes reçus:", extractedAttributes);
  console.log("📥 extractedMethods reçus:", extractedMethods);
  console.log("📊 uniqueAttributes calculés:", uniqueAttributes);
  console.log("📊 uniqueMethods calculés:", uniqueMethods);

  // Gestionnaires pour les checkboxes
  const handleAttributeToggle = (attribute: string) => {
    setSelectedAttributes(prev => 
      prev.includes(attribute) 
        ? prev.filter(attr => attr !== attribute) 
        : [...prev, attribute]
    );
  };

  const handleMethodToggle = (method: string) => {
    setSelectedMethods(prev =>
      prev.includes(method)
        ? prev.filter(m => m !== method)
        : [...prev, method]
    );
  };

  // Fonction pour gérer l'import depuis l'historique
  const handleHistoryImport = (importedData: ClassData) => {
    // Import direct sans résolution de conflits
    // Ajouter les éléments importés aux sélections actuelles
    const newSelectedAttributes = [...selectedAttributes];
    const newSelectedMethods = [...selectedMethods];

    // Ajouter les attributs importés (éviter les doublons)
    importedData.attributes.forEach(attr => {
      if (!newSelectedAttributes.includes(attr)) {
        newSelectedAttributes.push(attr);
      }
    });

    // Ajouter les méthodes importées (éviter les doublons)
    importedData.methods.forEach(method => {
      if (!newSelectedMethods.includes(method)) {
        newSelectedMethods.push(method);
      }
    });

    // Mettre à jour les sélections
    setSelectedAttributes(newSelectedAttributes);
    setSelectedMethods(newSelectedMethods);
  };


  
  // Fonction pour gérer le clic sur le bouton Modifier
  const handleModifyClick = () => {
    onModify(selectedAttributes, selectedMethods);
  };

  // Gestionnaires d'événements pour les effets hover
  const handleMouseEnter = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      (e.target as HTMLButtonElement).style.backgroundColor = darkMode ? '#475569' : '#f3f4f6';
    }
  };

  const handleMouseLeave = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      (e.target as HTMLButtonElement).style.backgroundColor = darkMode ? '#334155' : '#f8fafc';
    }
  };

  const handleModifyHover = (e: React.MouseEvent, isEnter: boolean) => {
    (e.target as HTMLButtonElement).style.backgroundColor = isEnter ? '#2563eb' : '#3b82f6';
    (e.target as HTMLButtonElement).style.transform = isEnter ? 'translateY(-1px)' : 'translateY(0)';
  };

  const handleCancelHover = (e: React.MouseEvent, isEnter: boolean) => {
    (e.target as HTMLButtonElement).style.backgroundColor = isEnter 
      ? (darkMode ? '#374151' : '#f9fafb') 
      : 'transparent';
  };

  // Gérer le clic sur l'overlay pour fermer le popup
  const handleOverlayClick = (e: React.MouseEvent) => {
    // Fermer seulement si on clique sur l'overlay, pas sur le popup
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  // Empêcher la propagation du clic depuis le popup vers l'overlay
  const handlePopupClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  // Obtenir les styles
  const styles = getEntityPopupStyles(darkMode);

  return (
    <div style={styles.overlay} onClick={handleOverlayClick}>
      <div style={styles.popup} onClick={handlePopupClick}>
        <div style={styles.header}>
          <div>
            <h3 style={styles.title}>{entityName}</h3>
            <div style={styles.subtitle}>{t('entity.subtitle')}</div>
          </div>
          <button 
            style={styles.closeButton} 
            onClick={onClose}
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
          >
            ×
          </button>
        </div>

        <div style={styles.content}>
          {/* Section d'analyse historique */}
          <HistoryAnalysisSection
            darkMode={darkMode}
            targetClassName={entityName}
            currentClassData={currentClassData}
            onImport={handleHistoryImport}
            currentDiagramText={currentDiagramText}
          />
          <div style={styles.tableContainer}>
            <table style={styles.table}>
              <thead style={styles.tableHeader}>
                <tr>
                  <th style={styles.tableHeaderCell}>{t('entity.attributes').toUpperCase()}</th>
                  <th style={styles.tableHeaderCell}>{t('entity.methods').toUpperCase()}</th>
                </tr>
              </thead>
              <tbody style={styles.tableBody}>
                <tr style={styles.tableRow}>
                  <td style={styles.tableCell}>
                    {uniqueAttributes.length > 0 ? (
                      uniqueAttributes.map((attr, index) => (
                        <div key={index} style={styles.checkboxContainer}>
                          <input 
                            type="checkbox" 
                            id={`attr-${index}`}
                            checked={selectedAttributes.includes(attr)}
                            onChange={() => handleAttributeToggle(attr)}
                            style={styles.checkbox}
                          />
                          <label 
                            htmlFor={`attr-${index}`}
                            style={styles.checkboxLabel}
                          >
                            {attr}
                          </label>
                        </div>
                      ))
                    ) : (
                      <div style={styles.emptyState}>
                        {t('entity.noAdditionalAttributes')}
                      </div>
                    )}
                  </td>
                  <td style={styles.tableCell}>
                    {uniqueMethods.length > 0 ? (
                      uniqueMethods.map((method, index) => (
                        <div key={index} style={styles.checkboxContainer}>
                          <input 
                            type="checkbox" 
                            id={`method-${index}`}
                            checked={selectedMethods.includes(method)}
                            onChange={() => handleMethodToggle(method)}
                            style={styles.checkbox}
                          />
                          <label 
                            htmlFor={`method-${index}`}
                            style={styles.checkboxLabel}
                          >
                            {method}
                          </label>
                        </div>
                      ))
                    ) : (
                      <div style={styles.emptyState}>
                        {t('entity.noAdditionalMethods')}
                      </div>
                    )}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          
          <div style={styles.buttonContainer}>
            <button 
              style={styles.modifyButton} 
              onClick={handleModifyClick}
              onMouseEnter={(e) => handleModifyHover(e, true)}
              onMouseLeave={(e) => handleModifyHover(e, false)}
            >
              <span>✏️</span>
              {t('entity.modifyEntity')}
            </button>
            <button
              style={styles.cancelButton}
              onClick={onClose}
              onMouseEnter={(e) => handleCancelHover(e, true)}
              onMouseLeave={(e) => handleCancelHover(e, false)}
            >
              {t('entity.cancel')}
            </button>
          </div>
        </div>
      </div>


    </div>
  );
};

export default EntityPopup;