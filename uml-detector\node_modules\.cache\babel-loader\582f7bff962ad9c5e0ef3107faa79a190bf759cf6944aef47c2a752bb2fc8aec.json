{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\FixTorchUMLDGM\\\\uml-detector\\\\src\\\\components\\\\DetectionArrow.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst DetectionArrow = ({\n  darkMode,\n  annotatedImage\n}) => {\n  _s();\n  const [imageLoading, setImageLoading] = useState(true);\n  const [imageError, setImageError] = useState(false);\n  const styles = {\n    container: {\n      maxWidth: \"800px\",\n      margin: \"0 auto\",\n      padding: \"20px\",\n      color: darkMode ? \"#e0e0e0\" : \"#333\"\n    },\n    header: {\n      textAlign: \"center\",\n      marginBottom: \"20px\"\n    },\n    imageContainer: {\n      display: \"flex\",\n      justifyContent: \"center\",\n      marginBottom: \"20px\"\n    },\n    annotatedImage: {\n      maxWidth: \"100%\",\n      borderRadius: \"8px\",\n      boxShadow: darkMode ? \"0 4px 8px rgba(0,0,0,0.5)\" : \"0 4px 8px rgba(0,0,0,0.1)\"\n    },\n    loadingContainer: {\n      textAlign: \"center\",\n      padding: \"40px\",\n      backgroundColor: darkMode ? \"#2d2d2d\" : \"#f5f5f5\",\n      borderRadius: \"8px\",\n      margin: \"20px auto\",\n      maxWidth: \"600px\"\n    },\n    errorContainer: {\n      textAlign: \"center\",\n      padding: \"40px\",\n      backgroundColor: darkMode ? \"#402020\" : \"#fff5f5\",\n      borderRadius: \"8px\",\n      margin: \"20px auto\",\n      maxWidth: \"600px\",\n      border: `1px solid ${darkMode ? \"#ff6b6b\" : \"#f56565\"}`\n    },\n    tip: {\n      backgroundColor: darkMode ? \"#333\" : \"#f8f9fa\",\n      padding: \"15px\",\n      borderRadius: \"8px\",\n      marginTop: \"30px\",\n      border: `1px solid ${darkMode ? \"#444\" : \"#e0e0e0\"}`\n    },\n    tipTitle: {\n      fontWeight: \"600\",\n      marginBottom: \"8px\",\n      display: \"flex\",\n      alignItems: \"center\",\n      gap: \"8px\"\n    },\n    explanationContainer: {\n      backgroundColor: darkMode ? \"#333\" : \"#fff\",\n      borderRadius: \"8px\",\n      padding: \"20px\",\n      marginTop: \"30px\",\n      boxShadow: darkMode ? \"0 2px 10px rgba(0,0,0,0.5)\" : \"0 2px 10px rgba(0,0,0,0.1)\"\n    },\n    explanationTitle: {\n      fontSize: \"18px\",\n      fontWeight: \"600\",\n      marginBottom: \"15px\"\n    },\n    explanationText: {\n      lineHeight: \"1.6\"\n    },\n    legendContainer: {\n      display: \"flex\",\n      flexWrap: \"wrap\",\n      gap: \"10px\",\n      marginTop: \"20px\"\n    },\n    legendItem: {\n      display: \"flex\",\n      alignItems: \"center\",\n      gap: \"8px\"\n    },\n    legendColor: {\n      width: \"20px\",\n      height: \"20px\",\n      borderRadius: \"4px\"\n    }\n  };\n  const handleImageLoad = () => {\n    setImageLoading(false);\n    setImageError(false);\n  };\n  const handleImageError = () => {\n    setImageLoading(false);\n    setImageError(true);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: styles.container,\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      style: styles.header,\n      children: \"D\\xE9tection de Relations UML\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 7\n    }, this), annotatedImage ? /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [imageLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.loadingContainer,\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Chargement de l'image annot\\xE9e...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 13\n      }, this), imageError ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.errorContainer,\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Impossible de charger l'image annot\\xE9e. Veuillez v\\xE9rifier que le serveur est en cours d'ex\\xE9cution.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"URL: \", annotatedImage]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.imageContainer,\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: annotatedImage,\n          alt: \"Diagramme UML annot\\xE9 avec relations d\\xE9tect\\xE9es\",\n          style: {\n            ...styles.annotatedImage,\n            display: imageLoading ? \"none\" : \"block\"\n          },\n          onLoad: handleImageLoad,\n          onError: handleImageError\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.explanationContainer,\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: styles.explanationTitle,\n          children: \"Analyse des Relations\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: styles.explanationText,\n          children: \"L'image ci-dessus montre les relations d\\xE9tect\\xE9es entre les diff\\xE9rentes classes de votre diagramme UML. Notre syst\\xE8me d'IA a identifi\\xE9 les connexions et les a marqu\\xE9es selon leur type.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.legendContainer,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.legendItem,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                ...styles.legendColor,\n                backgroundColor: \"#4CAF50\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Classe\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.legendItem,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                ...styles.legendColor,\n                backgroundColor: \"#FF0000\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Relation\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.legendItem,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                ...styles.legendColor,\n                backgroundColor: \"#0000FF\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Agr\\xE9gation\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.legendItem,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                ...styles.legendColor,\n                backgroundColor: \"#00FFFF\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Composition\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.legendItem,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                ...styles.legendColor,\n                backgroundColor: \"#9C27B0\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"G\\xE9n\\xE9ralisation\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.legendItem,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                ...styles.legendColor,\n                backgroundColor: \"rgb(137, 147, 131)\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Association\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.tip,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.tipTitle,\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"20\",\n            height: \"20\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: darkMode ? \"#e0e0e0\" : \"#333\",\n            strokeWidth: \"2\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n              cx: \"12\",\n              cy: \"12\",\n              r: \"10\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n              x1: \"12\",\n              y1: \"16\",\n              x2: \"12\",\n              y2: \"12\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n              x1: \"12\",\n              y1: \"8\",\n              x2: \"12.01\",\n              y2: \"8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 15\n          }, this), \"Astuce\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Pour une analyse plus d\\xE9taill\\xE9e, consultez l'onglet \\\"Extraction de texte UML\\\" qui contient les informations textuelles compl\\xE8tes de toutes les classes d\\xE9tect\\xE9es.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.loadingContainer,\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Aucune image annot\\xE9e n'est disponible. Veuillez d'abord t\\xE9l\\xE9charger et analyser une image de diagramme UML.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 108,\n    columnNumber: 5\n  }, this);\n};\n_s(DetectionArrow, \"1QKq6MuANuDh8ZIYL1gx4ygMTJo=\");\n_c = DetectionArrow;\nexport default DetectionArrow;\nvar _c;\n$RefreshReg$(_c, \"DetectionArrow\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "DetectionArrow", "darkMode", "annotatedImage", "_s", "imageLoading", "setImageLoading", "imageError", "setImageError", "styles", "container", "max<PERSON><PERSON><PERSON>", "margin", "padding", "color", "header", "textAlign", "marginBottom", "imageContainer", "display", "justifyContent", "borderRadius", "boxShadow", "loadingContainer", "backgroundColor", "<PERSON><PERSON><PERSON><PERSON>", "border", "tip", "marginTop", "tipTitle", "fontWeight", "alignItems", "gap", "<PERSON><PERSON><PERSON><PERSON>", "explanationTitle", "fontSize", "explanationText", "lineHeight", "<PERSON><PERSON><PERSON><PERSON>", "flexWrap", "legendItem", "legendColor", "width", "height", "handleImageLoad", "handleImageError", "style", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "alt", "onLoad", "onError", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "cx", "cy", "r", "x1", "y1", "x2", "y2", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/FixTorchUMLDGM/uml-detector/src/components/DetectionArrow.tsx"], "sourcesContent": ["import React, { useState } from \"react\";\r\n\r\ninterface DetectionArrowProps {\r\n  darkMode: boolean;\r\n  annotatedImage: string | null;\r\n}\r\n\r\nconst DetectionArrow: React.FC<DetectionArrowProps> = ({ darkMode, annotatedImage }) => {\r\n  const [imageLoading, setImageLoading] = useState(true);\r\n  const [imageError, setImageError] = useState(false);\r\n\r\n  const styles = {\r\n    container: {\r\n      maxWidth: \"800px\",\r\n      margin: \"0 auto\",\r\n      padding: \"20px\",\r\n      color: darkMode ? \"#e0e0e0\" : \"#333\"\r\n    },\r\n    header: {\r\n      textAlign: \"center\" as const,\r\n      marginBottom: \"20px\"\r\n    },\r\n    imageContainer: {\r\n      display: \"flex\",\r\n      justifyContent: \"center\",\r\n      marginBottom: \"20px\"\r\n    },\r\n    annotatedImage: {\r\n      maxWidth: \"100%\",\r\n      borderRadius: \"8px\",\r\n      boxShadow: darkMode ? \"0 4px 8px rgba(0,0,0,0.5)\" : \"0 4px 8px rgba(0,0,0,0.1)\"\r\n    },\r\n    loadingContainer: {\r\n      textAlign: \"center\" as const,\r\n      padding: \"40px\",\r\n      backgroundColor: darkMode ? \"#2d2d2d\" : \"#f5f5f5\",\r\n      borderRadius: \"8px\",\r\n      margin: \"20px auto\",\r\n      maxWidth: \"600px\"\r\n    },\r\n    errorContainer: {\r\n      textAlign: \"center\" as const,\r\n      padding: \"40px\",\r\n      backgroundColor: darkMode ? \"#402020\" : \"#fff5f5\",\r\n      borderRadius: \"8px\",\r\n      margin: \"20px auto\",\r\n      maxWidth: \"600px\",\r\n      border: `1px solid ${darkMode ? \"#ff6b6b\" : \"#f56565\"}`\r\n    },\r\n    tip: {\r\n      backgroundColor: darkMode ? \"#333\" : \"#f8f9fa\",\r\n      padding: \"15px\",\r\n      borderRadius: \"8px\",\r\n      marginTop: \"30px\",\r\n      border: `1px solid ${darkMode ? \"#444\" : \"#e0e0e0\"}`\r\n    },\r\n    tipTitle: {\r\n      fontWeight: \"600\",\r\n      marginBottom: \"8px\",\r\n      display: \"flex\",\r\n      alignItems: \"center\",\r\n      gap: \"8px\"\r\n    },\r\n    explanationContainer: {\r\n      backgroundColor: darkMode ? \"#333\" : \"#fff\",\r\n      borderRadius: \"8px\",\r\n      padding: \"20px\",\r\n      marginTop: \"30px\",\r\n      boxShadow: darkMode ? \"0 2px 10px rgba(0,0,0,0.5)\" : \"0 2px 10px rgba(0,0,0,0.1)\"\r\n    },\r\n    explanationTitle: {\r\n      fontSize: \"18px\",\r\n      fontWeight: \"600\",\r\n      marginBottom: \"15px\"\r\n    },\r\n    explanationText: {\r\n      lineHeight: \"1.6\"\r\n    },\r\n    legendContainer: {\r\n      display: \"flex\",\r\n      flexWrap: \"wrap\" as const,\r\n      gap: \"10px\",\r\n      marginTop: \"20px\"\r\n    },\r\n    legendItem: {\r\n      display: \"flex\",\r\n      alignItems: \"center\",\r\n      gap: \"8px\"\r\n    },\r\n    legendColor: {\r\n      width: \"20px\",\r\n      height: \"20px\",\r\n      borderRadius: \"4px\"\r\n    }\r\n  };\r\n\r\n  const handleImageLoad = () => {\r\n    setImageLoading(false);\r\n    setImageError(false);\r\n  };\r\n\r\n  const handleImageError = () => {\r\n    setImageLoading(false);\r\n    setImageError(true);\r\n  };\r\n\r\n  return (\r\n    <div style={styles.container}>\r\n      <h2 style={styles.header}>Détection de Relations UML</h2>\r\n\r\n      {annotatedImage ? (\r\n        <>\r\n          {imageLoading && (\r\n            <div style={styles.loadingContainer}>\r\n              <p>Chargement de l'image annotée...</p>\r\n            </div>\r\n          )}\r\n\r\n          {imageError ? (\r\n            <div style={styles.errorContainer}>\r\n              <p>Impossible de charger l'image annotée. Veuillez vérifier que le serveur est en cours d'exécution.</p>\r\n              <p>URL: {annotatedImage}</p>\r\n            </div>\r\n          ) : (\r\n            <div style={styles.imageContainer}>\r\n              <img\r\n                src={annotatedImage}\r\n                alt=\"Diagramme UML annoté avec relations détectées\"\r\n                style={{\r\n                  ...styles.annotatedImage,\r\n                  display: imageLoading ? \"none\" : \"block\"\r\n                }}\r\n                onLoad={handleImageLoad}\r\n                onError={handleImageError}\r\n              />\r\n            </div>\r\n          )}\r\n\r\n          <div style={styles.explanationContainer}>\r\n            <h3 style={styles.explanationTitle}>Analyse des Relations</h3>\r\n            <p style={styles.explanationText}>\r\n              L'image ci-dessus montre les relations détectées entre les différentes classes de votre diagramme UML.\r\n              Notre système d'IA a identifié les connexions et les a marquées selon leur type.\r\n            </p>\r\n\r\n            <div style={styles.legendContainer}>\r\n              <div style={styles.legendItem}>\r\n                <div style={{...styles.legendColor, backgroundColor: \"#4CAF50\"}}></div>\r\n                <span>Classe</span>\r\n              </div>\r\n              <div style={styles.legendItem}>\r\n                <div style={{...styles.legendColor, backgroundColor: \"#FF0000\"}}></div>\r\n                <span>Relation</span>\r\n              </div>\r\n              <div style={styles.legendItem}>\r\n                <div style={{...styles.legendColor, backgroundColor: \"#0000FF\"}}></div>\r\n                <span>Agrégation</span>\r\n              </div>\r\n              <div style={styles.legendItem}>\r\n                <div style={{...styles.legendColor, backgroundColor: \"#00FFFF\"}}></div>\r\n                <span>Composition</span>\r\n              </div>\r\n              <div style={styles.legendItem}>\r\n                <div style={{...styles.legendColor, backgroundColor: \"#9C27B0\"}}></div>\r\n                <span>Généralisation</span>\r\n              </div>\r\n              <div style={styles.legendItem}>\r\n                <div style={{...styles.legendColor, backgroundColor: \"rgb(137, 147, 131)\"}}></div>\r\n                <span>Association</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div style={styles.tip}>\r\n            <div style={styles.tipTitle}>\r\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke={darkMode ? \"#e0e0e0\" : \"#333\"} strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\r\n                <circle cx=\"12\" cy=\"12\" r=\"10\"></circle>\r\n                <line x1=\"12\" y1=\"16\" x2=\"12\" y2=\"12\"></line>\r\n                <line x1=\"12\" y1=\"8\" x2=\"12.01\" y2=\"8\"></line>\r\n              </svg>\r\n              Astuce\r\n            </div>\r\n            <p>Pour une analyse plus détaillée, consultez l'onglet \"Extraction de texte UML\" qui contient les informations textuelles complètes de toutes les classes détectées.</p>\r\n          </div>\r\n        </>\r\n      ) : (\r\n        <div style={styles.loadingContainer}>\r\n          <p>Aucune image annotée n'est disponible. Veuillez d'abord télécharger et analyser une image de diagramme UML.</p>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DetectionArrow;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAOxC,MAAMC,cAA6C,GAAGA,CAAC;EAAEC,QAAQ;EAAEC;AAAe,CAAC,KAAK;EAAAC,EAAA;EACtF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACW,UAAU,EAAEC,aAAa,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EAEnD,MAAMa,MAAM,GAAG;IACbC,SAAS,EAAE;MACTC,QAAQ,EAAE,OAAO;MACjBC,MAAM,EAAE,QAAQ;MAChBC,OAAO,EAAE,MAAM;MACfC,KAAK,EAAEZ,QAAQ,GAAG,SAAS,GAAG;IAChC,CAAC;IACDa,MAAM,EAAE;MACNC,SAAS,EAAE,QAAiB;MAC5BC,YAAY,EAAE;IAChB,CAAC;IACDC,cAAc,EAAE;MACdC,OAAO,EAAE,MAAM;MACfC,cAAc,EAAE,QAAQ;MACxBH,YAAY,EAAE;IAChB,CAAC;IACDd,cAAc,EAAE;MACdQ,QAAQ,EAAE,MAAM;MAChBU,YAAY,EAAE,KAAK;MACnBC,SAAS,EAAEpB,QAAQ,GAAG,2BAA2B,GAAG;IACtD,CAAC;IACDqB,gBAAgB,EAAE;MAChBP,SAAS,EAAE,QAAiB;MAC5BH,OAAO,EAAE,MAAM;MACfW,eAAe,EAAEtB,QAAQ,GAAG,SAAS,GAAG,SAAS;MACjDmB,YAAY,EAAE,KAAK;MACnBT,MAAM,EAAE,WAAW;MACnBD,QAAQ,EAAE;IACZ,CAAC;IACDc,cAAc,EAAE;MACdT,SAAS,EAAE,QAAiB;MAC5BH,OAAO,EAAE,MAAM;MACfW,eAAe,EAAEtB,QAAQ,GAAG,SAAS,GAAG,SAAS;MACjDmB,YAAY,EAAE,KAAK;MACnBT,MAAM,EAAE,WAAW;MACnBD,QAAQ,EAAE,OAAO;MACjBe,MAAM,EAAE,aAAaxB,QAAQ,GAAG,SAAS,GAAG,SAAS;IACvD,CAAC;IACDyB,GAAG,EAAE;MACHH,eAAe,EAAEtB,QAAQ,GAAG,MAAM,GAAG,SAAS;MAC9CW,OAAO,EAAE,MAAM;MACfQ,YAAY,EAAE,KAAK;MACnBO,SAAS,EAAE,MAAM;MACjBF,MAAM,EAAE,aAAaxB,QAAQ,GAAG,MAAM,GAAG,SAAS;IACpD,CAAC;IACD2B,QAAQ,EAAE;MACRC,UAAU,EAAE,KAAK;MACjBb,YAAY,EAAE,KAAK;MACnBE,OAAO,EAAE,MAAM;MACfY,UAAU,EAAE,QAAQ;MACpBC,GAAG,EAAE;IACP,CAAC;IACDC,oBAAoB,EAAE;MACpBT,eAAe,EAAEtB,QAAQ,GAAG,MAAM,GAAG,MAAM;MAC3CmB,YAAY,EAAE,KAAK;MACnBR,OAAO,EAAE,MAAM;MACfe,SAAS,EAAE,MAAM;MACjBN,SAAS,EAAEpB,QAAQ,GAAG,4BAA4B,GAAG;IACvD,CAAC;IACDgC,gBAAgB,EAAE;MAChBC,QAAQ,EAAE,MAAM;MAChBL,UAAU,EAAE,KAAK;MACjBb,YAAY,EAAE;IAChB,CAAC;IACDmB,eAAe,EAAE;MACfC,UAAU,EAAE;IACd,CAAC;IACDC,eAAe,EAAE;MACfnB,OAAO,EAAE,MAAM;MACfoB,QAAQ,EAAE,MAAe;MACzBP,GAAG,EAAE,MAAM;MACXJ,SAAS,EAAE;IACb,CAAC;IACDY,UAAU,EAAE;MACVrB,OAAO,EAAE,MAAM;MACfY,UAAU,EAAE,QAAQ;MACpBC,GAAG,EAAE;IACP,CAAC;IACDS,WAAW,EAAE;MACXC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,MAAM;MACdtB,YAAY,EAAE;IAChB;EACF,CAAC;EAED,MAAMuB,eAAe,GAAGA,CAAA,KAAM;IAC5BtC,eAAe,CAAC,KAAK,CAAC;IACtBE,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,MAAMqC,gBAAgB,GAAGA,CAAA,KAAM;IAC7BvC,eAAe,CAAC,KAAK,CAAC;IACtBE,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,oBACEV,OAAA;IAAKgD,KAAK,EAAErC,MAAM,CAACC,SAAU;IAAAqC,QAAA,gBAC3BjD,OAAA;MAAIgD,KAAK,EAAErC,MAAM,CAACM,MAAO;MAAAgC,QAAA,EAAC;IAA0B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EAExDhD,cAAc,gBACbL,OAAA,CAAAE,SAAA;MAAA+C,QAAA,GACG1C,YAAY,iBACXP,OAAA;QAAKgD,KAAK,EAAErC,MAAM,CAACc,gBAAiB;QAAAwB,QAAA,eAClCjD,OAAA;UAAAiD,QAAA,EAAG;QAAgC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CACN,EAEA5C,UAAU,gBACTT,OAAA;QAAKgD,KAAK,EAAErC,MAAM,CAACgB,cAAe;QAAAsB,QAAA,gBAChCjD,OAAA;UAAAiD,QAAA,EAAG;QAAiG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACxGrD,OAAA;UAAAiD,QAAA,GAAG,OAAK,EAAC5C,cAAc;QAAA;UAAA6C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,gBAENrD,OAAA;QAAKgD,KAAK,EAAErC,MAAM,CAACS,cAAe;QAAA6B,QAAA,eAChCjD,OAAA;UACEsD,GAAG,EAAEjD,cAAe;UACpBkD,GAAG,EAAC,wDAA+C;UACnDP,KAAK,EAAE;YACL,GAAGrC,MAAM,CAACN,cAAc;YACxBgB,OAAO,EAAEd,YAAY,GAAG,MAAM,GAAG;UACnC,CAAE;UACFiD,MAAM,EAAEV,eAAgB;UACxBW,OAAO,EAAEV;QAAiB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN,eAEDrD,OAAA;QAAKgD,KAAK,EAAErC,MAAM,CAACwB,oBAAqB;QAAAc,QAAA,gBACtCjD,OAAA;UAAIgD,KAAK,EAAErC,MAAM,CAACyB,gBAAiB;UAAAa,QAAA,EAAC;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9DrD,OAAA;UAAGgD,KAAK,EAAErC,MAAM,CAAC2B,eAAgB;UAAAW,QAAA,EAAC;QAGlC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAEJrD,OAAA;UAAKgD,KAAK,EAAErC,MAAM,CAAC6B,eAAgB;UAAAS,QAAA,gBACjCjD,OAAA;YAAKgD,KAAK,EAAErC,MAAM,CAAC+B,UAAW;YAAAO,QAAA,gBAC5BjD,OAAA;cAAKgD,KAAK,EAAE;gBAAC,GAAGrC,MAAM,CAACgC,WAAW;gBAAEjB,eAAe,EAAE;cAAS;YAAE;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvErD,OAAA;cAAAiD,QAAA,EAAM;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eACNrD,OAAA;YAAKgD,KAAK,EAAErC,MAAM,CAAC+B,UAAW;YAAAO,QAAA,gBAC5BjD,OAAA;cAAKgD,KAAK,EAAE;gBAAC,GAAGrC,MAAM,CAACgC,WAAW;gBAAEjB,eAAe,EAAE;cAAS;YAAE;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvErD,OAAA;cAAAiD,QAAA,EAAM;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,eACNrD,OAAA;YAAKgD,KAAK,EAAErC,MAAM,CAAC+B,UAAW;YAAAO,QAAA,gBAC5BjD,OAAA;cAAKgD,KAAK,EAAE;gBAAC,GAAGrC,MAAM,CAACgC,WAAW;gBAAEjB,eAAe,EAAE;cAAS;YAAE;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvErD,OAAA;cAAAiD,QAAA,EAAM;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,eACNrD,OAAA;YAAKgD,KAAK,EAAErC,MAAM,CAAC+B,UAAW;YAAAO,QAAA,gBAC5BjD,OAAA;cAAKgD,KAAK,EAAE;gBAAC,GAAGrC,MAAM,CAACgC,WAAW;gBAAEjB,eAAe,EAAE;cAAS;YAAE;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvErD,OAAA;cAAAiD,QAAA,EAAM;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eACNrD,OAAA;YAAKgD,KAAK,EAAErC,MAAM,CAAC+B,UAAW;YAAAO,QAAA,gBAC5BjD,OAAA;cAAKgD,KAAK,EAAE;gBAAC,GAAGrC,MAAM,CAACgC,WAAW;gBAAEjB,eAAe,EAAE;cAAS;YAAE;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvErD,OAAA;cAAAiD,QAAA,EAAM;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,eACNrD,OAAA;YAAKgD,KAAK,EAAErC,MAAM,CAAC+B,UAAW;YAAAO,QAAA,gBAC5BjD,OAAA;cAAKgD,KAAK,EAAE;gBAAC,GAAGrC,MAAM,CAACgC,WAAW;gBAAEjB,eAAe,EAAE;cAAoB;YAAE;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClFrD,OAAA;cAAAiD,QAAA,EAAM;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENrD,OAAA;QAAKgD,KAAK,EAAErC,MAAM,CAACkB,GAAI;QAAAoB,QAAA,gBACrBjD,OAAA;UAAKgD,KAAK,EAAErC,MAAM,CAACoB,QAAS;UAAAkB,QAAA,gBAC1BjD,OAAA;YAAK4C,KAAK,EAAC,IAAI;YAACC,MAAM,EAAC,IAAI;YAACa,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAACC,MAAM,EAAExD,QAAQ,GAAG,SAAS,GAAG,MAAO;YAACyD,WAAW,EAAC,GAAG;YAACC,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC,OAAO;YAAAd,QAAA,gBAC7JjD,OAAA;cAAQgE,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC,IAAI;cAACC,CAAC,EAAC;YAAI;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,eACxCrD,OAAA;cAAMmE,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC;YAAI;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7CrD,OAAA;cAAMmE,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC,GAAG;cAACC,EAAE,EAAC,OAAO;cAACC,EAAE,EAAC;YAAG;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,UAER;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNrD,OAAA;UAAAiD,QAAA,EAAG;QAAiK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrK,CAAC;IAAA,eACN,CAAC,gBAEHrD,OAAA;MAAKgD,KAAK,EAAErC,MAAM,CAACc,gBAAiB;MAAAwB,QAAA,eAClCjD,OAAA;QAAAiD,QAAA,EAAG;MAA2G;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/G,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC/C,EAAA,CAzLIH,cAA6C;AAAAoE,EAAA,GAA7CpE,cAA6C;AA2LnD,eAAeA,cAAc;AAAC,IAAAoE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}