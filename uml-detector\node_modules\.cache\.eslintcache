[{"C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\index.tsx": "1", "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\App.tsx": "3", "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\UmlDiagr\\UMLDiagrameExtractor.tsx": "4", "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\DetectionArrow.tsx": "5", "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\AppStyles.tsx": "6", "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\Loginsignup\\Login.tsx": "7", "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\imageUp\\ImageUploader.tsx": "8", "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\UmlDiagr\\UMLDiagrameExtractorStyles.tsx": "9", "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\imageUp\\AnalysisResults.tsx": "10", "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\Loginsignup\\UserProfile.tsx": "11", "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\UmlDiagr\\convertToMermaid.ts": "12", "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\Documentation.tsx": "13", "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\imageUp\\LoadingSpinner.tsx": "14", "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\context\\HistoryContext.tsx": "15", "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\History\\HistorySidebar.tsx": "16", "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\context\\AuthContext.tsx": "17", "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\config.ts": "18", "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\Loginsignup\\LoginStyles.tsx": "19", "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\History\\HistorySidebar.styles.ts": "20", "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\UmlDiagr\\convertToJava.ts": "21", "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\services\\HistoryAnalysisService.ts": "22", "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\imageUp\\ConflictResolutionModal.tsx": "23", "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\imageUp\\EntityPopup.tsx": "24", "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\imageUp\\EntityPopupStyles.ts": "25", "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\imageUp\\HistoryAnalysisSection.tsx": "26", "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\context\\LanguageContext.tsx": "27"}, {"size": 554, "mtime": 1743134091741, "results": "28", "hashOfConfig": "29"}, {"size": 425, "mtime": 1743134091738, "results": "30", "hashOfConfig": "29"}, {"size": 13319, "mtime": 1751543093448, "results": "31", "hashOfConfig": "29"}, {"size": 19607, "mtime": 1751544074833, "results": "32", "hashOfConfig": "29"}, {"size": 6342, "mtime": 1751546143943, "results": "33", "hashOfConfig": "29"}, {"size": 5302, "mtime": 1751542922323, "results": "34", "hashOfConfig": "29"}, {"size": 12450, "mtime": 1751544442591, "results": "35", "hashOfConfig": "29"}, {"size": 15201, "mtime": 1751543536618, "results": "36", "hashOfConfig": "29"}, {"size": 6735, "mtime": 1748353633295, "results": "37", "hashOfConfig": "29"}, {"size": 30938, "mtime": 1751545989022, "results": "38", "hashOfConfig": "29"}, {"size": 6109, "mtime": 1751544476410, "results": "39", "hashOfConfig": "29"}, {"size": 10444, "mtime": 1748363097655, "results": "40", "hashOfConfig": "29"}, {"size": 31772, "mtime": 1751547163475, "results": "41", "hashOfConfig": "29"}, {"size": 1538, "mtime": 1746536725382, "results": "42", "hashOfConfig": "29"}, {"size": 4852, "mtime": 1751217692569, "results": "43", "hashOfConfig": "29"}, {"size": 7104, "mtime": 1751544752042, "results": "44", "hashOfConfig": "29"}, {"size": 3550, "mtime": 1751217711360, "results": "45", "hashOfConfig": "29"}, {"size": 770, "mtime": 1748254620634, "results": "46", "hashOfConfig": "29"}, {"size": 6160, "mtime": 1748509603191, "results": "47", "hashOfConfig": "29"}, {"size": 3422, "mtime": 1748518747011, "results": "48", "hashOfConfig": "29"}, {"size": 9332, "mtime": 1751210954303, "results": "49", "hashOfConfig": "29"}, {"size": 11475, "mtime": 1751226591317, "results": "50", "hashOfConfig": "29"}, {"size": 13033, "mtime": 1751215875586, "results": "51", "hashOfConfig": "29"}, {"size": 11203, "mtime": 1751545147380, "results": "52", "hashOfConfig": "29"}, {"size": 9471, "mtime": 1751216633292, "results": "53", "hashOfConfig": "29"}, {"size": 28809, "mtime": 1751546970686, "results": "54", "hashOfConfig": "29"}, {"size": 24770, "mtime": 1751547197571, "results": "55", "hashOfConfig": "29"}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "aiy5ot", {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\UmlDiagr\\UMLDiagrameExtractor.tsx", ["137", "138"], [], "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\DetectionArrow.tsx", [], [], "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\AppStyles.tsx", ["139"], [], "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\Loginsignup\\Login.tsx", ["140"], [], "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\imageUp\\ImageUploader.tsx", ["141"], [], "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\UmlDiagr\\UMLDiagrameExtractorStyles.tsx", [], [], "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\imageUp\\AnalysisResults.tsx", [], [], "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\Loginsignup\\UserProfile.tsx", [], [], "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\UmlDiagr\\convertToMermaid.ts", [], [], "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\Documentation.tsx", [], [], "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\imageUp\\LoadingSpinner.tsx", [], [], "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\context\\HistoryContext.tsx", [], [], "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\History\\HistorySidebar.tsx", [], [], "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\context\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\config.ts", [], [], "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\Loginsignup\\LoginStyles.tsx", [], [], "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\History\\HistorySidebar.styles.ts", [], [], "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\UmlDiagr\\convertToJava.ts", [], [], "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\services\\HistoryAnalysisService.ts", [], [], "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\imageUp\\ConflictResolutionModal.tsx", [], [], "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\imageUp\\EntityPopup.tsx", [], [], "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\imageUp\\EntityPopupStyles.ts", [], [], "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\imageUp\\HistoryAnalysisSection.tsx", [], [], "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\context\\LanguageContext.tsx", [], [], {"ruleId": "142", "severity": 1, "message": "143", "line": 67, "column": 6, "nodeType": "144", "endLine": 67, "endColumn": 16, "suggestions": "145"}, {"ruleId": "142", "severity": 1, "message": "146", "line": 106, "column": 6, "nodeType": "144", "endLine": 106, "endColumn": 27, "suggestions": "147"}, {"ruleId": "148", "severity": 1, "message": "149", "line": 1, "column": 8, "nodeType": "150", "messageId": "151", "endLine": 1, "endColumn": 13}, {"ruleId": "152", "severity": 1, "message": "153", "line": 265, "column": 19, "nodeType": "154", "endLine": 265, "endColumn": 57}, {"ruleId": "142", "severity": 1, "message": "155", "line": 286, "column": 6, "nodeType": "144", "endLine": 286, "endColumn": 45, "suggestions": "156"}, "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'renderMermaidDiagram' and 'umlText'. Either include them or remove the dependency array.", "ArrayExpression", ["157"], "React Hook useEffect has a missing dependency: 'renderMermaidDiagram'. Either include it or remove the dependency array.", ["158"], "@typescript-eslint/no-unused-vars", "'React' is defined but never used.", "Identifier", "unusedVar", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "React Hook useEffect has missing dependencies: 'addToHistoryOnce' and 't'. Either include them or remove the dependency array.", ["159"], {"desc": "160", "fix": "161"}, {"desc": "162", "fix": "163"}, {"desc": "164", "fix": "165"}, "Update the dependencies array to be: [darkMode, renderMermaidDiagram, umlText]", {"range": "166", "text": "167"}, "Update the dependencies array to be: [umlText, exportType, renderMermaidDiagram]", {"range": "168", "text": "169"}, "Update the dependencies array to be: [textUrl, imageUrl, onAnalysisComplete, t, addToHistoryOnce]", {"range": "170", "text": "171"}, [2342, 2352], "[darkMode, renderMermaidDiagram, umlText]", [3487, 3508], "[umlText, exportType, renderMermaidDiagram]", [9671, 9710], "[textUrl, imageUrl, onAnalysisComplete, t, addToHistoryOnce]"]