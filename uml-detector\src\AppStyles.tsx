import React from 'react';
import { CSSProperties } from 'react';

// Define styles as a constant that can be imported by the main component
export const styles: Record<string, CSSProperties> = {
  container: {
    fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
    minHeight: "100vh",
    display: "flex",
    transition: "background-color 0.3s ease, color 0.3s ease"
  },
  historySidebar: {
    height: "100vh",
    position: "fixed",
    left: 0,
    top: 0,
    overflowY: "auto" as const,
    transition: "width 0.3s ease",
    backgroundColor: "inherit",
    zIndex: 10
  },
  historyHeader: {
    padding: "1.25rem 1rem",
    borderBottom: "1px solid rgba(148, 163, 184, 0.15)",
    display: "flex",
    flexDirection: "column" as const,
    gap: "12px"
  },
  historyTitle: {
    fontSize: "1rem",
    fontWeight: "600",
    margin: 0
  },
  newProjectButton: {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    gap: "6px",
    padding: "0.5rem",
    backgroundColor: "rgba(59, 130, 246, 0.1)",
    color: "#60a5fa",
    border: "1px solid",
    borderColor: "rgba(96, 165, 250, 0.2)",
    borderRadius: "6px",
    fontSize: "0.875rem",
    fontWeight: "500",
    cursor: "pointer",
    width: "100%",
    transition: "all 0.2s ease",
  },
  historyList: {
    padding: "0.5rem 0"
  },
  historyItem: {
    padding: "0.75rem 1rem",
    display: "flex",
    alignItems: "center",
    gap: "10px",
    cursor: "pointer",
    transition: "background-color 0.2s ease",
    borderLeft: "3px solid transparent"
  },
  historyThumbnail: {
    width: "32px",
    height: "32px",
    borderRadius: "4px",
    objectFit: "cover" as const
  },
  historyItemContent: {
    overflow: "hidden"
  },
  historyItemTitle: {
    fontSize: "0.875rem",
    fontWeight: "500",
    whiteSpace: "nowrap" as const,
    overflow: "hidden",
    textOverflow: "ellipsis"
  },
  historyItemDate: {
    fontSize: "0.75rem",
    opacity: 0.6,
    marginTop: "2px"
  },
  mainWrapper: {
    flex: 1,
    display: "flex",
    flexDirection: "column" as const,
    minHeight: "100vh",
    transition: "margin-left 0.3s ease"
  },
  header: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    padding: "1rem 1.5rem",
    borderBottom: "1px solid rgba(148, 163, 184, 0.15)"
  },
  headerLeft: {
    display: "flex",
    alignItems: "center",
    gap: "12px"
  },
  toggleHistoryButton: {
    background: "none",
    border: "none",
    color: "inherit",
    cursor: "pointer",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    padding: "6px",
    borderRadius: "6px",
    transition: "background-color 0.2s ease"
  },
  title: {
    fontSize: "1.25rem",
    fontWeight: "600",
    margin: 0,
    letterSpacing: "-0.025em"
  },
  headerRight: {
    display: "flex",
    alignItems: "center",
    gap: "12px"
  },
  iconButton: {
    background: "transparent",
    border: "1px solid",
    borderColor: "rgba(148, 163, 184, 0.2)",
    borderRadius: "6px",
    width: "36px",
    height: "36px",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    cursor: "pointer",
    color: "inherit",
    transition: "all 0.2s ease"
  },
  loginButton: {
    background: "transparent",
    border: "1px solid",
    borderColor: "rgba(96, 165, 250, 0.3)",
    color: "#60a5fa",
    borderRadius: "6px",
    padding: "0.5rem 1rem",
    fontSize: "0.875rem",
    fontWeight: "500",
    cursor: "pointer",
    display: "flex",
    alignItems: "center",
    gap: "8px",
    transition: "all 0.2s ease"
  },
  languageButton: {
    background: "transparent",
    border: "1px solid",
    borderColor: "rgba(148, 163, 184, 0.2)",
    borderRadius: "6px",
    width: "36px",
    height: "36px",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    cursor: "pointer",
    color: "inherit",
    fontSize: "0.75rem",
    fontWeight: "600",
    transition: "all 0.2s ease"
  },
  tabs: {
    display: "flex",
    gap: "1.5rem",
    padding: "0 1.5rem",
    borderBottom: "1px solid rgba(148, 163, 184, 0.15)"
  },
  tabButton: {
    background: "none",
    border: "none",
    padding: "1rem 0.25rem",
    fontSize: "0.875rem",
    cursor: "pointer",
    fontWeight: "500",
    transition: "all 0.2s ease",
    display: "flex",
    alignItems: "center",
    gap: "8px"
  },
  contentSection: {
    flex: 1,
    display: "flex",
    flexDirection: "column" as const
  },
  contentHeader: {
    padding: "1.5rem 2rem 1rem",
  },
  contentTitle: {
    fontSize: "1.25rem",
    fontWeight: "600",
    margin: "0 0 0.5rem",
    letterSpacing: "-0.025em"
  },
  contentSubtitle: {
    fontSize: "0.875rem",
    opacity: 0.8,
    margin: 0,
    fontWeight: "400"
  },
  contentWrapper: {
    flex: 1,
    padding: "1rem 2rem 2rem",
    overflowY: "auto" as const
  },
  footer: {
    padding: "1rem 2rem",
    borderTop: "1px solid rgba(148, 163, 184, 0.15)",
    fontSize: "0.75rem",
    opacity: 0.6,
    textAlign: "center" as const
  }
};