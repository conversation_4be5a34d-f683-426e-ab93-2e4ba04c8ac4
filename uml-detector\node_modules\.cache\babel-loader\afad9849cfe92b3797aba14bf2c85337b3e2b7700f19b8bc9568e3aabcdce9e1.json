{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\FixTorchUMLDGM\\\\uml-detector\\\\src\\\\components\\\\Loginsignup\\\\Login.tsx\",\n  _s = $RefreshSig$();\n// src/components/Auth/Login.tsx\nimport React, { useState } from 'react';\nimport { X, AlertCircle } from 'lucide-react';\nimport { useAuth } from '../../context/AuthContext';\nimport { getLoginStyles } from './LoginStyles';\nimport { useLanguage } from '../../context/LanguageContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Login = ({\n  darkMode,\n  onClose\n}) => {\n  _s();\n  // États du composant\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [confirmPassword, setConfirmPassword] = useState('');\n  const [name, setName] = useState('');\n  const [rememberMe, setRememberMe] = useState(false);\n  const [isLogin, setIsLogin] = useState(true);\n\n  // États d'erreur\n  const [emailError, setEmailError] = useState('');\n  const [passwordError, setPasswordError] = useState('');\n  const [confirmPasswordError, setConfirmPasswordError] = useState('');\n  const [nameError, setNameError] = useState('');\n  const [generalError, setGeneralError] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const {\n    login,\n    signup,\n    loginWithGoogle\n  } = useAuth();\n  const {\n    t\n  } = useLanguage();\n\n  // Fonction de validation email\n  const validateEmail = email => {\n    const re = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return re.test(email);\n  };\n\n  // Réinitialiser toutes les erreurs\n  const resetErrors = () => {\n    setEmailError('');\n    setPasswordError('');\n    setConfirmPasswordError('');\n    setNameError('');\n    setGeneralError('');\n  };\n\n  // Gestionnaire de soumission du formulaire\n  const handleSubmit = async e => {\n    if (e) e.preventDefault();\n    resetErrors();\n    let hasErrors = false;\n\n    // Validation email\n    if (!email) {\n      setEmailError(t('auth.error.emailRequired'));\n      hasErrors = true;\n    } else if (!validateEmail(email)) {\n      setEmailError(t('auth.error.emailInvalid'));\n      hasErrors = true;\n    }\n\n    // Validation mot de passe\n    if (!password) {\n      setPasswordError(t('auth.error.passwordRequired'));\n      hasErrors = true;\n    } else if (password.length < 6) {\n      setPasswordError(t('auth.error.passwordTooShort'));\n      hasErrors = true;\n    }\n\n    // Validations supplémentaires pour l'inscription\n    if (!isLogin) {\n      // Validation nom\n      if (!name) {\n        setNameError(t('auth.error.nameRequired'));\n        hasErrors = true;\n      }\n\n      // Validation confirmation mot de passe\n      if (!confirmPassword) {\n        setConfirmPasswordError(t('auth.error.confirmPasswordRequired'));\n        hasErrors = true;\n      } else if (password !== confirmPassword) {\n        setConfirmPasswordError(t('auth.error.passwordMismatch'));\n        hasErrors = true;\n      }\n    }\n    if (!hasErrors) {\n      setIsLoading(true);\n      try {\n        if (isLogin) {\n          await login(email, password);\n          onClose();\n        } else {\n          await signup(email, password, name);\n          onClose();\n        }\n      } catch (error) {\n        console.error(\"Authentication error:\", error);\n        let errorMessage = t('auth.error.authFailed');\n\n        // Gestion des erreurs Firebase\n        if (error instanceof Error) {\n          const errorCode = error.code;\n          switch (errorCode) {\n            case 'auth/user-not-found':\n            case 'auth/wrong-password':\n              errorMessage = t('auth.error.invalidCredentials');\n              break;\n            case 'auth/email-already-in-use':\n              errorMessage = t('auth.error.emailInUse');\n              break;\n            case 'auth/weak-password':\n              errorMessage = t('auth.error.weakPassword');\n              break;\n            case 'auth/invalid-email':\n              errorMessage = t('auth.error.emailInvalid');\n              break;\n            case 'auth/too-many-requests':\n              errorMessage = t('auth.error.tooManyRequests');\n              break;\n            default:\n              errorMessage = error.message;\n          }\n        }\n        setGeneralError(errorMessage);\n      } finally {\n        setIsLoading(false);\n      }\n    }\n  };\n\n  // Gestionnaire de connexion Google\n  const handleGoogleLogin = async () => {\n    setIsLoading(true);\n    try {\n      await loginWithGoogle();\n      onClose();\n    } catch (error) {\n      console.error(\"Google login error:\", error);\n      setGeneralError(t('auth.error.googleLoginFailed'));\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Gestionnaire de changement de mode (connexion/inscription)\n  const handleModeSwitch = () => {\n    resetErrors();\n    setIsLogin(!isLogin);\n  };\n\n  // Obtenir les styles\n  const styles = getLoginStyles(darkMode);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: styles.overlay,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.modalContainer,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.modalHeader,\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          style: styles.modalTitle,\n          children: isLogin ? t('auth.login.title') : t('auth.signup.title')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          style: styles.closeButton,\n          onClick: onClose,\n          children: /*#__PURE__*/_jsxDEV(X, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.modalBody,\n        children: [generalError && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.errorAlert,\n          children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: generalError\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          style: styles.formContainer,\n          children: [!isLogin && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.inputGroup,\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: styles.label,\n              htmlFor: \"name\",\n              children: t('auth.field.name')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"name\",\n              style: {\n                ...styles.input,\n                borderColor: nameError ? '#ef4444' : styles.input.borderColor\n              },\n              type: \"text\",\n              value: name,\n              onChange: e => setName(e.target.value),\n              placeholder: t('auth.placeholder.name')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 17\n            }, this), nameError && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: styles.errorText,\n              children: nameError\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 31\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.inputGroup,\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: styles.label,\n              htmlFor: \"email\",\n              children: t('auth.field.email')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"email\",\n              style: {\n                ...styles.input,\n                borderColor: emailError ? '#ef4444' : styles.input.borderColor\n              },\n              type: \"email\",\n              value: email,\n              onChange: e => setEmail(e.target.value),\n              placeholder: t('auth.placeholder.email')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 15\n            }, this), emailError && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: styles.errorText,\n              children: emailError\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 30\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.inputGroup,\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: styles.label,\n              htmlFor: \"password\",\n              children: t('auth.field.password')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"password\",\n              style: {\n                ...styles.input,\n                borderColor: passwordError ? '#ef4444' : styles.input.borderColor\n              },\n              type: \"password\",\n              value: password,\n              onChange: e => setPassword(e.target.value),\n              placeholder: isLogin ? '••••••••' : t('auth.placeholder.password')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 15\n            }, this), passwordError && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: styles.errorText,\n              children: passwordError\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this), !isLogin && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.inputGroup,\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: styles.label,\n              htmlFor: \"confirm-password\",\n              children: t('auth.field.confirmPassword')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"confirm-password\",\n              style: {\n                ...styles.input,\n                borderColor: confirmPasswordError ? '#ef4444' : styles.input.borderColor\n              },\n              type: \"password\",\n              value: confirmPassword,\n              onChange: e => setConfirmPassword(e.target.value),\n              placeholder: t('auth.placeholder.confirmPassword')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 17\n            }, this), confirmPasswordError && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: styles.errorText,\n              children: confirmPasswordError\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 42\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 15\n          }, this), isLogin && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: styles.checkbox,\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                style: styles.checkboxInput,\n                type: \"checkbox\",\n                id: \"remember-me\",\n                checked: rememberMe,\n                onChange: e => setRememberMe(e.target.checked)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"remember-me\",\n                style: styles.checkboxLabel,\n                children: t('auth.rememberMe')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: styles.forgotPassword,\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#\",\n                style: styles.forgotLink,\n                children: t('auth.forgotPassword')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true), !isLogin && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.checkbox,\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              style: styles.checkboxInput,\n              type: \"checkbox\",\n              id: \"terms\",\n              checked: rememberMe,\n              onChange: e => setRememberMe(e.target.checked)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"terms\",\n              style: styles.checkboxLabel,\n              children: t('auth.acceptTerms')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            style: {\n              ...styles.submitButton,\n              opacity: isLoading ? 0.7 : 1,\n              cursor: isLoading ? 'not-allowed' : 'pointer'\n            },\n            disabled: isLoading,\n            children: isLoading ? isLogin ? 'Logging in...' : 'Creating account...' : isLogin ? 'Log in' : 'Create account'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.divider,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.dividerLine\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: styles.dividerText,\n            children: \"OR\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.dividerLine\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.socialLogin,\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            style: {\n              ...styles.socialButton,\n              opacity: isLoading ? 0.7 : 1,\n              cursor: isLoading ? 'not-allowed' : 'pointer'\n            },\n            onClick: handleGoogleLogin,\n            disabled: isLoading,\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"20\",\n              height: \"20\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              xmlns: \"http://www.w3.org/2000/svg\",\n              children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\",\n                fill: \"#4285F4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\",\n                fill: \"#34A853\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l3.66-2.84z\",\n                fill: \"#FBBC05\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\",\n                fill: \"#EA4335\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 15\n            }, this), \"Continue with Google\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.switchMode,\n          children: [isLogin ? \"Don't have an account?\" : \"Already have an account?\", /*#__PURE__*/_jsxDEV(\"button\", {\n            style: styles.switchButton,\n            onClick: handleModeSwitch,\n            children: isLogin ? 'Sign up' : 'Log in'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 162,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 161,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"5Hx/s3OvfK2AX6YhdjjjAwkKvhY=\", false, function () {\n  return [useAuth, useLanguage];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "X", "AlertCircle", "useAuth", "getLoginStyles", "useLanguage", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON>", "darkMode", "onClose", "_s", "email", "setEmail", "password", "setPassword", "confirmPassword", "setConfirmPassword", "name", "setName", "rememberMe", "setRememberMe", "is<PERSON>ogin", "setIsLogin", "emailError", "setEmailError", "passwordError", "setPasswordError", "confirmPasswordError", "setConfirmPasswordError", "nameError", "setNameError", "general<PERSON><PERSON><PERSON>", "setGeneralError", "isLoading", "setIsLoading", "login", "signup", "loginWithGoogle", "t", "validateEmail", "re", "test", "resetErrors", "handleSubmit", "e", "preventDefault", "hasErrors", "length", "error", "console", "errorMessage", "Error", "errorCode", "code", "message", "handleGoogleLogin", "handleModeSwitch", "styles", "style", "overlay", "children", "modalContainer", "modalHeader", "modalTitle", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "closeButton", "onClick", "size", "modalBody", "<PERSON><PERSON><PERSON><PERSON>", "onSubmit", "formContainer", "inputGroup", "label", "htmlFor", "id", "input", "borderColor", "type", "value", "onChange", "target", "placeholder", "errorText", "checkbox", "checkboxInput", "checked", "checkboxLabel", "forgotPassword", "href", "forgotLink", "submitButton", "opacity", "cursor", "disabled", "divider", "dividerLine", "dividerText", "socialLogin", "socialButton", "width", "height", "viewBox", "fill", "xmlns", "d", "switchMode", "switchButton", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/FixTorchUMLDGM/uml-detector/src/components/Loginsignup/Login.tsx"], "sourcesContent": ["// src/components/Auth/Login.tsx\r\nimport React, { useState } from 'react';\r\nimport { X, AlertCircle } from 'lucide-react';\r\nimport { useAuth } from '../../context/AuthContext';\r\nimport { getLoginStyles } from './LoginStyles';\r\nimport { useLanguage } from '../../context/LanguageContext';\r\n\r\ninterface LoginProps {\r\n  darkMode: boolean;\r\n  onClose: () => void;\r\n}\r\n\r\nconst Login: React.FC<LoginProps> = ({ darkMode, onClose }) => {\r\n  // États du composant\r\n  const [email, setEmail] = useState<string>('');\r\n  const [password, setPassword] = useState<string>('');\r\n  const [confirmPassword, setConfirmPassword] = useState<string>('');\r\n  const [name, setName] = useState<string>('');\r\n  const [rememberMe, setRememberMe] = useState<boolean>(false);\r\n  const [isLogin, setIsLogin] = useState<boolean>(true);\r\n  \r\n  // États d'erreur\r\n  const [emailError, setEmailError] = useState<string>('');\r\n  const [passwordError, setPasswordError] = useState<string>('');\r\n  const [confirmPasswordError, setConfirmPasswordError] = useState<string>('');\r\n  const [nameError, setNameError] = useState<string>('');\r\n  const [generalError, setGeneralError] = useState<string>('');\r\n  const [isLoading, setIsLoading] = useState<boolean>(false);\r\n\r\n  const { login, signup, loginWithGoogle } = useAuth();\r\n  const { t } = useLanguage();\r\n\r\n  // Fonction de validation email\r\n  const validateEmail = (email: string): boolean => {\r\n    const re = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n    return re.test(email);\r\n  };\r\n\r\n  // Réinitialiser toutes les erreurs\r\n  const resetErrors = () => {\r\n    setEmailError('');\r\n    setPasswordError('');\r\n    setConfirmPasswordError('');\r\n    setNameError('');\r\n    setGeneralError('');\r\n  };\r\n\r\n  // Gestionnaire de soumission du formulaire\r\n  const handleSubmit = async (e?: React.FormEvent): Promise<void> => {\r\n    if (e) e.preventDefault();\r\n    \r\n    resetErrors();\r\n    let hasErrors = false;\r\n    \r\n    // Validation email\r\n    if (!email) {\r\n      setEmailError(t('auth.error.emailRequired'));\r\n      hasErrors = true;\r\n    } else if (!validateEmail(email)) {\r\n      setEmailError(t('auth.error.emailInvalid'));\r\n      hasErrors = true;\r\n    }\r\n\r\n    // Validation mot de passe\r\n    if (!password) {\r\n      setPasswordError(t('auth.error.passwordRequired'));\r\n      hasErrors = true;\r\n    } else if (password.length < 6) {\r\n      setPasswordError(t('auth.error.passwordTooShort'));\r\n      hasErrors = true;\r\n    }\r\n\r\n    // Validations supplémentaires pour l'inscription\r\n    if (!isLogin) {\r\n      // Validation nom\r\n      if (!name) {\r\n        setNameError(t('auth.error.nameRequired'));\r\n        hasErrors = true;\r\n      }\r\n\r\n      // Validation confirmation mot de passe\r\n      if (!confirmPassword) {\r\n        setConfirmPasswordError(t('auth.error.confirmPasswordRequired'));\r\n        hasErrors = true;\r\n      } else if (password !== confirmPassword) {\r\n        setConfirmPasswordError(t('auth.error.passwordMismatch'));\r\n        hasErrors = true;\r\n      }\r\n    }\r\n    \r\n    if (!hasErrors) {\r\n      setIsLoading(true);\r\n      try {\r\n        if (isLogin) {\r\n          await login(email, password);\r\n          onClose();\r\n        } else {\r\n          await signup(email, password, name);\r\n          onClose();\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Authentication error:\", error);\r\n        let errorMessage = t('auth.error.authFailed');\r\n\r\n        // Gestion des erreurs Firebase\r\n        if (error instanceof Error) {\r\n          const errorCode = (error as any).code;\r\n          switch (errorCode) {\r\n            case 'auth/user-not-found':\r\n            case 'auth/wrong-password':\r\n              errorMessage = t('auth.error.invalidCredentials');\r\n              break;\r\n            case 'auth/email-already-in-use':\r\n              errorMessage = t('auth.error.emailInUse');\r\n              break;\r\n            case 'auth/weak-password':\r\n              errorMessage = t('auth.error.weakPassword');\r\n              break;\r\n            case 'auth/invalid-email':\r\n              errorMessage = t('auth.error.emailInvalid');\r\n              break;\r\n            case 'auth/too-many-requests':\r\n              errorMessage = t('auth.error.tooManyRequests');\r\n              break;\r\n            default:\r\n              errorMessage = error.message;\r\n          }\r\n        }\r\n        \r\n        setGeneralError(errorMessage);\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    }\r\n  };\r\n\r\n  // Gestionnaire de connexion Google\r\n  const handleGoogleLogin = async () => {\r\n    setIsLoading(true);\r\n    try {\r\n      await loginWithGoogle();\r\n      onClose();\r\n    } catch (error) {\r\n      console.error(\"Google login error:\", error);\r\n      setGeneralError(t('auth.error.googleLoginFailed'));\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  // Gestionnaire de changement de mode (connexion/inscription)\r\n  const handleModeSwitch = () => {\r\n    resetErrors();\r\n    setIsLogin(!isLogin);\r\n  };\r\n\r\n  // Obtenir les styles\r\n  const styles = getLoginStyles(darkMode);\r\n\r\n  return (\r\n    <div style={styles.overlay}>\r\n      <div style={styles.modalContainer}>\r\n        <div style={styles.modalHeader}>\r\n          <h2 style={styles.modalTitle}>\r\n            {isLogin ? t('auth.login.title') : t('auth.signup.title')}\r\n          </h2>\r\n          <button style={styles.closeButton} onClick={onClose}>\r\n            <X size={20} />\r\n          </button>\r\n        </div>\r\n        \r\n        <div style={styles.modalBody}>\r\n          {generalError && (\r\n            <div style={styles.errorAlert}>\r\n              <AlertCircle size={16} />\r\n              <span>{generalError}</span>\r\n            </div>\r\n          )}\r\n\r\n          <form onSubmit={handleSubmit} style={styles.formContainer}>\r\n            {!isLogin && (\r\n              <div style={styles.inputGroup}>\r\n                <label style={styles.label} htmlFor=\"name\">{t('auth.field.name')}</label>\r\n                <input\r\n                  id=\"name\"\r\n                  style={{\r\n                    ...styles.input,\r\n                    borderColor: nameError ? '#ef4444' : styles.input.borderColor\r\n                  }}\r\n                  type=\"text\"\r\n                  value={name}\r\n                  onChange={(e) => setName(e.target.value)}\r\n                  placeholder={t('auth.placeholder.name')}\r\n                />\r\n                {nameError && <div style={styles.errorText}>{nameError}</div>}\r\n              </div>\r\n            )}\r\n            \r\n            <div style={styles.inputGroup}>\r\n              <label style={styles.label} htmlFor=\"email\">{t('auth.field.email')}</label>\r\n              <input\r\n                id=\"email\"\r\n                style={{\r\n                  ...styles.input,\r\n                  borderColor: emailError ? '#ef4444' : styles.input.borderColor\r\n                }}\r\n                type=\"email\"\r\n                value={email}\r\n                onChange={(e) => setEmail(e.target.value)}\r\n                placeholder={t('auth.placeholder.email')}\r\n              />\r\n              {emailError && <div style={styles.errorText}>{emailError}</div>}\r\n            </div>\r\n            \r\n            <div style={styles.inputGroup}>\r\n              <label style={styles.label} htmlFor=\"password\">{t('auth.field.password')}</label>\r\n              <input\r\n                id=\"password\"\r\n                style={{\r\n                  ...styles.input,\r\n                  borderColor: passwordError ? '#ef4444' : styles.input.borderColor\r\n                }}\r\n                type=\"password\"\r\n                value={password}\r\n                onChange={(e) => setPassword(e.target.value)}\r\n                placeholder={isLogin ? '••••••••' : t('auth.placeholder.password')}\r\n              />\r\n              {passwordError && <div style={styles.errorText}>{passwordError}</div>}\r\n            </div>\r\n\r\n            {!isLogin && (\r\n              <div style={styles.inputGroup}>\r\n                <label style={styles.label} htmlFor=\"confirm-password\">{t('auth.field.confirmPassword')}</label>\r\n                <input\r\n                  id=\"confirm-password\"\r\n                  style={{\r\n                    ...styles.input,\r\n                    borderColor: confirmPasswordError ? '#ef4444' : styles.input.borderColor\r\n                  }}\r\n                  type=\"password\"\r\n                  value={confirmPassword}\r\n                  onChange={(e) => setConfirmPassword(e.target.value)}\r\n                  placeholder={t('auth.placeholder.confirmPassword')}\r\n                />\r\n                {confirmPasswordError && <div style={styles.errorText}>{confirmPasswordError}</div>}\r\n              </div>\r\n            )}\r\n            \r\n            {isLogin && (\r\n              <>\r\n                <div style={styles.checkbox}>\r\n                  <input\r\n                    style={styles.checkboxInput}\r\n                    type=\"checkbox\"\r\n                    id=\"remember-me\"\r\n                    checked={rememberMe}\r\n                    onChange={(e) => setRememberMe(e.target.checked)}\r\n                  />\r\n                  <label htmlFor=\"remember-me\" style={styles.checkboxLabel}>\r\n                    {t('auth.rememberMe')}\r\n                  </label>\r\n                </div>\r\n\r\n                <div style={styles.forgotPassword}>\r\n                  <a href=\"#\" style={styles.forgotLink}>{t('auth.forgotPassword')}</a>\r\n                </div>\r\n              </>\r\n            )}\r\n            \r\n            {!isLogin && (\r\n              <div style={styles.checkbox}>\r\n                <input\r\n                  style={styles.checkboxInput}\r\n                  type=\"checkbox\"\r\n                  id=\"terms\"\r\n                  checked={rememberMe}\r\n                  onChange={(e) => setRememberMe(e.target.checked)}\r\n                />\r\n                <label htmlFor=\"terms\" style={styles.checkboxLabel}>\r\n                  {t('auth.acceptTerms')}\r\n                </label>\r\n              </div>\r\n            )}\r\n            \r\n            <button \r\n              type=\"submit\" \r\n              style={{\r\n                ...styles.submitButton,\r\n                opacity: isLoading ? 0.7 : 1,\r\n                cursor: isLoading ? 'not-allowed' : 'pointer'\r\n              }}\r\n              disabled={isLoading}\r\n            >\r\n              {isLoading ? \r\n                (isLogin ? 'Logging in...' : 'Creating account...') : \r\n                (isLogin ? 'Log in' : 'Create account')}\r\n            </button>\r\n          </form>\r\n          \r\n          <div style={styles.divider}>\r\n            <div style={styles.dividerLine}></div>\r\n            <span style={styles.dividerText}>OR</span>\r\n            <div style={styles.dividerLine}></div>\r\n          </div>\r\n          \r\n          <div style={styles.socialLogin}>\r\n            <button \r\n              type=\"button\" \r\n              style={{\r\n                ...styles.socialButton,\r\n                opacity: isLoading ? 0.7 : 1,\r\n                cursor: isLoading ? 'not-allowed' : 'pointer'\r\n              }}\r\n              onClick={handleGoogleLogin}\r\n              disabled={isLoading}\r\n            >\r\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path d=\"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\" fill=\"#4285F4\"/>\r\n                <path d=\"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\" fill=\"#34A853\"/>\r\n                <path d=\"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l3.66-2.84z\" fill=\"#FBBC05\"/>\r\n                <path d=\"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\" fill=\"#EA4335\"/>\r\n              </svg>\r\n              Continue with Google\r\n            </button>\r\n          </div>\r\n          \r\n          <div style={styles.switchMode}>\r\n            {isLogin ? \"Don't have an account?\" : \"Already have an account?\"}\r\n            <button style={styles.switchButton} onClick={handleModeSwitch}>\r\n              {isLogin ? 'Sign up' : 'Log in'}\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Login;"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,CAAC,EAAEC,WAAW,QAAQ,cAAc;AAC7C,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,cAAc,QAAQ,eAAe;AAC9C,SAASC,WAAW,QAAQ,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAO5D,MAAMC,KAA2B,GAAGA,CAAC;EAAEC,QAAQ;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAC7D;EACA,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGf,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAACgB,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAS,EAAE,CAAC;EACpD,MAAM,CAACkB,eAAe,EAAEC,kBAAkB,CAAC,GAAGnB,QAAQ,CAAS,EAAE,CAAC;EAClE,MAAM,CAACoB,IAAI,EAAEC,OAAO,CAAC,GAAGrB,QAAQ,CAAS,EAAE,CAAC;EAC5C,MAAM,CAACsB,UAAU,EAAEC,aAAa,CAAC,GAAGvB,QAAQ,CAAU,KAAK,CAAC;EAC5D,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAU,IAAI,CAAC;;EAErD;EACA,MAAM,CAAC0B,UAAU,EAAEC,aAAa,CAAC,GAAG3B,QAAQ,CAAS,EAAE,CAAC;EACxD,MAAM,CAAC4B,aAAa,EAAEC,gBAAgB,CAAC,GAAG7B,QAAQ,CAAS,EAAE,CAAC;EAC9D,MAAM,CAAC8B,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG/B,QAAQ,CAAS,EAAE,CAAC;EAC5E,MAAM,CAACgC,SAAS,EAAEC,YAAY,CAAC,GAAGjC,QAAQ,CAAS,EAAE,CAAC;EACtD,MAAM,CAACkC,YAAY,EAAEC,eAAe,CAAC,GAAGnC,QAAQ,CAAS,EAAE,CAAC;EAC5D,MAAM,CAACoC,SAAS,EAAEC,YAAY,CAAC,GAAGrC,QAAQ,CAAU,KAAK,CAAC;EAE1D,MAAM;IAAEsC,KAAK;IAAEC,MAAM;IAAEC;EAAgB,CAAC,GAAGrC,OAAO,CAAC,CAAC;EACpD,MAAM;IAAEsC;EAAE,CAAC,GAAGpC,WAAW,CAAC,CAAC;;EAE3B;EACA,MAAMqC,aAAa,GAAI5B,KAAa,IAAc;IAChD,MAAM6B,EAAE,GAAG,4BAA4B;IACvC,OAAOA,EAAE,CAACC,IAAI,CAAC9B,KAAK,CAAC;EACvB,CAAC;;EAED;EACA,MAAM+B,WAAW,GAAGA,CAAA,KAAM;IACxBlB,aAAa,CAAC,EAAE,CAAC;IACjBE,gBAAgB,CAAC,EAAE,CAAC;IACpBE,uBAAuB,CAAC,EAAE,CAAC;IAC3BE,YAAY,CAAC,EAAE,CAAC;IAChBE,eAAe,CAAC,EAAE,CAAC;EACrB,CAAC;;EAED;EACA,MAAMW,YAAY,GAAG,MAAOC,CAAmB,IAAoB;IACjE,IAAIA,CAAC,EAAEA,CAAC,CAACC,cAAc,CAAC,CAAC;IAEzBH,WAAW,CAAC,CAAC;IACb,IAAII,SAAS,GAAG,KAAK;;IAErB;IACA,IAAI,CAACnC,KAAK,EAAE;MACVa,aAAa,CAACc,CAAC,CAAC,0BAA0B,CAAC,CAAC;MAC5CQ,SAAS,GAAG,IAAI;IAClB,CAAC,MAAM,IAAI,CAACP,aAAa,CAAC5B,KAAK,CAAC,EAAE;MAChCa,aAAa,CAACc,CAAC,CAAC,yBAAyB,CAAC,CAAC;MAC3CQ,SAAS,GAAG,IAAI;IAClB;;IAEA;IACA,IAAI,CAACjC,QAAQ,EAAE;MACba,gBAAgB,CAACY,CAAC,CAAC,6BAA6B,CAAC,CAAC;MAClDQ,SAAS,GAAG,IAAI;IAClB,CAAC,MAAM,IAAIjC,QAAQ,CAACkC,MAAM,GAAG,CAAC,EAAE;MAC9BrB,gBAAgB,CAACY,CAAC,CAAC,6BAA6B,CAAC,CAAC;MAClDQ,SAAS,GAAG,IAAI;IAClB;;IAEA;IACA,IAAI,CAACzB,OAAO,EAAE;MACZ;MACA,IAAI,CAACJ,IAAI,EAAE;QACTa,YAAY,CAACQ,CAAC,CAAC,yBAAyB,CAAC,CAAC;QAC1CQ,SAAS,GAAG,IAAI;MAClB;;MAEA;MACA,IAAI,CAAC/B,eAAe,EAAE;QACpBa,uBAAuB,CAACU,CAAC,CAAC,oCAAoC,CAAC,CAAC;QAChEQ,SAAS,GAAG,IAAI;MAClB,CAAC,MAAM,IAAIjC,QAAQ,KAAKE,eAAe,EAAE;QACvCa,uBAAuB,CAACU,CAAC,CAAC,6BAA6B,CAAC,CAAC;QACzDQ,SAAS,GAAG,IAAI;MAClB;IACF;IAEA,IAAI,CAACA,SAAS,EAAE;MACdZ,YAAY,CAAC,IAAI,CAAC;MAClB,IAAI;QACF,IAAIb,OAAO,EAAE;UACX,MAAMc,KAAK,CAACxB,KAAK,EAAEE,QAAQ,CAAC;UAC5BJ,OAAO,CAAC,CAAC;QACX,CAAC,MAAM;UACL,MAAM2B,MAAM,CAACzB,KAAK,EAAEE,QAAQ,EAAEI,IAAI,CAAC;UACnCR,OAAO,CAAC,CAAC;QACX;MACF,CAAC,CAAC,OAAOuC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7C,IAAIE,YAAY,GAAGZ,CAAC,CAAC,uBAAuB,CAAC;;QAE7C;QACA,IAAIU,KAAK,YAAYG,KAAK,EAAE;UAC1B,MAAMC,SAAS,GAAIJ,KAAK,CAASK,IAAI;UACrC,QAAQD,SAAS;YACf,KAAK,qBAAqB;YAC1B,KAAK,qBAAqB;cACxBF,YAAY,GAAGZ,CAAC,CAAC,+BAA+B,CAAC;cACjD;YACF,KAAK,2BAA2B;cAC9BY,YAAY,GAAGZ,CAAC,CAAC,uBAAuB,CAAC;cACzC;YACF,KAAK,oBAAoB;cACvBY,YAAY,GAAGZ,CAAC,CAAC,yBAAyB,CAAC;cAC3C;YACF,KAAK,oBAAoB;cACvBY,YAAY,GAAGZ,CAAC,CAAC,yBAAyB,CAAC;cAC3C;YACF,KAAK,wBAAwB;cAC3BY,YAAY,GAAGZ,CAAC,CAAC,4BAA4B,CAAC;cAC9C;YACF;cACEY,YAAY,GAAGF,KAAK,CAACM,OAAO;UAChC;QACF;QAEAtB,eAAe,CAACkB,YAAY,CAAC;MAC/B,CAAC,SAAS;QACRhB,YAAY,CAAC,KAAK,CAAC;MACrB;IACF;EACF,CAAC;;EAED;EACA,MAAMqB,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpCrB,YAAY,CAAC,IAAI,CAAC;IAClB,IAAI;MACF,MAAMG,eAAe,CAAC,CAAC;MACvB5B,OAAO,CAAC,CAAC;IACX,CAAC,CAAC,OAAOuC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3ChB,eAAe,CAACM,CAAC,CAAC,8BAA8B,CAAC,CAAC;IACpD,CAAC,SAAS;MACRJ,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAMsB,gBAAgB,GAAGA,CAAA,KAAM;IAC7Bd,WAAW,CAAC,CAAC;IACbpB,UAAU,CAAC,CAACD,OAAO,CAAC;EACtB,CAAC;;EAED;EACA,MAAMoC,MAAM,GAAGxD,cAAc,CAACO,QAAQ,CAAC;EAEvC,oBACEJ,OAAA;IAAKsD,KAAK,EAAED,MAAM,CAACE,OAAQ;IAAAC,QAAA,eACzBxD,OAAA;MAAKsD,KAAK,EAAED,MAAM,CAACI,cAAe;MAAAD,QAAA,gBAChCxD,OAAA;QAAKsD,KAAK,EAAED,MAAM,CAACK,WAAY;QAAAF,QAAA,gBAC7BxD,OAAA;UAAIsD,KAAK,EAAED,MAAM,CAACM,UAAW;UAAAH,QAAA,EAC1BvC,OAAO,GAAGiB,CAAC,CAAC,kBAAkB,CAAC,GAAGA,CAAC,CAAC,mBAAmB;QAAC;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC,eACL/D,OAAA;UAAQsD,KAAK,EAAED,MAAM,CAACW,WAAY;UAACC,OAAO,EAAE5D,OAAQ;UAAAmD,QAAA,eAClDxD,OAAA,CAACN,CAAC;YAACwE,IAAI,EAAE;UAAG;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEN/D,OAAA;QAAKsD,KAAK,EAAED,MAAM,CAACc,SAAU;QAAAX,QAAA,GAC1B7B,YAAY,iBACX3B,OAAA;UAAKsD,KAAK,EAAED,MAAM,CAACe,UAAW;UAAAZ,QAAA,gBAC5BxD,OAAA,CAACL,WAAW;YAACuE,IAAI,EAAE;UAAG;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzB/D,OAAA;YAAAwD,QAAA,EAAO7B;UAAY;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CACN,eAED/D,OAAA;UAAMqE,QAAQ,EAAE9B,YAAa;UAACe,KAAK,EAAED,MAAM,CAACiB,aAAc;UAAAd,QAAA,GACvD,CAACvC,OAAO,iBACPjB,OAAA;YAAKsD,KAAK,EAAED,MAAM,CAACkB,UAAW;YAAAf,QAAA,gBAC5BxD,OAAA;cAAOsD,KAAK,EAAED,MAAM,CAACmB,KAAM;cAACC,OAAO,EAAC,MAAM;cAAAjB,QAAA,EAAEtB,CAAC,CAAC,iBAAiB;YAAC;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACzE/D,OAAA;cACE0E,EAAE,EAAC,MAAM;cACTpB,KAAK,EAAE;gBACL,GAAGD,MAAM,CAACsB,KAAK;gBACfC,WAAW,EAAEnD,SAAS,GAAG,SAAS,GAAG4B,MAAM,CAACsB,KAAK,CAACC;cACpD,CAAE;cACFC,IAAI,EAAC,MAAM;cACXC,KAAK,EAAEjE,IAAK;cACZkE,QAAQ,EAAGvC,CAAC,IAAK1B,OAAO,CAAC0B,CAAC,CAACwC,MAAM,CAACF,KAAK,CAAE;cACzCG,WAAW,EAAE/C,CAAC,CAAC,uBAAuB;YAAE;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC,EACDtC,SAAS,iBAAIzB,OAAA;cAAKsD,KAAK,EAAED,MAAM,CAAC6B,SAAU;cAAA1B,QAAA,EAAE/B;YAAS;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CACN,eAED/D,OAAA;YAAKsD,KAAK,EAAED,MAAM,CAACkB,UAAW;YAAAf,QAAA,gBAC5BxD,OAAA;cAAOsD,KAAK,EAAED,MAAM,CAACmB,KAAM;cAACC,OAAO,EAAC,OAAO;cAAAjB,QAAA,EAAEtB,CAAC,CAAC,kBAAkB;YAAC;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC3E/D,OAAA;cACE0E,EAAE,EAAC,OAAO;cACVpB,KAAK,EAAE;gBACL,GAAGD,MAAM,CAACsB,KAAK;gBACfC,WAAW,EAAEzD,UAAU,GAAG,SAAS,GAAGkC,MAAM,CAACsB,KAAK,CAACC;cACrD,CAAE;cACFC,IAAI,EAAC,OAAO;cACZC,KAAK,EAAEvE,KAAM;cACbwE,QAAQ,EAAGvC,CAAC,IAAKhC,QAAQ,CAACgC,CAAC,CAACwC,MAAM,CAACF,KAAK,CAAE;cAC1CG,WAAW,EAAE/C,CAAC,CAAC,wBAAwB;YAAE;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC,EACD5C,UAAU,iBAAInB,OAAA;cAAKsD,KAAK,EAAED,MAAM,CAAC6B,SAAU;cAAA1B,QAAA,EAAErC;YAAU;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,eAEN/D,OAAA;YAAKsD,KAAK,EAAED,MAAM,CAACkB,UAAW;YAAAf,QAAA,gBAC5BxD,OAAA;cAAOsD,KAAK,EAAED,MAAM,CAACmB,KAAM;cAACC,OAAO,EAAC,UAAU;cAAAjB,QAAA,EAAEtB,CAAC,CAAC,qBAAqB;YAAC;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACjF/D,OAAA;cACE0E,EAAE,EAAC,UAAU;cACbpB,KAAK,EAAE;gBACL,GAAGD,MAAM,CAACsB,KAAK;gBACfC,WAAW,EAAEvD,aAAa,GAAG,SAAS,GAAGgC,MAAM,CAACsB,KAAK,CAACC;cACxD,CAAE;cACFC,IAAI,EAAC,UAAU;cACfC,KAAK,EAAErE,QAAS;cAChBsE,QAAQ,EAAGvC,CAAC,IAAK9B,WAAW,CAAC8B,CAAC,CAACwC,MAAM,CAACF,KAAK,CAAE;cAC7CG,WAAW,EAAEhE,OAAO,GAAG,UAAU,GAAGiB,CAAC,CAAC,2BAA2B;YAAE;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpE,CAAC,EACD1C,aAAa,iBAAIrB,OAAA;cAAKsD,KAAK,EAAED,MAAM,CAAC6B,SAAU;cAAA1B,QAAA,EAAEnC;YAAa;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE,CAAC,EAEL,CAAC9C,OAAO,iBACPjB,OAAA;YAAKsD,KAAK,EAAED,MAAM,CAACkB,UAAW;YAAAf,QAAA,gBAC5BxD,OAAA;cAAOsD,KAAK,EAAED,MAAM,CAACmB,KAAM;cAACC,OAAO,EAAC,kBAAkB;cAAAjB,QAAA,EAAEtB,CAAC,CAAC,4BAA4B;YAAC;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAChG/D,OAAA;cACE0E,EAAE,EAAC,kBAAkB;cACrBpB,KAAK,EAAE;gBACL,GAAGD,MAAM,CAACsB,KAAK;gBACfC,WAAW,EAAErD,oBAAoB,GAAG,SAAS,GAAG8B,MAAM,CAACsB,KAAK,CAACC;cAC/D,CAAE;cACFC,IAAI,EAAC,UAAU;cACfC,KAAK,EAAEnE,eAAgB;cACvBoE,QAAQ,EAAGvC,CAAC,IAAK5B,kBAAkB,CAAC4B,CAAC,CAACwC,MAAM,CAACF,KAAK,CAAE;cACpDG,WAAW,EAAE/C,CAAC,CAAC,kCAAkC;YAAE;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,EACDxC,oBAAoB,iBAAIvB,OAAA;cAAKsD,KAAK,EAAED,MAAM,CAAC6B,SAAU;cAAA1B,QAAA,EAAEjC;YAAoB;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChF,CACN,EAEA9C,OAAO,iBACNjB,OAAA,CAAAE,SAAA;YAAAsD,QAAA,gBACExD,OAAA;cAAKsD,KAAK,EAAED,MAAM,CAAC8B,QAAS;cAAA3B,QAAA,gBAC1BxD,OAAA;gBACEsD,KAAK,EAAED,MAAM,CAAC+B,aAAc;gBAC5BP,IAAI,EAAC,UAAU;gBACfH,EAAE,EAAC,aAAa;gBAChBW,OAAO,EAAEtE,UAAW;gBACpBgE,QAAQ,EAAGvC,CAAC,IAAKxB,aAAa,CAACwB,CAAC,CAACwC,MAAM,CAACK,OAAO;cAAE;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC,eACF/D,OAAA;gBAAOyE,OAAO,EAAC,aAAa;gBAACnB,KAAK,EAAED,MAAM,CAACiC,aAAc;gBAAA9B,QAAA,EACtDtB,CAAC,CAAC,iBAAiB;cAAC;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAEN/D,OAAA;cAAKsD,KAAK,EAAED,MAAM,CAACkC,cAAe;cAAA/B,QAAA,eAChCxD,OAAA;gBAAGwF,IAAI,EAAC,GAAG;gBAAClC,KAAK,EAAED,MAAM,CAACoC,UAAW;gBAAAjC,QAAA,EAAEtB,CAAC,CAAC,qBAAqB;cAAC;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjE,CAAC;UAAA,eACN,CACH,EAEA,CAAC9C,OAAO,iBACPjB,OAAA;YAAKsD,KAAK,EAAED,MAAM,CAAC8B,QAAS;YAAA3B,QAAA,gBAC1BxD,OAAA;cACEsD,KAAK,EAAED,MAAM,CAAC+B,aAAc;cAC5BP,IAAI,EAAC,UAAU;cACfH,EAAE,EAAC,OAAO;cACVW,OAAO,EAAEtE,UAAW;cACpBgE,QAAQ,EAAGvC,CAAC,IAAKxB,aAAa,CAACwB,CAAC,CAACwC,MAAM,CAACK,OAAO;YAAE;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,eACF/D,OAAA;cAAOyE,OAAO,EAAC,OAAO;cAACnB,KAAK,EAAED,MAAM,CAACiC,aAAc;cAAA9B,QAAA,EAChDtB,CAAC,CAAC,kBAAkB;YAAC;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CACN,eAED/D,OAAA;YACE6E,IAAI,EAAC,QAAQ;YACbvB,KAAK,EAAE;cACL,GAAGD,MAAM,CAACqC,YAAY;cACtBC,OAAO,EAAE9D,SAAS,GAAG,GAAG,GAAG,CAAC;cAC5B+D,MAAM,EAAE/D,SAAS,GAAG,aAAa,GAAG;YACtC,CAAE;YACFgE,QAAQ,EAAEhE,SAAU;YAAA2B,QAAA,EAEnB3B,SAAS,GACPZ,OAAO,GAAG,eAAe,GAAG,qBAAqB,GACjDA,OAAO,GAAG,QAAQ,GAAG;UAAiB;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEP/D,OAAA;UAAKsD,KAAK,EAAED,MAAM,CAACyC,OAAQ;UAAAtC,QAAA,gBACzBxD,OAAA;YAAKsD,KAAK,EAAED,MAAM,CAAC0C;UAAY;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACtC/D,OAAA;YAAMsD,KAAK,EAAED,MAAM,CAAC2C,WAAY;YAAAxC,QAAA,EAAC;UAAE;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1C/D,OAAA;YAAKsD,KAAK,EAAED,MAAM,CAAC0C;UAAY;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eAEN/D,OAAA;UAAKsD,KAAK,EAAED,MAAM,CAAC4C,WAAY;UAAAzC,QAAA,eAC7BxD,OAAA;YACE6E,IAAI,EAAC,QAAQ;YACbvB,KAAK,EAAE;cACL,GAAGD,MAAM,CAAC6C,YAAY;cACtBP,OAAO,EAAE9D,SAAS,GAAG,GAAG,GAAG,CAAC;cAC5B+D,MAAM,EAAE/D,SAAS,GAAG,aAAa,GAAG;YACtC,CAAE;YACFoC,OAAO,EAAEd,iBAAkB;YAC3B0C,QAAQ,EAAEhE,SAAU;YAAA2B,QAAA,gBAEpBxD,OAAA;cAAKmG,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,MAAM;cAACC,KAAK,EAAC,4BAA4B;cAAA/C,QAAA,gBAC5FxD,OAAA;gBAAMwG,CAAC,EAAC,yHAAyH;gBAACF,IAAI,EAAC;cAAS;gBAAA1C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC,eAClJ/D,OAAA;gBAAMwG,CAAC,EAAC,uIAAuI;gBAACF,IAAI,EAAC;cAAS;gBAAA1C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC,eAChK/D,OAAA;gBAAMwG,CAAC,EAAC,wHAAwH;gBAACF,IAAI,EAAC;cAAS;gBAAA1C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC,eACjJ/D,OAAA;gBAAMwG,CAAC,EAAC,qIAAqI;gBAACF,IAAI,EAAC;cAAS;gBAAA1C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3J,CAAC,wBAER;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN/D,OAAA;UAAKsD,KAAK,EAAED,MAAM,CAACoD,UAAW;UAAAjD,QAAA,GAC3BvC,OAAO,GAAG,wBAAwB,GAAG,0BAA0B,eAChEjB,OAAA;YAAQsD,KAAK,EAAED,MAAM,CAACqD,YAAa;YAACzC,OAAO,EAAEb,gBAAiB;YAAAI,QAAA,EAC3DvC,OAAO,GAAG,SAAS,GAAG;UAAQ;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzD,EAAA,CApUIH,KAA2B;EAAA,QAiBYP,OAAO,EACpCE,WAAW;AAAA;AAAA6G,EAAA,GAlBrBxG,KAA2B;AAsUjC,eAAeA,KAAK;AAAC,IAAAwG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}