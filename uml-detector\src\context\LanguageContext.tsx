import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

// Types pour les langues supportées
export type Language = 'fr' | 'en';

// Interface pour le contexte de langue
interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string) => string;
}

// Traductions
const translations = {
  fr: {
    // Header
    'app.title': 'AI UML Class Analyzer',
    'button.login': 'Connexion',
    
    // Tabs
    'tab.upload': 'Import d\'image',
    'tab.relations': 'Analyse de relations',
    'tab.text': 'Extraction de diagramme',
    'tab.documentation': 'Documentation',
    
    // Content headers
    'content.upload.title': 'Import de diagramme UML',
    'content.upload.subtitle': 'Téléchargez et analysez vos diagrammes de classes UML avec notre IA avancée',
    'content.relations.title': 'Analyse de relations UML',
    'content.relations.subtitle': 'Visualisez les relations détectées entre les classes de votre diagramme',
    'content.text.title': 'Extraction de diagramme',
    'content.text.subtitle': 'Extrayez et exportez des diagrammes UML',
    'content.documentation.title': 'Documentation & Ressources',
    'content.documentation.subtitle': 'Découvrez les fonctionnalités de UML Class Analyzer',
    
    // Footer
    'footer.text': '© 2025 UML Class Analyzer | Édition Enterprise v2.5.3',
    
    // Language button
    'language.current': 'FR',
    'language.switch': 'Passer en anglais'
  },
  en: {
    // Header
    'app.title': 'AI UML Class Analyzer',
    'button.login': 'Login',
    
    // Tabs
    'tab.upload': 'Image Import',
    'tab.relations': 'Relations Analysis',
    'tab.text': 'Diagram Extraction',
    'tab.documentation': 'Documentation',
    
    // Content headers
    'content.upload.title': 'UML Diagram Import',
    'content.upload.subtitle': 'Upload and analyze your UML class diagrams with our advanced AI',
    'content.relations.title': 'UML Relations Analysis',
    'content.relations.subtitle': 'Visualize detected relationships between classes in your diagram',
    'content.text.title': 'Diagram Extraction',
    'content.text.subtitle': 'Extract and export UML diagrams',
    'content.documentation.title': 'Documentation & Resources',
    'content.documentation.subtitle': 'Discover UML Class Analyzer features',
    
    // Footer
    'footer.text': '© 2025 UML Class Analyzer | Enterprise Edition v2.5.3',
    
    // Language button
    'language.current': 'EN',
    'language.switch': 'Switch to French'
  }
};

// Création du contexte
const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

// Provider du contexte
interface LanguageProviderProps {
  children: ReactNode;
}

export const LanguageProvider: React.FC<LanguageProviderProps> = ({ children }) => {
  const [language, setLanguageState] = useState<Language>('fr');

  // Charger la langue depuis le localStorage au démarrage
  useEffect(() => {
    const savedLanguage = localStorage.getItem('app-language') as Language;
    if (savedLanguage && (savedLanguage === 'fr' || savedLanguage === 'en')) {
      setLanguageState(savedLanguage);
    }
  }, []);

  // Sauvegarder la langue dans le localStorage
  const setLanguage = (lang: Language) => {
    setLanguageState(lang);
    localStorage.setItem('app-language', lang);
  };

  // Fonction de traduction
  const t = (key: string): string => {
    return translations[language][key as keyof typeof translations[typeof language]] || key;
  };

  const value: LanguageContextType = {
    language,
    setLanguage,
    t
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};

// Hook pour utiliser le contexte
export const useLanguage = (): LanguageContextType => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};
