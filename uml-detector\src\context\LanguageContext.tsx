import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

// Types pour les langues supportées
export type Language = 'fr' | 'en';

// Interface pour le contexte de langue
interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string) => string;
}

// Traductions
const translations = {
  fr: {
    // Header
    'app.title': 'AI UML Class Analyzer',
    'button.login': 'Connexion',

    // Tabs
    'tab.upload': 'Import d\'image',
    'tab.relations': 'Analyse de relations',
    'tab.text': 'Extraction de diagramme',
    'tab.documentation': 'Documentation',

    // Content headers
    'content.upload.title': 'Import de diagramme UML',
    'content.upload.subtitle': 'Téléchargez et analysez vos diagrammes de classes UML avec notre IA avancée',
    'content.relations.title': 'Analyse de relations UML',
    'content.relations.subtitle': 'Visualisez les relations détectées entre les classes de votre diagramme',
    'content.text.title': 'Extraction de diagramme',
    'content.text.subtitle': 'Extrayez et exportez des diagrammes UML',
    'content.documentation.title': 'Documentation & Ressources',
    'content.documentation.subtitle': 'Découvrez les fonctionnalités de UML Class Analyzer',

    // Footer
    'footer.text': '© 2025 UML Class Analyzer | Édition Enterprise v2.5.3',

    // Language button
    'language.current': 'FR',
    'language.switch': 'Passer en anglais',

    // ImageUploader
    'imageUploader.dropZone': 'Glissez-déposez votre diagramme UML ou cliquez pour sélectionner',
    'imageUploader.selectFile': 'Sélectionner un fichier',
    'imageUploader.analyzing': 'Analyse en cours...',
    'imageUploader.progress.waiting': 'En attente de l\'image...',
    'imageUploader.progress.converting': 'Conversion et optimisation de l\'image...',
    'imageUploader.progress.detecting': 'Détection des classes et relations avec notre modèle IA...',
    'imageUploader.progress.analyzing': 'Analyse en cours ... Cette étape peut prendre quelques instants.',
    'imageUploader.progress.fetching': 'Récupération des résultats...',
    'imageUploader.progress.completed': 'Analyse terminée',
    'imageUploader.progress.error': 'Erreur pendant l\'analyse de l\'image',
    'imageUploader.progress.fetchError': 'Erreur lors de la récupération des résultats',
    'imageUploader.error.processing': 'Erreur lors du traitement de l\'image',
    'imageUploader.error.connection': 'Erreur de connexion ou analyse',
    'imageUploader.error.fetchText': 'Impossible de récupérer le texte extrait',
    'imageUploader.error.thumbnail': 'Miniature non disponible',
    'imageUploader.button.newAnalysis': 'Nouvelle analyse',
    'imageUploader.button.viewRelations': 'Voir les relations détectées',
    'imageUploader.button.extractDiagram': 'Extraire le diagramme',

    // DetectionArrow
    'detectionArrow.title': 'Détection de Relations UML',
    'detectionArrow.loading': 'Chargement de l\'image annotée...',
    'detectionArrow.error': 'Impossible de charger l\'image annotée. Veuillez vérifier que le serveur est en cours d\'exécution.',
    'detectionArrow.noImage': 'Aucune image annotée n\'est disponible. Veuillez d\'abord télécharger et analyser une image de diagramme UML.',
    'detectionArrow.imageAlt': 'Diagramme UML annoté avec relations détectées',
    'detectionArrow.legend.class': 'Classe',
    'detectionArrow.legend.relation': 'Relation',
    'detectionArrow.legend.aggregation': 'Agrégation',
    'detectionArrow.legend.composition': 'Composition',
    'detectionArrow.legend.generalization': 'Généralisation',
    'detectionArrow.legend.association': 'Association',
    'detectionArrow.tip.title': 'Astuce',
    'detectionArrow.tip.text': 'Pour une analyse plus détaillée, consultez l\'onglet "Extraction de texte UML" qui contient les informations textuelles complètes de toutes les classes détectées.',

    // UMLDiagrameExtractor
    'umlExtractor.title': 'Extracteur de Diagramme UML',
    'umlExtractor.format.diagram': 'Code de diagramme',
    'umlExtractor.format.java': 'Code Java',
    'umlExtractor.format.python': 'Code Python',
    'umlExtractor.placeholder.diagram': 'Entrez votre code diagramme ici...',
    'umlExtractor.placeholder.java': 'Entrez votre code Java ici...',
    'umlExtractor.placeholder.python': 'Entrez votre code Python ici...',
    'umlExtractor.preview.empty': 'Entrez votre code {format} pour le visualiser ici',
    'umlExtractor.error.syntax': 'Erreur de syntaxe:',
    'umlExtractor.button.copy': 'Copier',
    'umlExtractor.button.export': 'Exporter',
    'umlExtractor.button.fullscreen': 'Plein écran',
    'umlExtractor.button.zoomIn': 'Zoom +',
    'umlExtractor.button.zoomOut': 'Zoom -',
    'umlExtractor.button.resetZoom': 'Réinitialiser zoom',
    'umlExtractor.copied': 'Copié !',

    // HistorySidebar
    'history.title': 'Historique',
    'history.newProject': 'Nouveau projet',
    'history.empty': 'Aucun historique disponible',
    'history.loading': 'Chargement de l\'historique...',
    'history.error': 'Erreur lors du chargement de l\'historique',
    'history.deleteConfirm': 'Êtes-vous sûr de vouloir supprimer cet élément ?',
    'history.delete': 'Supprimer',
    'history.cancel': 'Annuler',

    // Login/Auth
    'auth.login': 'Connexion',
    'auth.signup': 'Inscription',
    'auth.email': 'Email',
    'auth.password': 'Mot de passe',
    'auth.confirmPassword': 'Confirmer le mot de passe',
    'auth.forgotPassword': 'Mot de passe oublié ?',
    'auth.loginButton': 'Se connecter',
    'auth.signupButton': 'S\'inscrire',
    'auth.logout': 'Déconnexion',
    'auth.profile': 'Profil',
    'auth.welcome': 'Bienvenue',
    'auth.error.invalidCredentials': 'Identifiants invalides',
    'auth.error.emailExists': 'Cet email existe déjà',
    'auth.error.passwordMismatch': 'Les mots de passe ne correspondent pas',
    'auth.error.weakPassword': 'Le mot de passe est trop faible',
    'auth.success.login': 'Connexion réussie',
    'auth.success.signup': 'Inscription réussie',
    'auth.success.logout': 'Déconnexion réussie',

    // Analysis Results
    'analysis.title': 'Résultats de l\'analyse',
    'analysis.classes': 'Classes détectées',
    'analysis.relations': 'Relations détectées',
    'analysis.attributes': 'Attributs',
    'analysis.methods': 'Méthodes',
    'analysis.noClasses': 'Aucune classe détectée',
    'analysis.noRelations': 'Aucune relation détectée',
    'analysis.export': 'Exporter les résultats',
    'analysis.download': 'Télécharger',
    'analysis.copy': 'Copier',
    'analysis.edit': 'Modifier',
    'analysis.save': 'Sauvegarder',

    // Entity Popup
    'entity.title': 'Détails de l\'entité',
    'entity.className': 'Nom de la classe',
    'entity.attributes': 'Attributs',
    'entity.methods': 'Méthodes',
    'entity.relations': 'Relations',
    'entity.close': 'Fermer',
    'entity.edit': 'Modifier',
    'entity.save': 'Sauvegarder',
    'entity.cancel': 'Annuler',

    // Conflict Resolution
    'conflict.title': 'Résolution de conflits',
    'conflict.description': 'Des conflits ont été détectés lors de l\'importation',
    'conflict.keepExisting': 'Conserver l\'existant',
    'conflict.useNew': 'Utiliser le nouveau',
    'conflict.merge': 'Fusionner',
    'conflict.resolve': 'Résoudre',
    'conflict.resolveAll': 'Résoudre tout',

    // Documentation
    'doc.title': 'Documentation',
    'doc.gettingStarted': 'Commencer',
    'doc.features': 'Fonctionnalités',
    'doc.tutorials': 'Tutoriels',
    'doc.faq': 'FAQ',
    'doc.support': 'Support',
    'doc.version': 'Version',
    'doc.lastUpdate': 'Dernière mise à jour',

    // Common buttons and actions
    'common.ok': 'OK',
    'common.cancel': 'Annuler',
    'common.save': 'Sauvegarder',
    'common.delete': 'Supprimer',
    'common.edit': 'Modifier',
    'common.close': 'Fermer',
    'common.back': 'Retour',
    'common.next': 'Suivant',
    'common.previous': 'Précédent',
    'common.loading': 'Chargement...',
    'common.error': 'Erreur',
    'common.success': 'Succès',
    'common.warning': 'Attention',
    'common.info': 'Information',
  },
  en: {
    // Header
    'app.title': 'AI UML Class Analyzer',
    'button.login': 'Login',

    // Tabs
    'tab.upload': 'Image Import',
    'tab.relations': 'Relations Analysis',
    'tab.text': 'Diagram Extraction',
    'tab.documentation': 'Documentation',

    // Content headers
    'content.upload.title': 'UML Diagram Import',
    'content.upload.subtitle': 'Upload and analyze your UML class diagrams with our advanced AI',
    'content.relations.title': 'UML Relations Analysis',
    'content.relations.subtitle': 'Visualize detected relationships between classes in your diagram',
    'content.text.title': 'Diagram Extraction',
    'content.text.subtitle': 'Extract and export UML diagrams',
    'content.documentation.title': 'Documentation & Resources',
    'content.documentation.subtitle': 'Discover UML Class Analyzer features',

    // Footer
    'footer.text': '© 2025 UML Class Analyzer | Enterprise Edition v2.5.3',

    // Language button
    'language.current': 'EN',
    'language.switch': 'Switch to French',

    // ImageUploader
    'imageUploader.dropZone': 'Drag and drop your UML diagram or click to select',
    'imageUploader.selectFile': 'Select a file',
    'imageUploader.analyzing': 'Analyzing...',
    'imageUploader.progress.waiting': 'Waiting for image...',
    'imageUploader.progress.converting': 'Converting and optimizing image...',
    'imageUploader.progress.detecting': 'Detecting classes and relationships with our AI model...',
    'imageUploader.progress.analyzing': 'Analysis in progress... This step may take a few moments.',
    'imageUploader.progress.fetching': 'Fetching results...',
    'imageUploader.progress.completed': 'Analysis completed',
    'imageUploader.progress.error': 'Error during image analysis',
    'imageUploader.progress.fetchError': 'Error fetching results',
    'imageUploader.error.processing': 'Error processing image',
    'imageUploader.error.connection': 'Connection or analysis error',
    'imageUploader.error.fetchText': 'Unable to fetch extracted text',
    'imageUploader.error.thumbnail': 'Thumbnail not available',
    'imageUploader.button.newAnalysis': 'New Analysis',
    'imageUploader.button.viewRelations': 'View Detected Relations',
    'imageUploader.button.extractDiagram': 'Extract Diagram',

    // DetectionArrow
    'detectionArrow.title': 'UML Relations Detection',
    'detectionArrow.loading': 'Loading annotated image...',
    'detectionArrow.error': 'Unable to load annotated image. Please check that the server is running.',
    'detectionArrow.noImage': 'No annotated image is available. Please first upload and analyze a UML diagram image.',
    'detectionArrow.imageAlt': 'UML diagram annotated with detected relationships',
    'detectionArrow.legend.class': 'Class',
    'detectionArrow.legend.relation': 'Relation',
    'detectionArrow.legend.aggregation': 'Aggregation',
    'detectionArrow.legend.composition': 'Composition',
    'detectionArrow.legend.generalization': 'Generalization',
    'detectionArrow.legend.association': 'Association',
    'detectionArrow.tip.title': 'Tip',
    'detectionArrow.tip.text': 'For more detailed analysis, check the "UML Text Extraction" tab which contains complete textual information of all detected classes.',

    // UMLDiagrameExtractor
    'umlExtractor.title': 'UML Diagram Extractor',
    'umlExtractor.format.diagram': 'Diagram Code',
    'umlExtractor.format.java': 'Java Code',
    'umlExtractor.format.python': 'Python Code',
    'umlExtractor.placeholder.diagram': 'Enter your diagram code here...',
    'umlExtractor.placeholder.java': 'Enter your Java code here...',
    'umlExtractor.placeholder.python': 'Enter your Python code here...',
    'umlExtractor.preview.empty': 'Enter your {format} code to visualize it here',
    'umlExtractor.error.syntax': 'Syntax error:',
    'umlExtractor.button.copy': 'Copy',
    'umlExtractor.button.export': 'Export',
    'umlExtractor.button.fullscreen': 'Fullscreen',
    'umlExtractor.button.zoomIn': 'Zoom In',
    'umlExtractor.button.zoomOut': 'Zoom Out',
    'umlExtractor.button.resetZoom': 'Reset Zoom',
    'umlExtractor.copied': 'Copied!',

    // HistorySidebar
    'history.title': 'History',
    'history.newProject': 'New Project',
    'history.empty': 'No history available',
    'history.loading': 'Loading history...',
    'history.error': 'Error loading history',
    'history.deleteConfirm': 'Are you sure you want to delete this item?',
    'history.delete': 'Delete',
    'history.cancel': 'Cancel',

    // Login/Auth
    'auth.login': 'Login',
    'auth.signup': 'Sign Up',
    'auth.email': 'Email',
    'auth.password': 'Password',
    'auth.confirmPassword': 'Confirm Password',
    'auth.forgotPassword': 'Forgot Password?',
    'auth.loginButton': 'Log In',
    'auth.signupButton': 'Sign Up',
    'auth.logout': 'Logout',
    'auth.profile': 'Profile',
    'auth.welcome': 'Welcome',
    'auth.error.invalidCredentials': 'Invalid credentials',
    'auth.error.emailExists': 'This email already exists',
    'auth.error.passwordMismatch': 'Passwords do not match',
    'auth.error.weakPassword': 'Password is too weak',
    'auth.success.login': 'Login successful',
    'auth.success.signup': 'Sign up successful',
    'auth.success.logout': 'Logout successful',

    // Analysis Results
    'analysis.title': 'Analysis Results',
    'analysis.classes': 'Detected Classes',
    'analysis.relations': 'Detected Relations',
    'analysis.attributes': 'Attributes',
    'analysis.methods': 'Methods',
    'analysis.noClasses': 'No classes detected',
    'analysis.noRelations': 'No relations detected',
    'analysis.export': 'Export Results',
    'analysis.download': 'Download',
    'analysis.copy': 'Copy',
    'analysis.edit': 'Edit',
    'analysis.save': 'Save',

    // Entity Popup
    'entity.title': 'Entity Details',
    'entity.className': 'Class Name',
    'entity.attributes': 'Attributes',
    'entity.methods': 'Methods',
    'entity.relations': 'Relations',
    'entity.close': 'Close',
    'entity.edit': 'Edit',
    'entity.save': 'Save',
    'entity.cancel': 'Cancel',

    // Conflict Resolution
    'conflict.title': 'Conflict Resolution',
    'conflict.description': 'Conflicts were detected during import',
    'conflict.keepExisting': 'Keep Existing',
    'conflict.useNew': 'Use New',
    'conflict.merge': 'Merge',
    'conflict.resolve': 'Resolve',
    'conflict.resolveAll': 'Resolve All',

    // Documentation
    'doc.title': 'Documentation',
    'doc.gettingStarted': 'Getting Started',
    'doc.features': 'Features',
    'doc.tutorials': 'Tutorials',
    'doc.faq': 'FAQ',
    'doc.support': 'Support',
    'doc.version': 'Version',
    'doc.lastUpdate': 'Last Update',

    // Common buttons and actions
    'common.ok': 'OK',
    'common.cancel': 'Cancel',
    'common.save': 'Save',
    'common.delete': 'Delete',
    'common.edit': 'Edit',
    'common.close': 'Close',
    'common.back': 'Back',
    'common.next': 'Next',
    'common.previous': 'Previous',
    'common.loading': 'Loading...',
    'common.error': 'Error',
    'common.success': 'Success',
    'common.warning': 'Warning',
    'common.info': 'Information',
  }
};

// Création du contexte
const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

// Provider du contexte
interface LanguageProviderProps {
  children: ReactNode;
}

export const LanguageProvider: React.FC<LanguageProviderProps> = ({ children }) => {
  const [language, setLanguageState] = useState<Language>('fr');

  // Charger la langue depuis le localStorage au démarrage
  useEffect(() => {
    const savedLanguage = localStorage.getItem('app-language') as Language;
    if (savedLanguage && (savedLanguage === 'fr' || savedLanguage === 'en')) {
      setLanguageState(savedLanguage);
    }
  }, []);

  // Sauvegarder la langue dans le localStorage
  const setLanguage = (lang: Language) => {
    setLanguageState(lang);
    localStorage.setItem('app-language', lang);
  };

  // Fonction de traduction
  const t = (key: string): string => {
    return translations[language][key as keyof typeof translations[typeof language]] || key;
  };

  const value: LanguageContextType = {
    language,
    setLanguage,
    t
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};

// Hook pour utiliser le contexte
export const useLanguage = (): LanguageContextType => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};
