import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

// Types pour les langues supportées
export type Language = 'fr' | 'en';

// Interface pour le contexte de langue
interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string) => string;
}

// Traductions
const translations = {
  fr: {
    // Header
    'app.title': 'AI UML Class Analyzer',
    'button.login': 'Connexion',

    // Tabs
    'tab.upload': 'Import d\'image',
    'tab.relations': 'Analyse de relations',
    'tab.text': 'Extraction de diagramme',
    'tab.documentation': 'Documentation',

    // Content headers
    'content.upload.title': 'Import de diagramme UML',
    'content.upload.subtitle': 'Téléchargez et analysez vos diagrammes de classes UML avec notre IA avancée',
    'content.relations.title': 'Analyse de relations UML',
    'content.relations.subtitle': 'Visualisez les relations détectées entre les classes de votre diagramme',
    'content.text.title': 'Extraction de diagramme',
    'content.text.subtitle': 'Extrayez et exportez des diagrammes UML',
    'content.documentation.title': 'Documentation & Ressources',
    'content.documentation.subtitle': 'Découvrez les fonctionnalités de UML Class Analyzer',

    // Footer
    'footer.text': '© 2025 UML Class Analyzer | Édition Enterprise v2.5.3',

    // Language button
    'language.current': 'FR',
    'language.switch': 'Passer en anglais',

    // ImageUploader
    'imageUploader.dropZone': 'Glissez-déposez votre diagramme UML ou cliquez pour sélectionner',
    'imageUploader.selectFile': 'Sélectionner un fichier',
    'imageUploader.analyzing': 'Analyse en cours...',
    'imageUploader.progress.waiting': 'En attente de l\'image...',
    'imageUploader.progress.converting': 'Conversion et optimisation de l\'image...',
    'imageUploader.progress.detecting': 'Détection des classes et relations avec notre modèle IA...',
    'imageUploader.progress.analyzing': 'Analyse en cours ... Cette étape peut prendre quelques instants.',
    'imageUploader.progress.fetching': 'Récupération des résultats...',
    'imageUploader.progress.completed': 'Analyse terminée',
    'imageUploader.progress.error': 'Erreur pendant l\'analyse de l\'image',
    'imageUploader.progress.fetchError': 'Erreur lors de la récupération des résultats',
    'imageUploader.error.processing': 'Erreur lors du traitement de l\'image',
    'imageUploader.error.connection': 'Erreur de connexion ou analyse',
    'imageUploader.error.fetchText': 'Impossible de récupérer le texte extrait',
    'imageUploader.error.thumbnail': 'Miniature non disponible',
    'imageUploader.button.newAnalysis': 'Nouvelle analyse',
    'imageUploader.button.viewRelations': 'Voir les relations détectées',
    'imageUploader.button.extractDiagram': 'Extraire le diagramme',

    // DetectionArrow
    'detectionArrow.title': 'Détection de Relations UML',
    'detectionArrow.loading': 'Chargement de l\'image annotée...',
    'detectionArrow.error': 'Impossible de charger l\'image annotée. Veuillez vérifier que le serveur est en cours d\'exécution.',
    'detectionArrow.noImage': 'Aucune image annotée n\'est disponible. Veuillez d\'abord télécharger et analyser une image de diagramme UML.',
    'detectionArrow.imageAlt': 'Diagramme UML annoté avec relations détectées',
    'detectionArrow.analysisTitle': 'Analyse des Relations',
    'detectionArrow.explanation': 'L\'image ci-dessus montre les relations détectées entre les différentes classes de votre diagramme UML. Notre système d\'IA a identifié les connexions et les a marquées selon leur type.',
    'detectionArrow.legend.class': 'Classe',
    'detectionArrow.legend.relation': 'Relation',
    'detectionArrow.legend.aggregation': 'Agrégation',
    'detectionArrow.legend.composition': 'Composition',
    'detectionArrow.legend.generalization': 'Généralisation',
    'detectionArrow.legend.association': 'Association',
    'detectionArrow.tip.title': 'Astuce',
    'detectionArrow.tip.text': 'Pour une analyse plus détaillée, consultez l\'onglet "Extraction de texte UML" qui contient les informations textuelles complètes de toutes les classes détectées.',

    // UMLDiagrameExtractor
    'umlExtractor.title': 'Extracteur de Diagramme UML',
    'umlExtractor.format.diagram': 'Code de diagramme',
    'umlExtractor.format.java': 'Code Java',
    'umlExtractor.format.python': 'Code Python',
    'umlExtractor.placeholder.diagram': 'Entrez votre code diagramme ici...',
    'umlExtractor.placeholder.java': 'Entrez votre code Java ici...',
    'umlExtractor.placeholder.python': 'Entrez votre code Python ici...',
    'umlExtractor.preview.empty': 'Entrez votre code {format} pour le visualiser ici',
    'umlExtractor.error.syntax': 'Erreur de syntaxe:',
    'umlExtractor.button.copy': 'Copier',
    'umlExtractor.button.export': 'Exporter',
    'umlExtractor.button.fullscreen': 'Plein écran',
    'umlExtractor.button.zoomIn': 'Zoom +',
    'umlExtractor.button.zoomOut': 'Zoom -',
    'umlExtractor.button.resetZoom': 'Réinitialiser zoom',
    'umlExtractor.copied': 'Copié !',

    // Authentication
    'auth.login.title': 'Connexion à votre compte',
    'auth.signup.title': 'Créer un compte',
    'auth.field.name': 'Nom',
    'auth.field.email': 'Email',
    'auth.field.password': 'Mot de passe',
    'auth.field.confirmPassword': 'Confirmer le mot de passe',
    'auth.placeholder.name': 'Votre nom',
    'auth.placeholder.email': '<EMAIL>',
    'auth.placeholder.password': 'Créer un mot de passe',
    'auth.placeholder.confirmPassword': 'Confirmez votre mot de passe',
    'auth.rememberMe': 'Se souvenir de moi',
    'auth.forgotPassword': 'Mot de passe oublié ?',
    'auth.acceptTerms': 'J\'accepte les conditions d\'utilisation',
    'auth.button.login': 'Se connecter',
    'auth.button.signup': 'S\'inscrire',
    'auth.button.createAccount': 'Créer un compte',
    'auth.button.loggingIn': 'Connexion...',
    'auth.button.creatingAccount': 'Création du compte...',
    'auth.button.continueWithGoogle': 'Continuer avec Google',
    'auth.button.logout': 'Se déconnecter',
    'auth.or': 'OU',
    'auth.noAccount': 'Vous n\'avez pas de compte ?',
    'auth.haveAccount': 'Vous avez déjà un compte ?',
    'auth.error.emailRequired': 'L\'email est requis',
    'auth.error.emailInvalid': 'Veuillez entrer un email valide',
    'auth.error.passwordRequired': 'Le mot de passe est requis',
    'auth.error.passwordTooShort': 'Le mot de passe doit contenir au moins 6 caractères',
    'auth.error.nameRequired': 'Le nom est requis',
    'auth.error.confirmPasswordRequired': 'Veuillez confirmer votre mot de passe',
    'auth.error.passwordMismatch': 'Les mots de passe ne correspondent pas',
    'auth.error.authFailed': 'Échec de l\'authentification',
    'auth.error.invalidCredentials': 'Email ou mot de passe invalide',
    'auth.error.emailInUse': 'Cet email est déjà utilisé',
    'auth.error.weakPassword': 'Le mot de passe est trop faible',
    'auth.error.tooManyRequests': 'Trop de tentatives échouées. Réessayez plus tard.',
    'auth.error.googleLoginFailed': 'Connexion Google échouée. Veuillez réessayer.',

    // HistorySidebar
    'history.title': 'Historique UML',
    'history.newProject': 'Nouveau projet',
    'history.empty': 'Aucun historique',
    'history.loading': 'Chargement...',
    'history.error': 'Erreur lors du chargement de l\'historique',
    'history.deleteConfirm': 'Êtes-vous sûr de vouloir supprimer cet élément ?',
    'history.deleteAllConfirm': 'Êtes-vous sûr de vouloir supprimer tout votre historique ? Cette action est irréversible.',
    'history.delete': 'Supprimer',
    'history.deleteAll': 'Supprimer tout l\'historique',
    'history.cancel': 'Annuler',
    'history.loginPrompt': 'Connectez-vous pour voir votre historique',
    'history.searchPlaceholder': 'Rechercher...',
    'history.noResults': 'Aucun résultat trouvé',
    'history.thumbnail': 'Miniature',
    'history.reload': 'Recharger',

    // Analysis Results
    'analysis.error.updateFailed': 'Échec de la mise à jour du texte. Veuillez réessayer.',
    'analysis.diagramAlt': 'Diagramme UML',
    'analysis.tooltip.viewAnnotated': 'Cliquez pour voir l\'image annotée',
    'analysis.tooltip.convertDiagram': 'Si votre diagramme est manuscrit et vous souhaitez le convertir vers un diagramme imprimé, cliquez ici',
    'analysis.imageAnalyzed': 'Image analysée',
    'analysis.extractedText': 'Texte extrait',
    'analysis.copied': 'Copié!',
    'analysis.newProject': 'Nouveau Projet',

    // Conflict Resolution Modal
    'conflict.title': 'Résolution des Conflits',
    'conflict.attribute': 'Attribut',
    'conflict.method': 'Méthode',
    'conflict.currentVersion': 'Version Actuelle',
    'conflict.importedVersions': 'Versions Importées',
    'conflict.keepCurrent': 'Garder l\'actuel',
    'conflict.replace': 'Remplacer',
    'conflict.merge': 'Fusionner',
    'conflict.cancel': 'Annuler',
    'conflict.applyResolutions': 'Appliquer les Résolutions',

    // Entity Popup
    'entity.subtitle': 'UML IA propose plusieurs choix possibles pour cette entité.',
    'entity.attributes': 'Attributs',
    'entity.methods': 'Méthodes',
    'entity.noAdditionalAttributes': 'Aucun attribut supplémentaire',
    'entity.noAdditionalMethods': 'Aucune méthode supplémentaire',
    'entity.modifyEntity': 'Modifier l\'entité',
    'entity.cancel': 'Annuler',

    // History Analysis Section
    'historyAnalysis.title': 'Analyse Historique',
    'historyAnalysis.noResults': 'Aucun diagramme historique trouvé pour la classe "{className}"',
    'historyAnalysis.sortBy': 'Trier par:',
    'historyAnalysis.conflictsDetected': 'Conflits détectés: {attributes} attribut(s), {methods} méthode(s)',
    'historyAnalysis.access.owner': 'Votre diagramme',
    'historyAnalysis.access.shared': 'Diagramme partagé',
    'historyAnalysis.access.public': 'Diagramme public',
    'historyAnalysis.access.restricted': 'Accès restreint',

    // Documentation
    'doc.description': 'UML Class Analyzer est une application web avancée qui utilise l\'intelligence artificielle pour analyser automatiquement les diagrammes de classes UML. Notre solution combine la puissance des modèles pour offrir une précision exceptionnelle dans la détection et l\'extraction d\'éléments UML.',
    'doc.features.aiDetection.title': 'Détection IA Avancée',
    'doc.features.aiDetection.description': 'Utilise des modèles entraînés spécifiquement pour reconnaître les éléments UML avec une précision de plus de 95%.',
    'doc.features.fastProcessing.title': 'Traitement Rapide',
    'doc.features.fastProcessing.description': 'Analyse complète d\'un diagramme UML en moins de 30 secondes grâce à notre infrastructure optimisée.',
    'doc.features.multiFormat.title': 'Multi-format',
    'doc.features.multiFormat.description': 'Support complet des formats PNG, JPG, JPEG et PDF avec export pour le moment vers Java et Mermaid.',
    'doc.featuresTitle': 'Fonctionnalités principales',
    'doc.features.uploadAnalyze.title': 'Upload & Analyse',
    'doc.features.uploadAnalyze.description': 'Téléchargez vos diagrammes UML (PNG, JPG, PDF) et obtenez une analyse complète en quelques secondes avec notre IA spécialisée.',
    'doc.features.relationDetection.title': 'Détection de Relations',
    'doc.features.relationDetection.description': 'Identification automatique des relations UML : héritage, composition, agrégation, association avec visualisation annotée.',
    'doc.features.textExtraction.title': 'Extraction de Texte',
    'doc.features.textExtraction.description': 'Extraction intelligente des classes, attributs et méthodes avec organisation automatique et export multi-format.',
    'doc.features.codeGeneration.title': 'Génération de Code',
    'doc.features.codeGeneration.description': 'Conversion automatique vers Java et diagrammes Mermaid avec structure de classes complète.',
    'doc.features.historyManagement.title': 'Historique & Gestion',
    'doc.features.historyManagement.description': 'Sauvegarde automatique des analyses, historique complet avec authentification Firebase et gestion des projets.',
    'doc.features.modernInterface.title': 'Interface Moderne',
    'doc.features.modernInterface.description': 'Interface utilisateur intuitive avec mode sombre/clair, design responsive et expérience utilisateur optimisée.',

    // Guide d'utilisation
    'doc.guideTitle': 'Guide d\'utilisation',
    'doc.guide.step1.title': 'Téléchargement du diagramme',
    'doc.guide.step1.description': 'Cliquez sur "Sélectionner un fichier" ou glissez-déposez votre diagramme UML. Formats supportés : PNG, JPG, JPEG, PDF. Taille maximale : 50MB.',
    'doc.guide.step2.title': 'Analyse automatique',
    'doc.guide.step2.description': 'Notre IA analyse votre diagramme en utilisant plusieurs modèles pour de bons résultats. Le processus prend généralement 15-30 secondes selon la complexité.',
    'doc.guide.step3.title': 'Visualisation des relations',
    'doc.guide.step3.description': 'Consultez l\'onglet "Analyse de relations" pour voir votre diagramme annoté avec toutes les relations détectées (héritage, composition, agrégation, etc.).',
    'doc.guide.step4.title': 'Export et génération de code',
    'doc.guide.step4.description': 'Dans l\'onglet "Extraction de diagramme", exportez vos résultats en Java ou Mermaid. Personnalisez le code généré selon vos besoins.',





    // Documentation
    'doc.title': 'Documentation UML Class Analyzer',
    'doc.subtitle': 'Guide complet pour l\'utilisation de notre plateforme d\'analyse UML alimentée par l\'IA',
    'doc.version': 'v2.5.3 Enterprise',
    'doc.overview': 'Vue d\'ensemble',
    'doc.features': 'Fonctionnalités',
    'doc.guide': 'Guide d\'utilisation',
    'doc.technologies': 'Technologies',
    'doc.api': 'API',
    'doc.faq': 'FAQ',
    'doc.projectOverview': 'Vue d\'ensemble du projet',
    'doc.appTitle': 'UML Class Analyzer - Édition Enterprise v2.5.3',
    'doc.gettingStarted': 'Commencer',
    'doc.tutorials': 'Tutoriels',
    'doc.support': 'Support',
    'doc.lastUpdate': 'Dernière mise à jour',

    // Common buttons and actions
    'common.ok': 'OK',
    'common.cancel': 'Annuler',
    'common.save': 'Sauvegarder',
    'common.delete': 'Supprimer',
    'common.edit': 'Modifier',
    'common.close': 'Fermer',
    'common.back': 'Retour',
    'common.next': 'Suivant',
    'common.previous': 'Précédent',
    'common.loading': 'Chargement...',
    'common.error': 'Erreur',
    'common.success': 'Succès',
    'common.warning': 'Attention',
    'common.info': 'Information',
  },
  en: {
    // Header
    'app.title': 'AI UML Class Analyzer',
    'button.login': 'Login',

    // Tabs
    'tab.upload': 'Image Import',
    'tab.relations': 'Relations Analysis',
    'tab.text': 'Diagram Extraction',
    'tab.documentation': 'Documentation',

    // Content headers
    'content.upload.title': 'UML Diagram Import',
    'content.upload.subtitle': 'Upload and analyze your UML class diagrams with our advanced AI',
    'content.relations.title': 'UML Relations Analysis',
    'content.relations.subtitle': 'Visualize detected relationships between classes in your diagram',
    'content.text.title': 'Diagram Extraction',
    'content.text.subtitle': 'Extract and export UML diagrams',
    'content.documentation.title': 'Documentation & Resources',
    'content.documentation.subtitle': 'Discover UML Class Analyzer features',

    // Footer
    'footer.text': '© 2025 UML Class Analyzer | Enterprise Edition v2.5.3',

    // Language button
    'language.current': 'EN',
    'language.switch': 'Switch to French',

    // ImageUploader
    'imageUploader.dropZone': 'Drag and drop your UML diagram or click to select',
    'imageUploader.selectFile': 'Select a file',
    'imageUploader.analyzing': 'Analyzing...',
    'imageUploader.progress.waiting': 'Waiting for image...',
    'imageUploader.progress.converting': 'Converting and optimizing image...',
    'imageUploader.progress.detecting': 'Detecting classes and relationships with our AI model...',
    'imageUploader.progress.analyzing': 'Analysis in progress... This step may take a few moments.',
    'imageUploader.progress.fetching': 'Fetching results...',
    'imageUploader.progress.completed': 'Analysis completed',
    'imageUploader.progress.error': 'Error during image analysis',
    'imageUploader.progress.fetchError': 'Error fetching results',
    'imageUploader.error.processing': 'Error processing image',
    'imageUploader.error.connection': 'Connection or analysis error',
    'imageUploader.error.fetchText': 'Unable to fetch extracted text',
    'imageUploader.error.thumbnail': 'Thumbnail not available',
    'imageUploader.button.newAnalysis': 'New Analysis',
    'imageUploader.button.viewRelations': 'View Detected Relations',
    'imageUploader.button.extractDiagram': 'Extract Diagram',

    // DetectionArrow
    'detectionArrow.title': 'UML Relations Detection',
    'detectionArrow.loading': 'Loading annotated image...',
    'detectionArrow.error': 'Unable to load annotated image. Please check that the server is running.',
    'detectionArrow.noImage': 'No annotated image is available. Please first upload and analyze a UML diagram image.',
    'detectionArrow.imageAlt': 'UML diagram annotated with detected relationships',
    'detectionArrow.analysisTitle': 'Relations Analysis',
    'detectionArrow.explanation': 'The image above shows the detected relationships between the different classes in your UML diagram. Our AI system has identified the connections and marked them according to their type.',
    'detectionArrow.legend.class': 'Class',
    'detectionArrow.legend.relation': 'Relation',
    'detectionArrow.legend.aggregation': 'Aggregation',
    'detectionArrow.legend.composition': 'Composition',
    'detectionArrow.legend.generalization': 'Generalization',
    'detectionArrow.legend.association': 'Association',
    'detectionArrow.tip.title': 'Tip',
    'detectionArrow.tip.text': 'For more detailed analysis, check the "UML Text Extraction" tab which contains complete textual information of all detected classes.',

    // UMLDiagrameExtractor
    'umlExtractor.title': 'UML Diagram Extractor',
    'umlExtractor.format.diagram': 'Diagram Code',
    'umlExtractor.format.java': 'Java Code',
    'umlExtractor.format.python': 'Python Code',
    'umlExtractor.placeholder.diagram': 'Enter your diagram code here...',
    'umlExtractor.placeholder.java': 'Enter your Java code here...',
    'umlExtractor.placeholder.python': 'Enter your Python code here...',
    'umlExtractor.preview.empty': 'Enter your {format} code to visualize it here',
    'umlExtractor.error.syntax': 'Syntax error:',
    'umlExtractor.button.copy': 'Copy',
    'umlExtractor.button.export': 'Export',
    'umlExtractor.button.fullscreen': 'Fullscreen',
    'umlExtractor.button.zoomIn': 'Zoom In',
    'umlExtractor.button.zoomOut': 'Zoom Out',
    'umlExtractor.button.resetZoom': 'Reset Zoom',
    'umlExtractor.copied': 'Copied!',

    // Authentication
    'auth.login.title': 'Log in to your account',
    'auth.signup.title': 'Create an account',
    'auth.field.name': 'Name',
    'auth.field.email': 'Email',
    'auth.field.password': 'Password',
    'auth.field.confirmPassword': 'Confirm Password',
    'auth.placeholder.name': 'Your name',
    'auth.placeholder.email': '<EMAIL>',
    'auth.placeholder.password': 'Create a password',
    'auth.placeholder.confirmPassword': 'Confirm your password',
    'auth.rememberMe': 'Remember me',
    'auth.forgotPassword': 'Forgot password?',
    'auth.acceptTerms': 'I accept the Terms of Service',
    'auth.button.login': 'Log in',
    'auth.button.signup': 'Sign up',
    'auth.button.createAccount': 'Create account',
    'auth.button.loggingIn': 'Logging in...',
    'auth.button.creatingAccount': 'Creating account...',
    'auth.button.continueWithGoogle': 'Continue with Google',
    'auth.button.logout': 'Log out',
    'auth.or': 'OR',
    'auth.noAccount': 'Don\'t have an account?',
    'auth.haveAccount': 'Already have an account?',
    'auth.error.emailRequired': 'Email is required',
    'auth.error.emailInvalid': 'Please enter a valid email',
    'auth.error.passwordRequired': 'Password is required',
    'auth.error.passwordTooShort': 'Password must be at least 6 characters',
    'auth.error.nameRequired': 'Name is required',
    'auth.error.confirmPasswordRequired': 'Please confirm your password',
    'auth.error.passwordMismatch': 'Passwords do not match',
    'auth.error.authFailed': 'Authentication failed',
    'auth.error.invalidCredentials': 'Invalid email or password',
    'auth.error.emailInUse': 'Email is already in use',
    'auth.error.weakPassword': 'Password is too weak',
    'auth.error.tooManyRequests': 'Too many failed attempts. Try again later.',
    'auth.error.googleLoginFailed': 'Google login failed. Please try again.',

    // HistorySidebar
    'history.title': 'UML History',
    'history.newProject': 'New Project',
    'history.empty': 'No history',
    'history.loading': 'Loading...',
    'history.error': 'Error loading history',
    'history.deleteConfirm': 'Are you sure you want to delete this item?',
    'history.deleteAllConfirm': 'Are you sure you want to delete all your history? This action is irreversible.',
    'history.delete': 'Delete',
    'history.deleteAll': 'Delete all history',
    'history.cancel': 'Cancel',
    'history.loginPrompt': 'Log in to see your history',
    'history.searchPlaceholder': 'Search...',
    'history.noResults': 'No results found',
    'history.thumbnail': 'Thumbnail',
    'history.reload': 'Reload',

    // Analysis Results
    'analysis.error.updateFailed': 'Failed to update text. Please try again.',
    'analysis.diagramAlt': 'UML Diagram',
    'analysis.tooltip.viewAnnotated': 'Click to view annotated image',
    'analysis.tooltip.convertDiagram': 'If your diagram is handwritten and you want to convert it to a printed diagram, click here',
    'analysis.imageAnalyzed': 'Analyzed Image',
    'analysis.extractedText': 'Extracted Text',
    'analysis.copied': 'Copied!',
    'analysis.newProject': 'New Project',

    // Conflict Resolution Modal
    'conflict.title': 'Conflict Resolution',
    'conflict.attribute': 'Attribute',
    'conflict.method': 'Method',
    'conflict.currentVersion': 'Current Version',
    'conflict.importedVersions': 'Imported Versions',
    'conflict.keepCurrent': 'Keep Current',
    'conflict.replace': 'Replace',
    'conflict.merge': 'Merge',
    'conflict.cancel': 'Cancel',
    'conflict.applyResolutions': 'Apply Resolutions',

    // Entity Popup
    'entity.subtitle': 'UML AI suggests several possible choices for this entity.',
    'entity.attributes': 'Attributes',
    'entity.methods': 'Methods',
    'entity.noAdditionalAttributes': 'No additional attributes',
    'entity.noAdditionalMethods': 'No additional methods',
    'entity.modifyEntity': 'Modify Entity',
    'entity.cancel': 'Cancel',

    // History Analysis Section
    'historyAnalysis.title': 'Historical Analysis',
    'historyAnalysis.noResults': 'No historical diagrams found for class "{className}"',
    'historyAnalysis.sortBy': 'Sort by:',
    'historyAnalysis.conflictsDetected': 'Conflicts detected: {attributes} attribute(s), {methods} method(s)',
    'historyAnalysis.access.owner': 'Your diagram',
    'historyAnalysis.access.shared': 'Shared diagram',
    'historyAnalysis.access.public': 'Public diagram',
    'historyAnalysis.access.restricted': 'Restricted access',

    // Documentation
    'doc.description': 'UML Class Analyzer is an advanced web application that uses artificial intelligence to automatically analyze UML class diagrams. Our solution combines the power of models to offer exceptional precision in UML element detection and extraction.',
    'doc.features.aiDetection.title': 'Advanced AI Detection',
    'doc.features.aiDetection.description': 'Uses models specifically trained to recognize UML elements with over 95% accuracy.',
    'doc.features.fastProcessing.title': 'Fast Processing',
    'doc.features.fastProcessing.description': 'Complete analysis of a UML diagram in less than 30 seconds thanks to our optimized infrastructure.',
    'doc.features.multiFormat.title': 'Multi-format',
    'doc.features.multiFormat.description': 'Complete support for PNG, JPG, JPEG and PDF formats with export currently to Java and Mermaid.',
    'doc.featuresTitle': 'Main Features',
    'doc.features.uploadAnalyze.title': 'Upload & Analyze',
    'doc.features.uploadAnalyze.description': 'Upload your UML diagrams (PNG, JPG, PDF) and get complete analysis in seconds with our specialized AI.',
    'doc.features.relationDetection.title': 'Relation Detection',
    'doc.features.relationDetection.description': 'Automatic identification of UML relations: inheritance, composition, aggregation, association with annotated visualization.',
    'doc.features.textExtraction.title': 'Text Extraction',
    'doc.features.textExtraction.description': 'Intelligent extraction of classes, attributes and methods with automatic organization and multi-format export.',
    'doc.features.codeGeneration.title': 'Code Generation',
    'doc.features.codeGeneration.description': 'Automatic conversion to Java and Mermaid diagrams with complete class structure.',
    'doc.features.historyManagement.title': 'History & Management',
    'doc.features.historyManagement.description': 'Automatic analysis backup, complete history with Firebase authentication and project management.',
    'doc.features.modernInterface.title': 'Modern Interface',
    'doc.features.modernInterface.description': 'Intuitive user interface with dark/light mode, responsive design and optimized user experience.',

    // Usage Guide
    'doc.guideTitle': 'Usage Guide',
    'doc.guide.step1.title': 'Diagram Upload',
    'doc.guide.step1.description': 'Click "Select a file" or drag and drop your UML diagram. Supported formats: PNG, JPG, JPEG, PDF. Maximum size: 50MB.',
    'doc.guide.step2.title': 'Automatic Analysis',
    'doc.guide.step2.description': 'Our AI analyzes your diagram using multiple models for good results. The process typically takes 15-30 seconds depending on complexity.',
    'doc.guide.step3.title': 'Relationship Visualization',
    'doc.guide.step3.description': 'Check the "Relationship Analysis" tab to see your annotated diagram with all detected relationships (inheritance, composition, aggregation, etc.).',
    'doc.guide.step4.title': 'Export and Code Generation',
    'doc.guide.step4.description': 'In the "Diagram Extraction" tab, export your results to Java or Mermaid. Customize the generated code according to your needs.',





    // Documentation
    'doc.title': 'UML Class Analyzer Documentation',
    'doc.subtitle': 'Complete guide for using our AI-powered UML analysis platform',
    'doc.version': 'v2.5.3 Enterprise',
    'doc.overview': 'Overview',
    'doc.features': 'Features',
    'doc.guide': 'User Guide',
    'doc.technologies': 'Technologies',
    'doc.api': 'API',
    'doc.faq': 'FAQ',
    'doc.projectOverview': 'Project Overview',
    'doc.appTitle': 'UML Class Analyzer - Enterprise Edition v2.5.3',
    'doc.gettingStarted': 'Getting Started',
    'doc.tutorials': 'Tutorials',
    'doc.support': 'Support',
    'doc.lastUpdate': 'Last Update',

    // Common buttons and actions
    'common.ok': 'OK',
    'common.cancel': 'Cancel',
    'common.save': 'Save',
    'common.delete': 'Delete',
    'common.edit': 'Edit',
    'common.close': 'Close',
    'common.back': 'Back',
    'common.next': 'Next',
    'common.previous': 'Previous',
    'common.loading': 'Loading...',
    'common.error': 'Error',
    'common.success': 'Success',
    'common.warning': 'Warning',
    'common.info': 'Information',
  }
};

// Création du contexte
const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

// Provider du contexte
interface LanguageProviderProps {
  children: ReactNode;
}

export const LanguageProvider: React.FC<LanguageProviderProps> = ({ children }) => {
  const [language, setLanguageState] = useState<Language>('fr');

  // Charger la langue depuis le localStorage au démarrage
  useEffect(() => {
    const savedLanguage = localStorage.getItem('app-language') as Language;
    if (savedLanguage && (savedLanguage === 'fr' || savedLanguage === 'en')) {
      setLanguageState(savedLanguage);
    }
  }, []);

  // Sauvegarder la langue dans le localStorage
  const setLanguage = (lang: Language) => {
    setLanguageState(lang);
    localStorage.setItem('app-language', lang);
  };

  // Fonction de traduction
  const t = (key: string): string => {
    return translations[language][key as keyof typeof translations[typeof language]] || key;
  };

  const value: LanguageContextType = {
    language,
    setLanguage,
    t
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};

// Hook pour utiliser le contexte
export const useLanguage = (): LanguageContextType => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};
