{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\FixTorchUMLDGM\\\\uml-detector\\\\src\\\\components\\\\Loginsignup\\\\UserProfile.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { User, LogOut } from 'lucide-react';\nimport { useAuth } from '../../context/AuthContext';\nimport { useLanguage } from '../../context/LanguageContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UserProfile = ({\n  darkMode\n}) => {\n  _s();\n  var _currentUser$email, _currentUser$email2;\n  const {\n    currentUser,\n    logout\n  } = useAuth();\n  const {\n    t\n  } = useLanguage();\n  const [showDropdown, setShowDropdown] = useState(false);\n  const styles = {\n    container: {\n      position: 'relative',\n      display: 'flex',\n      alignItems: 'center'\n    },\n    userButton: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '0.5rem',\n      padding: '0.5rem',\n      borderRadius: '9999px',\n      border: 'none',\n      backgroundColor: 'transparent',\n      cursor: 'pointer',\n      color: darkMode ? '#e2e8f0' : '#334155',\n      transition: 'background-color 0.2s',\n      ':hover': {\n        backgroundColor: darkMode ? 'rgba(148, 163, 184, 0.1)' : 'rgba(226, 232, 240, 0.5)'\n      }\n    },\n    avatar: {\n      width: '32px',\n      height: '32px',\n      borderRadius: '9999px',\n      objectFit: 'cover',\n      border: `1px solid ${darkMode ? 'rgba(148, 163, 184, 0.2)' : 'rgba(203, 213, 225, 0.8)'}`\n    },\n    placeholder: {\n      width: '32px',\n      height: '32px',\n      borderRadius: '9999px',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      backgroundColor: darkMode ? '#334155' : '#e2e8f0',\n      color: darkMode ? '#e2e8f0' : '#334155'\n    },\n    userName: {\n      fontWeight: 500,\n      fontSize: '0.875rem',\n      marginLeft: '0.5rem',\n      display: {\n        xs: 'none',\n        sm: 'block'\n      }\n    },\n    dropdown: {\n      position: 'absolute',\n      top: '100%',\n      right: 0,\n      marginTop: '0.5rem',\n      width: '200px',\n      backgroundColor: darkMode ? '#1e293b' : '#ffffff',\n      borderRadius: '0.375rem',\n      boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',\n      zIndex: 50,\n      border: `1px solid ${darkMode ? 'rgba(148, 163, 184, 0.2)' : 'rgba(203, 213, 225, 0.8)'}`,\n      overflow: 'hidden'\n    },\n    dropdownHeader: {\n      padding: '1rem',\n      borderBottom: `1px solid ${darkMode ? 'rgba(148, 163, 184, 0.2)' : 'rgba(203, 213, 225, 0.8)'}`,\n      display: 'flex',\n      flexDirection: 'column',\n      alignItems: 'center'\n    },\n    dropdownAvatar: {\n      width: '48px',\n      height: '48px',\n      borderRadius: '9999px',\n      objectFit: 'cover',\n      marginBottom: '0.5rem',\n      border: `1px solid ${darkMode ? 'rgba(148, 163, 184, 0.2)' : 'rgba(203, 213, 225, 0.8)'}`\n    },\n    dropdownUserName: {\n      fontWeight: 600,\n      fontSize: '0.875rem',\n      color: darkMode ? '#e2e8f0' : '#334155'\n    },\n    dropdownEmail: {\n      fontSize: '0.75rem',\n      color: darkMode ? '#94a3b8' : '#64748b',\n      marginTop: '0.25rem'\n    },\n    dropdownItems: {\n      padding: '0.5rem'\n    },\n    dropdownItem: {\n      display: 'flex',\n      alignItems: 'center',\n      padding: '0.75rem',\n      borderRadius: '0.375rem',\n      fontSize: '0.875rem',\n      color: darkMode ? '#e2e8f0' : '#334155',\n      cursor: 'pointer',\n      transition: 'background-color 0.2s',\n      ':hover': {\n        backgroundColor: darkMode ? 'rgba(148, 163, 184, 0.1)' : 'rgba(226, 232, 240, 0.5)'\n      }\n    },\n    logoutButton: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '0.5rem',\n      width: '100%',\n      padding: '0.75rem',\n      borderRadius: '0.375rem',\n      fontSize: '0.875rem',\n      color: darkMode ? '#e2e8f0' : '#334155',\n      backgroundColor: 'transparent',\n      border: 'none',\n      cursor: 'pointer',\n      textAlign: 'left',\n      transition: 'background-color 0.2s'\n    }\n  };\n  const handleLogout = async () => {\n    try {\n      await logout();\n      setShowDropdown(false);\n    } catch (error) {\n      console.error('Logout error:', error);\n    }\n  };\n\n  // Default avatar if no photo URL\n  const defaultAvatar = /*#__PURE__*/_jsxDEV(\"div\", {\n    style: styles.placeholder,\n    children: /*#__PURE__*/_jsxDEV(User, {\n      size: 20\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 144,\n    columnNumber: 5\n  }, this);\n  if (!currentUser) {\n    return null;\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: styles.container,\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      style: styles.userButton,\n      onClick: () => setShowDropdown(!showDropdown),\n      children: [currentUser.photoURL ? /*#__PURE__*/_jsxDEV(\"img\", {\n        src: currentUser.photoURL,\n        alt: currentUser.displayName || 'User',\n        style: styles.avatar\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 11\n      }, this) : defaultAvatar, /*#__PURE__*/_jsxDEV(\"span\", {\n        style: {\n          fontWeight: 500,\n          fontSize: '0.875rem',\n          marginLeft: '0.5rem',\n          display: 'block' // ou 'none'\n        },\n        children: currentUser.displayName || ((_currentUser$email = currentUser.email) === null || _currentUser$email === void 0 ? void 0 : _currentUser$email.split('@')[0])\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 7\n    }, this), showDropdown && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.dropdown,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.dropdownHeader,\n        children: [currentUser.photoURL ? /*#__PURE__*/_jsxDEV(\"img\", {\n          src: currentUser.photoURL,\n          alt: currentUser.displayName || 'User',\n          style: styles.dropdownAvatar\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            ...styles.placeholder,\n            width: '48px',\n            height: '48px'\n          },\n          children: /*#__PURE__*/_jsxDEV(User, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.dropdownUserName,\n          children: currentUser.displayName || ((_currentUser$email2 = currentUser.email) === null || _currentUser$email2 === void 0 ? void 0 : _currentUser$email2.split('@')[0])\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.dropdownEmail,\n          children: currentUser.email\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.dropdownItems,\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          style: styles.logoutButton,\n          onClick: handleLogout,\n          children: [/*#__PURE__*/_jsxDEV(LogOut, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Log out\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 154,\n    columnNumber: 5\n  }, this);\n};\n_s(UserProfile, \"P1yw5zxJz6VWN5EN6/UjAoueXG0=\", false, function () {\n  return [useAuth, useLanguage];\n});\n_c = UserProfile;\nexport default UserProfile;\nvar _c;\n$RefreshReg$(_c, \"UserProfile\");", "map": {"version": 3, "names": ["React", "useState", "User", "LogOut", "useAuth", "useLanguage", "jsxDEV", "_jsxDEV", "UserProfile", "darkMode", "_s", "_currentUser$email", "_currentUser$email2", "currentUser", "logout", "t", "showDropdown", "setShowDropdown", "styles", "container", "position", "display", "alignItems", "userButton", "gap", "padding", "borderRadius", "border", "backgroundColor", "cursor", "color", "transition", "avatar", "width", "height", "objectFit", "placeholder", "justifyContent", "userName", "fontWeight", "fontSize", "marginLeft", "xs", "sm", "dropdown", "top", "right", "marginTop", "boxShadow", "zIndex", "overflow", "dropdownHeader", "borderBottom", "flexDirection", "dropdownAvatar", "marginBottom", "dropdownUserName", "dropdownEmail", "dropdownItems", "dropdownItem", "logoutButton", "textAlign", "handleLogout", "error", "console", "defaultAvatar", "style", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "photoURL", "src", "alt", "displayName", "email", "split", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/FixTorchUMLDGM/uml-detector/src/components/Loginsignup/UserProfile.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { User, LogOut } from 'lucide-react';\r\nimport { useAuth } from '../../context/AuthContext';\r\nimport { useLanguage } from '../../context/LanguageContext';\r\n\r\ninterface UserProfileProps {\r\n  darkMode: boolean;\r\n}\r\n\r\nconst UserProfile: React.FC<UserProfileProps> = ({ darkMode }) => {\r\n  const { currentUser, logout } = useAuth();\r\n  const { t } = useLanguage();\r\n  const [showDropdown, setShowDropdown] = useState<boolean>(false);\r\n\r\n  const styles = {\r\n    container: {\r\n      position: 'relative' as const,\r\n      display: 'flex',\r\n      alignItems: 'center',\r\n    },\r\n    userButton: {\r\n      display: 'flex',\r\n      alignItems: 'center',\r\n      gap: '0.5rem',\r\n      padding: '0.5rem',\r\n      borderRadius: '9999px',\r\n      border: 'none',\r\n      backgroundColor: 'transparent',\r\n      cursor: 'pointer',\r\n      color: darkMode ? '#e2e8f0' : '#334155',\r\n      transition: 'background-color 0.2s',\r\n      ':hover': {\r\n        backgroundColor: darkMode ? 'rgba(148, 163, 184, 0.1)' : 'rgba(226, 232, 240, 0.5)',\r\n      },\r\n    },\r\n    avatar: {\r\n      width: '32px',\r\n      height: '32px',\r\n      borderRadius: '9999px',\r\n      objectFit: 'cover' as const,\r\n      border: `1px solid ${darkMode ? 'rgba(148, 163, 184, 0.2)' : 'rgba(203, 213, 225, 0.8)'}`,\r\n    },\r\n    placeholder: {\r\n      width: '32px',\r\n      height: '32px',\r\n      borderRadius: '9999px',\r\n      display: 'flex',\r\n      alignItems: 'center',\r\n      justifyContent: 'center',\r\n      backgroundColor: darkMode ? '#334155' : '#e2e8f0',\r\n      color: darkMode ? '#e2e8f0' : '#334155',\r\n    },\r\n    userName: {\r\n      fontWeight: 500,\r\n      fontSize: '0.875rem',\r\n      marginLeft: '0.5rem',\r\n      display: {\r\n        xs: 'none',\r\n        sm: 'block',\r\n      },\r\n    },\r\n    dropdown: {\r\n      position: 'absolute' as const,\r\n      top: '100%',\r\n      right: 0,\r\n      marginTop: '0.5rem',\r\n      width: '200px',\r\n      backgroundColor: darkMode ? '#1e293b' : '#ffffff',\r\n      borderRadius: '0.375rem',\r\n      boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',\r\n      zIndex: 50,\r\n      border: `1px solid ${darkMode ? 'rgba(148, 163, 184, 0.2)' : 'rgba(203, 213, 225, 0.8)'}`,\r\n      overflow: 'hidden',\r\n    },\r\n    dropdownHeader: {\r\n      padding: '1rem',\r\n      borderBottom: `1px solid ${darkMode ? 'rgba(148, 163, 184, 0.2)' : 'rgba(203, 213, 225, 0.8)'}`,\r\n      display: 'flex',\r\n      flexDirection: 'column' as const,\r\n      alignItems: 'center',\r\n    },\r\n    dropdownAvatar: {\r\n      width: '48px',\r\n      height: '48px',\r\n      borderRadius: '9999px',\r\n      objectFit: 'cover' as const,\r\n      marginBottom: '0.5rem',\r\n      border: `1px solid ${darkMode ? 'rgba(148, 163, 184, 0.2)' : 'rgba(203, 213, 225, 0.8)'}`,\r\n    },\r\n    dropdownUserName: {\r\n      fontWeight: 600,\r\n      fontSize: '0.875rem',\r\n      color: darkMode ? '#e2e8f0' : '#334155',\r\n    },\r\n    dropdownEmail: {\r\n      fontSize: '0.75rem',\r\n      color: darkMode ? '#94a3b8' : '#64748b',\r\n      marginTop: '0.25rem',\r\n    },\r\n    dropdownItems: {\r\n      padding: '0.5rem',\r\n    },\r\n    dropdownItem: {\r\n      display: 'flex',\r\n      alignItems: 'center',\r\n      padding: '0.75rem',\r\n      borderRadius: '0.375rem',\r\n      fontSize: '0.875rem',\r\n      color: darkMode ? '#e2e8f0' : '#334155',\r\n      cursor: 'pointer',\r\n      transition: 'background-color 0.2s',\r\n      ':hover': {\r\n        backgroundColor: darkMode ? 'rgba(148, 163, 184, 0.1)' : 'rgba(226, 232, 240, 0.5)',\r\n      },\r\n    },\r\n    logoutButton: {\r\n      display: 'flex',\r\n      alignItems: 'center',\r\n      gap: '0.5rem',\r\n      width: '100%',\r\n      padding: '0.75rem',\r\n      borderRadius: '0.375rem',\r\n      fontSize: '0.875rem',\r\n      color: darkMode ? '#e2e8f0' : '#334155',\r\n      backgroundColor: 'transparent',\r\n      border: 'none',\r\n      cursor: 'pointer',\r\n      textAlign: 'left' as const,\r\n      transition: 'background-color 0.2s',\r\n    },\r\n  };\r\n\r\n  const handleLogout = async () => {\r\n    try {\r\n      await logout();\r\n      setShowDropdown(false);\r\n    } catch (error) {\r\n      console.error('Logout error:', error);\r\n    }\r\n  };\r\n\r\n  // Default avatar if no photo URL\r\n  const defaultAvatar = (\r\n    <div style={styles.placeholder}>\r\n      <User size={20} />\r\n    </div>\r\n  );\r\n\r\n  if (!currentUser) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <div style={styles.container}>\r\n      <button \r\n        style={styles.userButton}\r\n        onClick={() => setShowDropdown(!showDropdown)}\r\n      >\r\n        {currentUser.photoURL ? (\r\n          <img \r\n            src={currentUser.photoURL} \r\n            alt={currentUser.displayName || 'User'} \r\n            style={styles.avatar}\r\n          />\r\n        ) : defaultAvatar}\r\n        <span \r\n  style={{\r\n    fontWeight: 500,\r\n    fontSize: '0.875rem',\r\n    marginLeft: '0.5rem',\r\n    display: 'block', // ou 'none'\r\n  }}\r\n>\r\n  {currentUser.displayName || currentUser.email?.split('@')[0]}\r\n</span>\r\n\r\n      </button>\r\n\r\n      {showDropdown && (\r\n        <div style={styles.dropdown}>\r\n          <div style={styles.dropdownHeader}>\r\n            {currentUser.photoURL ? (\r\n              <img \r\n                src={currentUser.photoURL} \r\n                alt={currentUser.displayName || 'User'} \r\n                style={styles.dropdownAvatar}\r\n              />\r\n            ) : (\r\n              <div style={{...styles.placeholder, width: '48px', height: '48px'}}>\r\n                <User size={24} />\r\n              </div>\r\n            )}\r\n            <div style={styles.dropdownUserName}>\r\n              {currentUser.displayName || currentUser.email?.split('@')[0]}\r\n            </div>\r\n            <div style={styles.dropdownEmail}>{currentUser.email}</div>\r\n          </div>\r\n          <div style={styles.dropdownItems}>\r\n            <button \r\n              style={styles.logoutButton}\r\n              onClick={handleLogout}\r\n            >\r\n              <LogOut size={16} />\r\n              <span>Log out</span>\r\n            </button>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default UserProfile;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,MAAM,QAAQ,cAAc;AAC3C,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,WAAW,QAAQ,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAM5D,MAAMC,WAAuC,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,kBAAA,EAAAC,mBAAA;EAChE,MAAM;IAAEC,WAAW;IAAEC;EAAO,CAAC,GAAGV,OAAO,CAAC,CAAC;EACzC,MAAM;IAAEW;EAAE,CAAC,GAAGV,WAAW,CAAC,CAAC;EAC3B,MAAM,CAACW,YAAY,EAAEC,eAAe,CAAC,GAAGhB,QAAQ,CAAU,KAAK,CAAC;EAEhE,MAAMiB,MAAM,GAAG;IACbC,SAAS,EAAE;MACTC,QAAQ,EAAE,UAAmB;MAC7BC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE;IACd,CAAC;IACDC,UAAU,EAAE;MACVF,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBE,GAAG,EAAE,QAAQ;MACbC,OAAO,EAAE,QAAQ;MACjBC,YAAY,EAAE,QAAQ;MACtBC,MAAM,EAAE,MAAM;MACdC,eAAe,EAAE,aAAa;MAC9BC,MAAM,EAAE,SAAS;MACjBC,KAAK,EAAErB,QAAQ,GAAG,SAAS,GAAG,SAAS;MACvCsB,UAAU,EAAE,uBAAuB;MACnC,QAAQ,EAAE;QACRH,eAAe,EAAEnB,QAAQ,GAAG,0BAA0B,GAAG;MAC3D;IACF,CAAC;IACDuB,MAAM,EAAE;MACNC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,MAAM;MACdR,YAAY,EAAE,QAAQ;MACtBS,SAAS,EAAE,OAAgB;MAC3BR,MAAM,EAAE,aAAalB,QAAQ,GAAG,0BAA0B,GAAG,0BAA0B;IACzF,CAAC;IACD2B,WAAW,EAAE;MACXH,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,MAAM;MACdR,YAAY,EAAE,QAAQ;MACtBL,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBe,cAAc,EAAE,QAAQ;MACxBT,eAAe,EAAEnB,QAAQ,GAAG,SAAS,GAAG,SAAS;MACjDqB,KAAK,EAAErB,QAAQ,GAAG,SAAS,GAAG;IAChC,CAAC;IACD6B,QAAQ,EAAE;MACRC,UAAU,EAAE,GAAG;MACfC,QAAQ,EAAE,UAAU;MACpBC,UAAU,EAAE,QAAQ;MACpBpB,OAAO,EAAE;QACPqB,EAAE,EAAE,MAAM;QACVC,EAAE,EAAE;MACN;IACF,CAAC;IACDC,QAAQ,EAAE;MACRxB,QAAQ,EAAE,UAAmB;MAC7ByB,GAAG,EAAE,MAAM;MACXC,KAAK,EAAE,CAAC;MACRC,SAAS,EAAE,QAAQ;MACnBd,KAAK,EAAE,OAAO;MACdL,eAAe,EAAEnB,QAAQ,GAAG,SAAS,GAAG,SAAS;MACjDiB,YAAY,EAAE,UAAU;MACxBsB,SAAS,EAAE,yEAAyE;MACpFC,MAAM,EAAE,EAAE;MACVtB,MAAM,EAAE,aAAalB,QAAQ,GAAG,0BAA0B,GAAG,0BAA0B,EAAE;MACzFyC,QAAQ,EAAE;IACZ,CAAC;IACDC,cAAc,EAAE;MACd1B,OAAO,EAAE,MAAM;MACf2B,YAAY,EAAE,aAAa3C,QAAQ,GAAG,0BAA0B,GAAG,0BAA0B,EAAE;MAC/FY,OAAO,EAAE,MAAM;MACfgC,aAAa,EAAE,QAAiB;MAChC/B,UAAU,EAAE;IACd,CAAC;IACDgC,cAAc,EAAE;MACdrB,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,MAAM;MACdR,YAAY,EAAE,QAAQ;MACtBS,SAAS,EAAE,OAAgB;MAC3BoB,YAAY,EAAE,QAAQ;MACtB5B,MAAM,EAAE,aAAalB,QAAQ,GAAG,0BAA0B,GAAG,0BAA0B;IACzF,CAAC;IACD+C,gBAAgB,EAAE;MAChBjB,UAAU,EAAE,GAAG;MACfC,QAAQ,EAAE,UAAU;MACpBV,KAAK,EAAErB,QAAQ,GAAG,SAAS,GAAG;IAChC,CAAC;IACDgD,aAAa,EAAE;MACbjB,QAAQ,EAAE,SAAS;MACnBV,KAAK,EAAErB,QAAQ,GAAG,SAAS,GAAG,SAAS;MACvCsC,SAAS,EAAE;IACb,CAAC;IACDW,aAAa,EAAE;MACbjC,OAAO,EAAE;IACX,CAAC;IACDkC,YAAY,EAAE;MACZtC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBG,OAAO,EAAE,SAAS;MAClBC,YAAY,EAAE,UAAU;MACxBc,QAAQ,EAAE,UAAU;MACpBV,KAAK,EAAErB,QAAQ,GAAG,SAAS,GAAG,SAAS;MACvCoB,MAAM,EAAE,SAAS;MACjBE,UAAU,EAAE,uBAAuB;MACnC,QAAQ,EAAE;QACRH,eAAe,EAAEnB,QAAQ,GAAG,0BAA0B,GAAG;MAC3D;IACF,CAAC;IACDmD,YAAY,EAAE;MACZvC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBE,GAAG,EAAE,QAAQ;MACbS,KAAK,EAAE,MAAM;MACbR,OAAO,EAAE,SAAS;MAClBC,YAAY,EAAE,UAAU;MACxBc,QAAQ,EAAE,UAAU;MACpBV,KAAK,EAAErB,QAAQ,GAAG,SAAS,GAAG,SAAS;MACvCmB,eAAe,EAAE,aAAa;MAC9BD,MAAM,EAAE,MAAM;MACdE,MAAM,EAAE,SAAS;MACjBgC,SAAS,EAAE,MAAe;MAC1B9B,UAAU,EAAE;IACd;EACF,CAAC;EAED,MAAM+B,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMhD,MAAM,CAAC,CAAC;MACdG,eAAe,CAAC,KAAK,CAAC;IACxB,CAAC,CAAC,OAAO8C,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;IACvC;EACF,CAAC;;EAED;EACA,MAAME,aAAa,gBACjB1D,OAAA;IAAK2D,KAAK,EAAEhD,MAAM,CAACkB,WAAY;IAAA+B,QAAA,eAC7B5D,OAAA,CAACL,IAAI;MAACkE,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACf,CACN;EAED,IAAI,CAAC3D,WAAW,EAAE;IAChB,OAAO,IAAI;EACb;EAEA,oBACEN,OAAA;IAAK2D,KAAK,EAAEhD,MAAM,CAACC,SAAU;IAAAgD,QAAA,gBAC3B5D,OAAA;MACE2D,KAAK,EAAEhD,MAAM,CAACK,UAAW;MACzBkD,OAAO,EAAEA,CAAA,KAAMxD,eAAe,CAAC,CAACD,YAAY,CAAE;MAAAmD,QAAA,GAE7CtD,WAAW,CAAC6D,QAAQ,gBACnBnE,OAAA;QACEoE,GAAG,EAAE9D,WAAW,CAAC6D,QAAS;QAC1BE,GAAG,EAAE/D,WAAW,CAACgE,WAAW,IAAI,MAAO;QACvCX,KAAK,EAAEhD,MAAM,CAACc;MAAO;QAAAqC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC,GACAP,aAAa,eACjB1D,OAAA;QACN2D,KAAK,EAAE;UACL3B,UAAU,EAAE,GAAG;UACfC,QAAQ,EAAE,UAAU;UACpBC,UAAU,EAAE,QAAQ;UACpBpB,OAAO,EAAE,OAAO,CAAE;QACpB,CAAE;QAAA8C,QAAA,EAEDtD,WAAW,CAACgE,WAAW,MAAAlE,kBAAA,GAAIE,WAAW,CAACiE,KAAK,cAAAnE,kBAAA,uBAAjBA,kBAAA,CAAmBoE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAAA;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEO,CAAC,EAERxD,YAAY,iBACXT,OAAA;MAAK2D,KAAK,EAAEhD,MAAM,CAAC0B,QAAS;MAAAuB,QAAA,gBAC1B5D,OAAA;QAAK2D,KAAK,EAAEhD,MAAM,CAACiC,cAAe;QAAAgB,QAAA,GAC/BtD,WAAW,CAAC6D,QAAQ,gBACnBnE,OAAA;UACEoE,GAAG,EAAE9D,WAAW,CAAC6D,QAAS;UAC1BE,GAAG,EAAE/D,WAAW,CAACgE,WAAW,IAAI,MAAO;UACvCX,KAAK,EAAEhD,MAAM,CAACoC;QAAe;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,gBAEFjE,OAAA;UAAK2D,KAAK,EAAE;YAAC,GAAGhD,MAAM,CAACkB,WAAW;YAAEH,KAAK,EAAE,MAAM;YAAEC,MAAM,EAAE;UAAM,CAAE;UAAAiC,QAAA,eACjE5D,OAAA,CAACL,IAAI;YAACkE,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CACN,eACDjE,OAAA;UAAK2D,KAAK,EAAEhD,MAAM,CAACsC,gBAAiB;UAAAW,QAAA,EACjCtD,WAAW,CAACgE,WAAW,MAAAjE,mBAAA,GAAIC,WAAW,CAACiE,KAAK,cAAAlE,mBAAA,uBAAjBA,mBAAA,CAAmBmE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAAA;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eACNjE,OAAA;UAAK2D,KAAK,EAAEhD,MAAM,CAACuC,aAAc;UAAAU,QAAA,EAAEtD,WAAW,CAACiE;QAAK;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC,eACNjE,OAAA;QAAK2D,KAAK,EAAEhD,MAAM,CAACwC,aAAc;QAAAS,QAAA,eAC/B5D,OAAA;UACE2D,KAAK,EAAEhD,MAAM,CAAC0C,YAAa;UAC3Ba,OAAO,EAAEX,YAAa;UAAAK,QAAA,gBAEtB5D,OAAA,CAACJ,MAAM;YAACiE,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpBjE,OAAA;YAAA4D,QAAA,EAAM;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC9D,EAAA,CAzMIF,WAAuC;EAAA,QACXJ,OAAO,EACzBC,WAAW;AAAA;AAAA2E,EAAA,GAFrBxE,WAAuC;AA2M7C,eAAeA,WAAW;AAAC,IAAAwE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}