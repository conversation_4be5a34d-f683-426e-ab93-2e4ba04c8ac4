//AnalysisResults.tsx

import React, { useState, useRef, useEffect } from "react";
import EntityPopup from "./EntityPopup";
import { useLanguage } from "../../context/LanguageContext";

// Définir l'interface ClassData une seule fois
interface ClassData {
  name: string;
  attributes: string[];
  methods: string[];
}

interface SelectedClassData {
  memoryAttributes: string[];
  memoryMethods: string[];
  extractedAttributes: string[];
  extractedMethods: string[];
}

interface AnalysisResultsProps {
  darkMode: boolean;
  imageUrl: string;
  extractedText: string;
  onViewAnnotatedImage?: () => void;
  onNavigateToUMLExtractor?: () => void;
  resetAnalysis: () => void;
  onUpdateExtractedText?: (newText: string) => void; // Nouvelle prop pour mettre à jour le texte extrait
}

const AnalysisResults: React.FC<AnalysisResultsProps> = ({
  darkMode,
  imageUrl,
  extractedText,
  onViewAnnotatedImage,
  onNavigateToUMLExtractor,
  resetAnalysis,
  onUpdateExtractedText
}) => {
  const { t } = useLanguage();
  const [showCopyTooltip, setShowCopyTooltip] = useState(false);
  const [showAnnotateTooltip, setShowAnnotateTooltip] = useState(false);
  const [showConvertTooltip, setShowConvertTooltip] = useState(false);
  const [selectedEntity, setSelectedEntity] = useState<string | null>(null);
  const [popupPosition, setPopupPosition] = useState({ x: 0, y: 0 });
  const textContentRef = useRef<HTMLDivElement>(null);
  
  // Ajouter les états manquants
  const [memoryData, setMemoryData] = useState<ClassData[]>([]);
  const [extractedClasses, setExtractedClasses] = useState<ClassData[]>([]);
  const [selectedClassData, setSelectedClassData] = useState<SelectedClassData>({
    memoryAttributes: [],
    memoryMethods: [],
    extractedAttributes: [],
    extractedMethods: []
  });

  // Ajouter un état pour le texte extrait - C'EST LA CLEF DU PROBLÈME
  const [localExtractedText, setLocalExtractedText] = useState<string>(extractedText);

  // Mettre à jour l'état local lorsque la prop extractedText change
  useEffect(() => {
    setLocalExtractedText(extractedText);
  }, [extractedText]);

  // Définir la fonction parseExtractedText en dehors des useEffect
  const parseExtractedText = (text: string) => {
    if (!text) return;
    
    const classes: ClassData[] = [];
    const classSections = text.split(/class \d+:/g);
    
    // Remove empty first element if exists
    if (classSections[0].trim() === '') {
      classSections.shift();
    }

    classSections.forEach(section => {
      if (!section.trim()) return;
      
      const classNameMatch = section.match(/NOM_CLASSE:\s+([^\n]+)/);
      if (classNameMatch) {
        const className = classNameMatch[1].trim();
        
        // Extract attributes
        const attributesMatch = section.match(/ATTRIBUTS:([\s\S]*?)(?=MÉTHODES:|$)/);
        const attributes = attributesMatch ? 
          attributesMatch[1].trim().split('\n').filter(attr => attr.trim()) : [];
        
        // Extract methods
        const methodsMatch = section.match(/MÉTHODES:([\s\S]*?)(?=class \d+:|$)/);
        const methods = methodsMatch ?
          methodsMatch[1].trim().split('\n').filter(method => method.trim()) : [];
        
        classes.push({
          name: className,
          attributes,
          methods
        });
      }
    });

    setExtractedClasses(classes);
  };

  // Charger les données de memoire.txt au chargement du composant
  useEffect(() => {
    fetchMemoryData();
  }, []);

  // Analyser le texte extrait lorsqu'il change
  useEffect(() => {
    if (extractedText) {
      parseExtractedText(extractedText);
    }
  }, [extractedText]);

  // Dans AnalysisResults.tsx, ajoutez un useEffect pour déboguer les changements de props
  useEffect(() => {
    console.log("extractedText a changé dans AnalysisResults:", extractedText.substring(0, 100) + "...");
    // Assurez-vous que parseExtractedText est appelé lorsque extractedText change
    if (extractedText) {
      parseExtractedText(extractedText);
    }
  }, [extractedText]);

  const copyToClipboard = () => {
    // CORRECTION: Utiliser localExtractedText au lieu d'extractedText
    if (localExtractedText) {
      navigator.clipboard.writeText(localExtractedText)
        .then(() => {
          setShowCopyTooltip(true);
          setTimeout(() => setShowCopyTooltip(false), 2000);
        })
        .catch(err => {
          console.error('Erreur lors de la copie dans le presse-papiers: ', err);
        });
    }
  };

  const downloadText = () => {
    // CORRECTION: Utiliser localExtractedText au lieu d'extractedText
    if (localExtractedText) {
      const blob = new Blob([localExtractedText], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'texte_extrait_uml.txt';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  const handleViewAnnotatedImage = () => {
    if (onViewAnnotatedImage) {
      onViewAnnotatedImage();
    }
  };

  const handleNavigateToUMLExtractor = () => {
    if (onNavigateToUMLExtractor) {
      onNavigateToUMLExtractor();
    }
  };

  // Fonction pour récupérer les données de memoire.txt
  const fetchMemoryData = async () => {
    try {
      console.log("🔄 FORÇAGE: Récupération de memoire.txt sans cache...");
      // Forcer le rechargement en ajoutant un timestamp pour éviter le cache
      const timestamp = new Date().getTime();
      const response = await fetch(`http://127.0.0.1:8000/memoire2.txt?t=${timestamp}`, {
        cache: 'no-cache',
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      });
      if (!response.ok) {
        throw new Error(`Impossible de récupérer le fichier mémoire: ${response.status} ${response.statusText}`);
      }

      const text = await response.text();
      console.log("📄 CONTENU RÉCUPÉRÉ de memoire.txt:", text.substring(0, 500) + "..."); // Afficher les 500 premiers caractères
      console.log("📊 TAILLE du fichier:", text.length, "caractères");
      
      // Nouvelle approche pour diviser le texte en sections de classes
      // Rechercher tous les "NOM_CLASSE:" comme début d'une nouvelle classe
      const classRegex = /NOM_CLASSE:/g;
      let match;
      const startIndices = [];
      
      while ((match = classRegex.exec(text)) !== null) {
        startIndices.push(match.index);
      }
      
      const parsedData: ClassData[] = [];
      
      // Traiter chaque section de classe
      for (let i = 0; i < startIndices.length; i++) {
        const startIdx = startIndices[i];
        const endIdx = i < startIndices.length - 1 ? startIndices[i + 1] : text.length;
        const classBlock = text.substring(startIdx, endIdx).trim();
        
        const lines = classBlock.split('\n');
        let className = "";
        const attributes: string[] = [];
        const methods: string[] = [];
        
        let currentSection = '';
        
        for (const line of lines) {
          if (line.startsWith('NOM_CLASSE:')) {
            className = line.replace('NOM_CLASSE:', '').trim();
          } else if (line.startsWith('ATTRIBUTS:')) {
            currentSection = 'attributes';
          } else if (line.startsWith('MÉTHODES:')) {
            currentSection = 'methods';
          } else if (line.trim() && currentSection === 'attributes') {
            attributes.push(line.trim());
          } else if (line.trim() && currentSection === 'methods') {
            methods.push(line.trim());
          }
        }
        
        if (className) {
          console.log(`Classe parsée: ${className} avec ${attributes.length} attributs et ${methods.length} méthodes`);
          parsedData.push({ name: className, attributes, methods });
        }
      }
      
      console.log("Toutes les classes parsées:", parsedData.map(d => d.name).join(", "));
      setMemoryData(parsedData);
    } catch (error) {
      console.error("Erreur lors de la récupération du fichier mémoire:", error);
    }
  };

  // Modifier la fonction handleEntityClick pour rendre la recherche insensible à la casse
  const handleEntityClick = (event: React.MouseEvent<HTMLSpanElement>) => {
    const entityName = event.currentTarget.getAttribute('data-entity');
    if (entityName) {
      console.log(`Classe sélectionnée: ${entityName}`);
      console.log(`Noms de classes en mémoire: ${memoryData.map(c => c.name).join(", ")}`);
      
      const rect = event.currentTarget.getBoundingClientRect();
      setPopupPosition({
        x: rect.left,
        y: rect.bottom + window.scrollY
      });
      
      // Trouver TOUTES les classes de même nom dans memoire.txt (insensible à la casse)
      const memoryClasses = memoryData.filter(c => c.name.toLowerCase() === entityName.toLowerCase());
      console.log(`🔍 RECHERCHE: Trouvé ${memoryClasses.length} classe(s) avec le nom "${entityName}":`, memoryClasses.map(c => c.name));

      // DEBUG: Afficher le contenu de chaque classe trouvée
      memoryClasses.forEach((cls, index) => {
        console.log(`📋 Classe ${index + 1} "${cls.name}":`, {
          attributes: cls.attributes,
          methods: cls.methods
        });
      });

      // Fusionner tous les attributs et méthodes des classes de même nom
      const mergedMemoryData = {
        attributes: [] as string[],
        methods: [] as string[]
      };

      memoryClasses.forEach(cls => {
        // Ajouter les attributs (éviter les doublons)
        cls.attributes.forEach(attr => {
          if (!mergedMemoryData.attributes.includes(attr)) {
            mergedMemoryData.attributes.push(attr);
          }
        });

        // Ajouter les méthodes (éviter les doublons)
        cls.methods.forEach(method => {
          if (!mergedMemoryData.methods.includes(method)) {
            mergedMemoryData.methods.push(method);
          }
        });
      });

      console.log("Données fusionnées de la classe:", mergedMemoryData);

      // DEBUG: Afficher les données exactes qui seront passées à EntityPopup
      console.log("🔍 DEBUG - Données qui seront affichées dans EntityPopup:");
      console.log("📋 memoryAttributes:", mergedMemoryData.attributes);
      console.log("🔧 memoryMethods:", mergedMemoryData.methods);

      // VÉRIFICATION: Ces données correspondent-elles à ce qui est dans memoire.txt ?
      console.log("⚠️ VÉRIFICATION: Si vous voyez des attributs comme 'idClient', 'nomClient', etc.");
      console.log("⚠️ C'est un BUG car ces attributs n'existent pas dans les classes Client de memoire.txt !");
      
      // Trouver les données de la classe dans le texte extrait (insensible à la casse)
      const extractedClass = extractedClasses.find(c => c.name.toLowerCase() === entityName.toLowerCase());
      console.log("Données de la classe dans le texte extrait:", extractedClass);
      
      // Mettre à jour les données sélectionnées avec les données fusionnées
      const updatedData = {
        memoryAttributes: mergedMemoryData.attributes,
        memoryMethods: mergedMemoryData.methods,
        extractedAttributes: extractedClass?.attributes || [],
        extractedMethods: extractedClass?.methods || []
      };
      
      console.log("Données mises à jour pour le popup:", updatedData);
      setSelectedClassData(updatedData);
      
      setSelectedEntity(entityName);
    }
  };

  const closePopup = () => {
    setSelectedEntity(null);
  };

  // Ajoutons une fonction pour envoyer le texte mis à jour au serveur
  const saveUpdatedTextToServer = async (updatedText: string) => {
    try {
      console.log("Envoi du texte mis à jour au serveur...");
      const response = await fetch("http://127.0.0.1:8000/update_text/", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ text: updatedText }),
      });

      if (!response.ok) {
        throw new Error(`Erreur HTTP: ${response.status}`);
      }

      const result = await response.json();
      console.log("Texte mis à jour sur le serveur:", result);
      return true;
    } catch (error) {
      console.error("Erreur lors de la mise à jour du texte sur le serveur:", error);
      return false;
    }
  };

  // FONCTION CORRIGÉE: handleModifyEntity
const handleModifyEntity = async (selectedAttributes: string[], selectedMethods: string[]) => {
  if (!selectedEntity || (!selectedAttributes.length && !selectedMethods.length)) {
    closePopup();
    return;
  }
  
  console.log(`Modification de l'entité: ${selectedEntity}`);
  console.log("Attributs sélectionnés:", selectedAttributes);
  console.log("Méthodes sélectionnées:", selectedMethods);
  
  // Créer une copie du texte extrait LOCAL
  let newExtractedText = localExtractedText;
  console.log("Texte extrait original:", newExtractedText.substring(0, 100) + "...");
  
  // Rechercher le pattern de la classe spécifique
  const classNameEscaped = selectedEntity.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  
  // Pattern pour trouver toute la classe (du nom jusqu'à la prochaine classe ou fin)
  const classPattern = new RegExp(
    `(class \\d+:\\s*NOM_CLASSE:\\s*${classNameEscaped}\\s*ATTRIBUTS:[\\s\\S]*?)(?=class \\d+:|$)`,
    'i'
  );
  
  const classMatch = newExtractedText.match(classPattern);
  
  if (!classMatch) {
    console.error("Classe non trouvée dans le texte:", selectedEntity);
    closePopup();
    return;
  }
  
  let classContent = classMatch[1];
  console.log("Contenu de la classe trouvé:", classContent);
  
  // Traitement des attributs
  if (selectedAttributes.length > 0) {
    // Trouver la position après "ATTRIBUTS:" et avant "MÉTHODES:"
    const attributesRegex = /(ATTRIBUTS:\s*)([\s\S]*?)(?=MÉTHODES:|$)/;
    const attributesMatch = classContent.match(attributesRegex);
    
    if (attributesMatch) {
      const attributesPrefix = attributesMatch[1]; // "ATTRIBUTS: "
      const existingAttributes = attributesMatch[2].trim(); // attributs existants
      
      // Construire la nouvelle section d'attributs
      let newAttributesSection = attributesPrefix;
      
      // Ajouter les attributs existants s'il y en a
      if (existingAttributes) {
        newAttributesSection += '\n' + existingAttributes;
      }
      
      // Ajouter les nouveaux attributs
      selectedAttributes.forEach(attr => {
        newAttributesSection += '\n' + attr;
      });
      
      // Ajouter un saut de ligne à la fin pour séparer de MÉTHODES
      newAttributesSection += '\n';
      
      // CORRECTION: Remplacer SEULEMENT la section des attributs, sans ajouter MÉTHODES:
      classContent = classContent.replace(attributesRegex, newAttributesSection);
      
      console.log("Attributs ajoutés avec succès");
    }
  }
  
  // Traitement des méthodes
  if (selectedMethods.length > 0) {
    // Vérifier d'abord si MÉTHODES: existe déjà
    const methodsRegex = /(MÉTHODES:\s*)([\s\S]*?)(?=----- |$)/;
    const methodsMatch = classContent.match(methodsRegex);
    
    if (methodsMatch) {
      // MÉTHODES: existe déjà
      const methodsPrefix = methodsMatch[1]; // "MÉTHODES: "
      const existingMethods = methodsMatch[2].trim(); // méthodes existantes
      
      // Construire la nouvelle section de méthodes
      let newMethodsSection = methodsPrefix;
      
      // Ajouter les méthodes existantes s'il y en a
      if (existingMethods) {
        newMethodsSection += '\n' + existingMethods;
      }
      
      // Ajouter les nouvelles méthodes
      selectedMethods.forEach(method => {
        newMethodsSection += '\n' + method;
      });
      
      // Ajouter deux sauts de ligne à la fin pour bien séparer des sections suivantes
      newMethodsSection += '\n\n';
      
      // Remplacer dans le contenu de la classe
      classContent = classContent.replace(methodsRegex, newMethodsSection);
      
      console.log("Méthodes ajoutées avec succès");
    } else {
      // MÉTHODES: n'existe pas encore, l'ajouter après ATTRIBUTS
      const attributesEndRegex = /(ATTRIBUTS:[\s\S]*?)(\n*)$/;
      if (classContent.match(attributesEndRegex)) {
        let methodsSection = '\nMÉTHODES:';
        selectedMethods.forEach(method => {
          methodsSection += '\n' + method;
        });
        methodsSection += '\n\n';
        
        classContent = classContent.replace(attributesEndRegex, '$1' + methodsSection);
        console.log("Section MÉTHODES ajoutée pour la première fois");
      }
    }
  }
  
  // Remplacer le contenu de la classe dans le texte complet
  const updatedText = newExtractedText.replace(classPattern, classContent);
  
  console.log("Texte mis à jour:", updatedText.substring(0, 500) + "...");
  
  // Mettre à jour immédiatement l'état local AVANT d'envoyer au serveur
  setLocalExtractedText(updatedText);
  parseExtractedText(updatedText);
  
  // Envoyer le texte mis à jour au serveur
  const success = await saveUpdatedTextToServer(updatedText);
  
  if (success) {
    // Propager les modifications au composant parent
    if (onUpdateExtractedText) {
      console.log("Appel de onUpdateExtractedText");
      onUpdateExtractedText(updatedText);
    } else {
      console.error("onUpdateExtractedText n'est pas défini");
    }
  } else {
    console.error("Échec de la mise à jour du texte sur le serveur");
    // En cas d'échec, revenir au texte précédent
    setLocalExtractedText(extractedText);
    parseExtractedText(extractedText);
    alert(t('analysis.error.updateFailed'));
  }
  
  // Fermer le popup
  closePopup();
};

  // Fermer le popup si on clique ailleurs sur la page
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectedEntity) {
        // Vérifier si le clic est sur l'overlay du popup (pas sur le popup lui-même)
        const target = event.target as HTMLElement;
        
        // Si le clic est sur l'overlay (div avec position fixed qui couvre tout l'écran)
        if (target.style.position === 'fixed' && 
            target.style.top === '0px' && 
            target.style.left === '0px' && 
            target.style.right === '0px' && 
            target.style.bottom === '0px') {
          closePopup();
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [selectedEntity]);

  // CORRECTION PRINCIPALE: Fonction pour rendre le texte avec des entités cliquables
  // Utiliser localExtractedText au lieu d'extractedText
  const renderTextWithClickableEntities = () => {
    if (!localExtractedText) return null;
    console.log("Rendu du texte avec entités cliquables:", localExtractedText.substring(0, 100) + "...");
    
    // Diviser le texte en sections pour chaque classe
    const classSections = localExtractedText.split(/class \d+:/g);
    
    // Si le premier élément est vide (ce qui arrive si le texte commence par "class X:"), le supprimer
    const sections = classSections[0].trim() === '' ? classSections.slice(1) : classSections;
    
    return (
      <>
        {sections.map((section, index) => {
          if (!section.trim()) return null;
          
          // Extraire le nom de la classe
          const classNameMatch = section.match(/NOM_CLASSE:\s+([^\n]+)/);
          if (!classNameMatch || classNameMatch.index === undefined) {
            return (
              <div key={index}>
                <div>class {index+1}:</div>
                {section}
              </div>
            );
          }
          
          const className = classNameMatch[1].trim();
          const matchIndex = classNameMatch.index;
          const beforeClassName = section.substring(0, matchIndex + 11); // 11 = longueur de "NOM_CLASSE: "
          const afterClassName = section.substring(matchIndex + 11 + className.length);
          
          return (
            <div key={index}>
              <div>class {index+1}:</div>
              {beforeClassName}
              <span 
                style={{
                  cursor: 'pointer',
                  color: '#3b82f6',
                  fontWeight: 'bold',
                  textDecoration: 'underline'
                }}
                data-entity={className}
                onClick={handleEntityClick}
              >
                {className}
              </span>
              {afterClassName}
            </div>
          );
        })}
      </>
    );
  };

  const getStyles = () => ({
    resultsHeader: {
      textAlign: 'center' as const,
      marginBottom: '20px',
      color: darkMode ? '#ffffff' : '#333'
    },
    imagePreviewContainer: {
      position: 'relative' as const,
      width: '100%',
      maxWidth: '600px',
      margin: '20px auto'
    },
    imagePreview: {
      width: '100%',
      borderRadius: '8px',
      display: 'block',
      boxShadow: darkMode ? '0 4px 8px rgba(0,0,0,0.5)' : '0 4px 8px rgba(0,0,0,0.1)'
    },
    annotateIcon: {
      position: 'absolute' as const,
      top: '10px',
      right: '10px',
      backgroundColor: darkMode ? 'rgba(0,0,0,0.7)' : 'rgba(255,255,255,0.7)',
      borderRadius: '50%',
      padding: '8px',
      cursor: 'pointer',
      boxShadow: '0 2px 5px rgba(0,0,0,0.2)',
      zIndex: 2,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    },
    textPreviewContainer: {
      position: 'relative' as const,
      margin: '20px 0',
      borderRadius: '8px',
      backgroundColor: darkMode ? '#222' : '#f5f7fa',
      border: `1px solid ${darkMode ? '#444' : '#e0e0e0'}`,
      padding: '15px'
    },
    textPreviewHeader: {
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: '10px',
      borderBottom: `1px solid ${darkMode ? '#444' : '#e0e0e0'}`,
      paddingBottom: '8px'
    },
    textPreviewTitle: {
      margin: 0,
      fontSize: '16px',
      fontWeight: '500'
    },
    textPreviewActions: {
      display: 'flex',
      gap: '10px'
    },
    actionButton: {
      background: 'none',
      border: 'none',
      cursor: 'pointer',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '5px',
      borderRadius: '4px',
      color: darkMode ? '#aaa' : '#666',
      transition: 'all 0.2s ease',
      position: 'relative' as const
    },
    textContent: {
      whiteSpace: 'pre-wrap' as const,
      fontSize: '14px',
      maxHeight: '300px',
      overflowY: 'auto' as const,
      padding: '5px'
    },
    tooltip: {
      position: 'absolute' as const,
      bottom: '100%',
      left: '50%',
      transform: 'translateX(-50%)',
      backgroundColor: darkMode ? '#333' : '#fff',
      color: darkMode ? '#fff' : '#333',
      padding: '5px 10px',
      borderRadius: '4px',
      fontSize: '12px',
      boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
      zIndex: 1000,
      whiteSpace: 'nowrap' as const
    },
    annotateTooltip: {
      position: 'absolute' as const,
      top: '-40px',
      right: '0',
      backgroundColor: darkMode ? '#333' : '#fff',
      color: darkMode ? '#fff' : '#333',
      padding: '6px 12px',
      borderRadius: '4px',
      fontSize: '12px',
      boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
      zIndex: 1000,
      whiteSpace: 'nowrap' as const,
      width: '180px',
      textAlign: 'center' as const
    },
    convertTooltip: {
      position: 'absolute' as const,
      bottom: '100%',
      left: '50%',
      transform: 'translateX(-50%)',
      backgroundColor: darkMode ? '#333' : '#fff',
      color: darkMode ? '#fff' : '#333',
      padding: '6px 12px',
      borderRadius: '4px',
      fontSize: '12px',
      boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
      zIndex: 1000,
      width: '220px',
      textAlign: 'center' as const,
      marginBottom: '5px',
      whiteSpace: 'normal' as const
    },
    checkboxContainer: {
      display: 'flex',
      flexDirection: 'column' as const,
      gap: '15px',
      marginTop: '30px'
    },
    checkboxLabel: {
      display: 'flex',
      alignItems: 'center',
      gap: '10px',
      cursor: 'pointer'
    }
  });

  const styles = getStyles();

  return (
    <>
      <h2 style={styles.resultsHeader}>Image analysée</h2>
      <div style={{textAlign: 'center', marginBottom: '20px'}}>
      </div>
      
      <div style={styles.imagePreviewContainer}>
        <img src={imageUrl} alt={t('analysis.diagramAlt')} style={styles.imagePreview} />
        <div 
          style={styles.annotateIcon}
          onClick={handleViewAnnotatedImage}
          onMouseEnter={() => setShowAnnotateTooltip(true)}
          onMouseLeave={() => setShowAnnotateTooltip(false)}
        >
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke={darkMode ? "white" : "#333"} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path>
            <circle cx="12" cy="12" r="3"></circle>
          </svg>
          {showAnnotateTooltip && (
            <div style={styles.annotateTooltip}>
              {t('analysis.tooltip.viewAnnotated')}
            </div>
          )}
        </div>
      </div>

      {extractedText && (
        <div style={styles.textPreviewContainer}>
          <div style={styles.textPreviewHeader}>
            <h3 style={styles.textPreviewTitle}>Texte extrait</h3>
            <div style={styles.textPreviewActions}>
              <button 
                onClick={copyToClipboard}
                style={styles.actionButton}
                onMouseOver={(e) => e.currentTarget.style.backgroundColor = darkMode ? '#444' : '#eee'}
                onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                  <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                </svg>
                {showCopyTooltip && (
                  <span style={styles.tooltip}>Copié!</span>
                )}
              </button>
              <button 
                onClick={downloadText}
                style={styles.actionButton}
                onMouseOver={(e) => e.currentTarget.style.backgroundColor = darkMode ? '#444' : '#eee'}
                onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                  <polyline points="7 10 12 15 17 10"></polyline>
                  <line x1="12" y1="15" x2="12" y2="3"></line>
                </svg>
              </button>
              <button 
                onClick={handleNavigateToUMLExtractor}
                style={styles.actionButton}
                onMouseOver={(e) => {
                  e.currentTarget.style.backgroundColor = darkMode ? '#444' : '#eee';
                  setShowConvertTooltip(true);
                }}
                onMouseOut={(e) => {
                  e.currentTarget.style.backgroundColor = 'transparent';
                  setShowConvertTooltip(false);
                }}
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M5 12h14M13 5l7 7-7 7" />
                </svg>
                {showConvertTooltip && (
                  <span style={styles.convertTooltip}>
                    {t('analysis.tooltip.convertDiagram')}
                  </span>
                )}
              </button>
            </div>
          </div>
          <div style={styles.textContent} ref={textContentRef}>
            {renderTextWithClickableEntities()}
          </div>
        </div>
      )}

      {/* Popup pour l'entité sélectionnée */}
      {selectedEntity && (
        <EntityPopup
          darkMode={darkMode}
          entityName={selectedEntity}
          position={popupPosition}
          onClose={closePopup}
          onModify={handleModifyEntity}
          memoryAttributes={selectedClassData.memoryAttributes}
          memoryMethods={selectedClassData.memoryMethods}
          extractedAttributes={selectedClassData.extractedAttributes}
          extractedMethods={selectedClassData.extractedMethods}
          currentDiagramText={extractedText}
        />
      )}

      <div style={styles.checkboxContainer}>
        <button 
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            gap: "6px",
            padding: "0.5rem",
            backgroundColor: "rgba(59, 130, 246, 0.1)",
            color: "#60a5fa",
            border: "1px solid rgba(96, 165, 250, 0.2)",
            borderRadius: "6px",
            fontSize: "0.875rem",
            fontWeight: "500",
            cursor: "pointer",
            width: "100%",
            boxSizing: "border-box"
          }}
          onClick={resetAnalysis}
        >
          <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <line x1="12" y1="5" x2="12" y2="19"></line>
            <line x1="5" y1="12" x2="19" y2="12"></line>
          </svg>
          Nouveau Projet
        </button>
      </div>
    </>
  );
};

export default AnalysisResults;
