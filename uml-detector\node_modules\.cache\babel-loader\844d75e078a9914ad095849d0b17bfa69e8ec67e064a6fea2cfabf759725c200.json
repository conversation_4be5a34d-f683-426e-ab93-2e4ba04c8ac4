{"ast": null, "code": "// Define styles as a constant that can be imported by the main component\nexport const styles = {\n  container: {\n    fontFamily: \"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif\",\n    minHeight: \"100vh\",\n    display: \"flex\",\n    transition: \"background-color 0.3s ease, color 0.3s ease\"\n  },\n  historySidebar: {\n    height: \"100vh\",\n    position: \"fixed\",\n    left: 0,\n    top: 0,\n    overflowY: \"auto\",\n    transition: \"width 0.3s ease\",\n    backgroundColor: \"inherit\",\n    zIndex: 10\n  },\n  historyHeader: {\n    padding: \"1.25rem 1rem\",\n    borderBottom: \"1px solid rgba(148, 163, 184, 0.15)\",\n    display: \"flex\",\n    flexDirection: \"column\",\n    gap: \"12px\"\n  },\n  historyTitle: {\n    fontSize: \"1rem\",\n    fontWeight: \"600\",\n    margin: 0\n  },\n  newProjectButton: {\n    display: \"flex\",\n    alignItems: \"center\",\n    justifyContent: \"center\",\n    gap: \"6px\",\n    padding: \"0.5rem\",\n    backgroundColor: \"rgba(59, 130, 246, 0.1)\",\n    color: \"#60a5fa\",\n    border: \"1px solid\",\n    borderColor: \"rgba(96, 165, 250, 0.2)\",\n    borderRadius: \"6px\",\n    fontSize: \"0.875rem\",\n    fontWeight: \"500\",\n    cursor: \"pointer\",\n    width: \"100%\",\n    transition: \"all 0.2s ease\"\n  },\n  historyList: {\n    padding: \"0.5rem 0\"\n  },\n  historyItem: {\n    padding: \"0.75rem 1rem\",\n    display: \"flex\",\n    alignItems: \"center\",\n    gap: \"10px\",\n    cursor: \"pointer\",\n    transition: \"background-color 0.2s ease\",\n    borderLeft: \"3px solid transparent\"\n  },\n  historyThumbnail: {\n    width: \"32px\",\n    height: \"32px\",\n    borderRadius: \"4px\",\n    objectFit: \"cover\"\n  },\n  historyItemContent: {\n    overflow: \"hidden\"\n  },\n  historyItemTitle: {\n    fontSize: \"0.875rem\",\n    fontWeight: \"500\",\n    whiteSpace: \"nowrap\",\n    overflow: \"hidden\",\n    textOverflow: \"ellipsis\"\n  },\n  historyItemDate: {\n    fontSize: \"0.75rem\",\n    opacity: 0.6,\n    marginTop: \"2px\"\n  },\n  mainWrapper: {\n    flex: 1,\n    display: \"flex\",\n    flexDirection: \"column\",\n    minHeight: \"100vh\",\n    transition: \"margin-left 0.3s ease\"\n  },\n  header: {\n    display: \"flex\",\n    justifyContent: \"space-between\",\n    alignItems: \"center\",\n    padding: \"1rem 1.5rem\",\n    borderBottom: \"1px solid rgba(148, 163, 184, 0.15)\"\n  },\n  headerLeft: {\n    display: \"flex\",\n    alignItems: \"center\",\n    gap: \"12px\"\n  },\n  toggleHistoryButton: {\n    background: \"none\",\n    border: \"none\",\n    color: \"inherit\",\n    cursor: \"pointer\",\n    display: \"flex\",\n    alignItems: \"center\",\n    justifyContent: \"center\",\n    padding: \"6px\",\n    borderRadius: \"6px\",\n    transition: \"background-color 0.2s ease\"\n  },\n  title: {\n    fontSize: \"1.25rem\",\n    fontWeight: \"600\",\n    margin: 0,\n    letterSpacing: \"-0.025em\"\n  },\n  headerRight: {\n    display: \"flex\",\n    alignItems: \"center\",\n    gap: \"12px\"\n  },\n  iconButton: {\n    background: \"transparent\",\n    border: \"1px solid\",\n    borderColor: \"rgba(148, 163, 184, 0.2)\",\n    borderRadius: \"6px\",\n    width: \"36px\",\n    height: \"36px\",\n    display: \"flex\",\n    alignItems: \"center\",\n    justifyContent: \"center\",\n    cursor: \"pointer\",\n    color: \"inherit\",\n    transition: \"all 0.2s ease\"\n  },\n  loginButton: {\n    background: \"transparent\",\n    border: \"1px solid\",\n    borderColor: \"rgba(96, 165, 250, 0.3)\",\n    color: \"#60a5fa\",\n    borderRadius: \"6px\",\n    padding: \"0.5rem 1rem\",\n    fontSize: \"0.875rem\",\n    fontWeight: \"500\",\n    cursor: \"pointer\",\n    display: \"flex\",\n    alignItems: \"center\",\n    gap: \"8px\",\n    transition: \"all 0.2s ease\"\n  },\n  languageButton: {\n    background: \"transparent\",\n    border: \"1px solid\",\n    borderColor: \"rgba(148, 163, 184, 0.2)\",\n    borderRadius: \"6px\",\n    width: \"36px\",\n    height: \"36px\",\n    display: \"flex\",\n    alignItems: \"center\",\n    justifyContent: \"center\",\n    cursor: \"pointer\",\n    color: \"inherit\",\n    fontSize: \"0.75rem\",\n    fontWeight: \"600\",\n    transition: \"all 0.2s ease\"\n  },\n  tabs: {\n    display: \"flex\",\n    gap: \"1.5rem\",\n    padding: \"0 1.5rem\",\n    borderBottom: \"1px solid rgba(148, 163, 184, 0.15)\"\n  },\n  tabButton: {\n    background: \"none\",\n    border: \"none\",\n    padding: \"1rem 0.25rem\",\n    fontSize: \"0.875rem\",\n    cursor: \"pointer\",\n    fontWeight: \"500\",\n    transition: \"all 0.2s ease\",\n    display: \"flex\",\n    alignItems: \"center\",\n    gap: \"8px\"\n  },\n  contentSection: {\n    flex: 1,\n    display: \"flex\",\n    flexDirection: \"column\"\n  },\n  contentHeader: {\n    padding: \"1.5rem 2rem 1rem\"\n  },\n  contentTitle: {\n    fontSize: \"1.25rem\",\n    fontWeight: \"600\",\n    margin: \"0 0 0.5rem\",\n    letterSpacing: \"-0.025em\"\n  },\n  contentSubtitle: {\n    fontSize: \"0.875rem\",\n    opacity: 0.8,\n    margin: 0,\n    fontWeight: \"400\"\n  },\n  contentWrapper: {\n    flex: 1,\n    padding: \"1rem 2rem 2rem\",\n    overflowY: \"auto\"\n  },\n  footer: {\n    padding: \"1rem 2rem\",\n    borderTop: \"1px solid rgba(148, 163, 184, 0.15)\",\n    fontSize: \"0.75rem\",\n    opacity: 0.6,\n    textAlign: \"center\"\n  }\n};", "map": {"version": 3, "names": ["styles", "container", "fontFamily", "minHeight", "display", "transition", "historySidebar", "height", "position", "left", "top", "overflowY", "backgroundColor", "zIndex", "<PERSON><PERSON><PERSON><PERSON>", "padding", "borderBottom", "flexDirection", "gap", "historyTitle", "fontSize", "fontWeight", "margin", "newProjectButton", "alignItems", "justifyContent", "color", "border", "borderColor", "borderRadius", "cursor", "width", "historyList", "historyItem", "borderLeft", "historyThumbnail", "objectFit", "historyItemContent", "overflow", "historyItemTitle", "whiteSpace", "textOverflow", "historyItemDate", "opacity", "marginTop", "mainWrapper", "flex", "header", "headerLeft", "to<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "background", "title", "letterSpacing", "headerRight", "iconButton", "loginButton", "languageButton", "tabs", "tabButton", "contentSection", "contentHeader", "contentTitle", "contentSubtitle", "contentWrapper", "footer", "borderTop", "textAlign"], "sources": ["C:/Users/<USER>/FixTorchUMLDGM/uml-detector/src/AppStyles.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { CSSProperties } from 'react';\r\n\r\n// Define styles as a constant that can be imported by the main component\r\nexport const styles: Record<string, CSSProperties> = {\r\n  container: {\r\n    fontFamily: \"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif\",\r\n    minHeight: \"100vh\",\r\n    display: \"flex\",\r\n    transition: \"background-color 0.3s ease, color 0.3s ease\"\r\n  },\r\n  historySidebar: {\r\n    height: \"100vh\",\r\n    position: \"fixed\",\r\n    left: 0,\r\n    top: 0,\r\n    overflowY: \"auto\" as const,\r\n    transition: \"width 0.3s ease\",\r\n    backgroundColor: \"inherit\",\r\n    zIndex: 10\r\n  },\r\n  historyHeader: {\r\n    padding: \"1.25rem 1rem\",\r\n    borderBottom: \"1px solid rgba(148, 163, 184, 0.15)\",\r\n    display: \"flex\",\r\n    flexDirection: \"column\" as const,\r\n    gap: \"12px\"\r\n  },\r\n  historyTitle: {\r\n    fontSize: \"1rem\",\r\n    fontWeight: \"600\",\r\n    margin: 0\r\n  },\r\n  newProjectButton: {\r\n    display: \"flex\",\r\n    alignItems: \"center\",\r\n    justifyContent: \"center\",\r\n    gap: \"6px\",\r\n    padding: \"0.5rem\",\r\n    backgroundColor: \"rgba(59, 130, 246, 0.1)\",\r\n    color: \"#60a5fa\",\r\n    border: \"1px solid\",\r\n    borderColor: \"rgba(96, 165, 250, 0.2)\",\r\n    borderRadius: \"6px\",\r\n    fontSize: \"0.875rem\",\r\n    fontWeight: \"500\",\r\n    cursor: \"pointer\",\r\n    width: \"100%\",\r\n    transition: \"all 0.2s ease\",\r\n  },\r\n  historyList: {\r\n    padding: \"0.5rem 0\"\r\n  },\r\n  historyItem: {\r\n    padding: \"0.75rem 1rem\",\r\n    display: \"flex\",\r\n    alignItems: \"center\",\r\n    gap: \"10px\",\r\n    cursor: \"pointer\",\r\n    transition: \"background-color 0.2s ease\",\r\n    borderLeft: \"3px solid transparent\"\r\n  },\r\n  historyThumbnail: {\r\n    width: \"32px\",\r\n    height: \"32px\",\r\n    borderRadius: \"4px\",\r\n    objectFit: \"cover\" as const\r\n  },\r\n  historyItemContent: {\r\n    overflow: \"hidden\"\r\n  },\r\n  historyItemTitle: {\r\n    fontSize: \"0.875rem\",\r\n    fontWeight: \"500\",\r\n    whiteSpace: \"nowrap\" as const,\r\n    overflow: \"hidden\",\r\n    textOverflow: \"ellipsis\"\r\n  },\r\n  historyItemDate: {\r\n    fontSize: \"0.75rem\",\r\n    opacity: 0.6,\r\n    marginTop: \"2px\"\r\n  },\r\n  mainWrapper: {\r\n    flex: 1,\r\n    display: \"flex\",\r\n    flexDirection: \"column\" as const,\r\n    minHeight: \"100vh\",\r\n    transition: \"margin-left 0.3s ease\"\r\n  },\r\n  header: {\r\n    display: \"flex\",\r\n    justifyContent: \"space-between\",\r\n    alignItems: \"center\",\r\n    padding: \"1rem 1.5rem\",\r\n    borderBottom: \"1px solid rgba(148, 163, 184, 0.15)\"\r\n  },\r\n  headerLeft: {\r\n    display: \"flex\",\r\n    alignItems: \"center\",\r\n    gap: \"12px\"\r\n  },\r\n  toggleHistoryButton: {\r\n    background: \"none\",\r\n    border: \"none\",\r\n    color: \"inherit\",\r\n    cursor: \"pointer\",\r\n    display: \"flex\",\r\n    alignItems: \"center\",\r\n    justifyContent: \"center\",\r\n    padding: \"6px\",\r\n    borderRadius: \"6px\",\r\n    transition: \"background-color 0.2s ease\"\r\n  },\r\n  title: {\r\n    fontSize: \"1.25rem\",\r\n    fontWeight: \"600\",\r\n    margin: 0,\r\n    letterSpacing: \"-0.025em\"\r\n  },\r\n  headerRight: {\r\n    display: \"flex\",\r\n    alignItems: \"center\",\r\n    gap: \"12px\"\r\n  },\r\n  iconButton: {\r\n    background: \"transparent\",\r\n    border: \"1px solid\",\r\n    borderColor: \"rgba(148, 163, 184, 0.2)\",\r\n    borderRadius: \"6px\",\r\n    width: \"36px\",\r\n    height: \"36px\",\r\n    display: \"flex\",\r\n    alignItems: \"center\",\r\n    justifyContent: \"center\",\r\n    cursor: \"pointer\",\r\n    color: \"inherit\",\r\n    transition: \"all 0.2s ease\"\r\n  },\r\n  loginButton: {\r\n    background: \"transparent\",\r\n    border: \"1px solid\",\r\n    borderColor: \"rgba(96, 165, 250, 0.3)\",\r\n    color: \"#60a5fa\",\r\n    borderRadius: \"6px\",\r\n    padding: \"0.5rem 1rem\",\r\n    fontSize: \"0.875rem\",\r\n    fontWeight: \"500\",\r\n    cursor: \"pointer\",\r\n    display: \"flex\",\r\n    alignItems: \"center\",\r\n    gap: \"8px\",\r\n    transition: \"all 0.2s ease\"\r\n  },\r\n  languageButton: {\r\n    background: \"transparent\",\r\n    border: \"1px solid\",\r\n    borderColor: \"rgba(148, 163, 184, 0.2)\",\r\n    borderRadius: \"6px\",\r\n    width: \"36px\",\r\n    height: \"36px\",\r\n    display: \"flex\",\r\n    alignItems: \"center\",\r\n    justifyContent: \"center\",\r\n    cursor: \"pointer\",\r\n    color: \"inherit\",\r\n    fontSize: \"0.75rem\",\r\n    fontWeight: \"600\",\r\n    transition: \"all 0.2s ease\"\r\n  },\r\n  tabs: {\r\n    display: \"flex\",\r\n    gap: \"1.5rem\",\r\n    padding: \"0 1.5rem\",\r\n    borderBottom: \"1px solid rgba(148, 163, 184, 0.15)\"\r\n  },\r\n  tabButton: {\r\n    background: \"none\",\r\n    border: \"none\",\r\n    padding: \"1rem 0.25rem\",\r\n    fontSize: \"0.875rem\",\r\n    cursor: \"pointer\",\r\n    fontWeight: \"500\",\r\n    transition: \"all 0.2s ease\",\r\n    display: \"flex\",\r\n    alignItems: \"center\",\r\n    gap: \"8px\"\r\n  },\r\n  contentSection: {\r\n    flex: 1,\r\n    display: \"flex\",\r\n    flexDirection: \"column\" as const\r\n  },\r\n  contentHeader: {\r\n    padding: \"1.5rem 2rem 1rem\",\r\n  },\r\n  contentTitle: {\r\n    fontSize: \"1.25rem\",\r\n    fontWeight: \"600\",\r\n    margin: \"0 0 0.5rem\",\r\n    letterSpacing: \"-0.025em\"\r\n  },\r\n  contentSubtitle: {\r\n    fontSize: \"0.875rem\",\r\n    opacity: 0.8,\r\n    margin: 0,\r\n    fontWeight: \"400\"\r\n  },\r\n  contentWrapper: {\r\n    flex: 1,\r\n    padding: \"1rem 2rem 2rem\",\r\n    overflowY: \"auto\" as const\r\n  },\r\n  footer: {\r\n    padding: \"1rem 2rem\",\r\n    borderTop: \"1px solid rgba(148, 163, 184, 0.15)\",\r\n    fontSize: \"0.75rem\",\r\n    opacity: 0.6,\r\n    textAlign: \"center\" as const\r\n  }\r\n};"], "mappings": "AAGA;AACA,OAAO,MAAMA,MAAqC,GAAG;EACnDC,SAAS,EAAE;IACTC,UAAU,EAAE,4EAA4E;IACxFC,SAAS,EAAE,OAAO;IAClBC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE;EACd,CAAC;EACDC,cAAc,EAAE;IACdC,MAAM,EAAE,OAAO;IACfC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,CAAC;IACPC,GAAG,EAAE,CAAC;IACNC,SAAS,EAAE,MAAe;IAC1BN,UAAU,EAAE,iBAAiB;IAC7BO,eAAe,EAAE,SAAS;IAC1BC,MAAM,EAAE;EACV,CAAC;EACDC,aAAa,EAAE;IACbC,OAAO,EAAE,cAAc;IACvBC,YAAY,EAAE,qCAAqC;IACnDZ,OAAO,EAAE,MAAM;IACfa,aAAa,EAAE,QAAiB;IAChCC,GAAG,EAAE;EACP,CAAC;EACDC,YAAY,EAAE;IACZC,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjBC,MAAM,EAAE;EACV,CAAC;EACDC,gBAAgB,EAAE;IAChBnB,OAAO,EAAE,MAAM;IACfoB,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBP,GAAG,EAAE,KAAK;IACVH,OAAO,EAAE,QAAQ;IACjBH,eAAe,EAAE,yBAAyB;IAC1Cc,KAAK,EAAE,SAAS;IAChBC,MAAM,EAAE,WAAW;IACnBC,WAAW,EAAE,yBAAyB;IACtCC,YAAY,EAAE,KAAK;IACnBT,QAAQ,EAAE,UAAU;IACpBC,UAAU,EAAE,KAAK;IACjBS,MAAM,EAAE,SAAS;IACjBC,KAAK,EAAE,MAAM;IACb1B,UAAU,EAAE;EACd,CAAC;EACD2B,WAAW,EAAE;IACXjB,OAAO,EAAE;EACX,CAAC;EACDkB,WAAW,EAAE;IACXlB,OAAO,EAAE,cAAc;IACvBX,OAAO,EAAE,MAAM;IACfoB,UAAU,EAAE,QAAQ;IACpBN,GAAG,EAAE,MAAM;IACXY,MAAM,EAAE,SAAS;IACjBzB,UAAU,EAAE,4BAA4B;IACxC6B,UAAU,EAAE;EACd,CAAC;EACDC,gBAAgB,EAAE;IAChBJ,KAAK,EAAE,MAAM;IACbxB,MAAM,EAAE,MAAM;IACdsB,YAAY,EAAE,KAAK;IACnBO,SAAS,EAAE;EACb,CAAC;EACDC,kBAAkB,EAAE;IAClBC,QAAQ,EAAE;EACZ,CAAC;EACDC,gBAAgB,EAAE;IAChBnB,QAAQ,EAAE,UAAU;IACpBC,UAAU,EAAE,KAAK;IACjBmB,UAAU,EAAE,QAAiB;IAC7BF,QAAQ,EAAE,QAAQ;IAClBG,YAAY,EAAE;EAChB,CAAC;EACDC,eAAe,EAAE;IACftB,QAAQ,EAAE,SAAS;IACnBuB,OAAO,EAAE,GAAG;IACZC,SAAS,EAAE;EACb,CAAC;EACDC,WAAW,EAAE;IACXC,IAAI,EAAE,CAAC;IACP1C,OAAO,EAAE,MAAM;IACfa,aAAa,EAAE,QAAiB;IAChCd,SAAS,EAAE,OAAO;IAClBE,UAAU,EAAE;EACd,CAAC;EACD0C,MAAM,EAAE;IACN3C,OAAO,EAAE,MAAM;IACfqB,cAAc,EAAE,eAAe;IAC/BD,UAAU,EAAE,QAAQ;IACpBT,OAAO,EAAE,aAAa;IACtBC,YAAY,EAAE;EAChB,CAAC;EACDgC,UAAU,EAAE;IACV5C,OAAO,EAAE,MAAM;IACfoB,UAAU,EAAE,QAAQ;IACpBN,GAAG,EAAE;EACP,CAAC;EACD+B,mBAAmB,EAAE;IACnBC,UAAU,EAAE,MAAM;IAClBvB,MAAM,EAAE,MAAM;IACdD,KAAK,EAAE,SAAS;IAChBI,MAAM,EAAE,SAAS;IACjB1B,OAAO,EAAE,MAAM;IACfoB,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBV,OAAO,EAAE,KAAK;IACdc,YAAY,EAAE,KAAK;IACnBxB,UAAU,EAAE;EACd,CAAC;EACD8C,KAAK,EAAE;IACL/B,QAAQ,EAAE,SAAS;IACnBC,UAAU,EAAE,KAAK;IACjBC,MAAM,EAAE,CAAC;IACT8B,aAAa,EAAE;EACjB,CAAC;EACDC,WAAW,EAAE;IACXjD,OAAO,EAAE,MAAM;IACfoB,UAAU,EAAE,QAAQ;IACpBN,GAAG,EAAE;EACP,CAAC;EACDoC,UAAU,EAAE;IACVJ,UAAU,EAAE,aAAa;IACzBvB,MAAM,EAAE,WAAW;IACnBC,WAAW,EAAE,0BAA0B;IACvCC,YAAY,EAAE,KAAK;IACnBE,KAAK,EAAE,MAAM;IACbxB,MAAM,EAAE,MAAM;IACdH,OAAO,EAAE,MAAM;IACfoB,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBK,MAAM,EAAE,SAAS;IACjBJ,KAAK,EAAE,SAAS;IAChBrB,UAAU,EAAE;EACd,CAAC;EACDkD,WAAW,EAAE;IACXL,UAAU,EAAE,aAAa;IACzBvB,MAAM,EAAE,WAAW;IACnBC,WAAW,EAAE,yBAAyB;IACtCF,KAAK,EAAE,SAAS;IAChBG,YAAY,EAAE,KAAK;IACnBd,OAAO,EAAE,aAAa;IACtBK,QAAQ,EAAE,UAAU;IACpBC,UAAU,EAAE,KAAK;IACjBS,MAAM,EAAE,SAAS;IACjB1B,OAAO,EAAE,MAAM;IACfoB,UAAU,EAAE,QAAQ;IACpBN,GAAG,EAAE,KAAK;IACVb,UAAU,EAAE;EACd,CAAC;EACDmD,cAAc,EAAE;IACdN,UAAU,EAAE,aAAa;IACzBvB,MAAM,EAAE,WAAW;IACnBC,WAAW,EAAE,0BAA0B;IACvCC,YAAY,EAAE,KAAK;IACnBE,KAAK,EAAE,MAAM;IACbxB,MAAM,EAAE,MAAM;IACdH,OAAO,EAAE,MAAM;IACfoB,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBK,MAAM,EAAE,SAAS;IACjBJ,KAAK,EAAE,SAAS;IAChBN,QAAQ,EAAE,SAAS;IACnBC,UAAU,EAAE,KAAK;IACjBhB,UAAU,EAAE;EACd,CAAC;EACDoD,IAAI,EAAE;IACJrD,OAAO,EAAE,MAAM;IACfc,GAAG,EAAE,QAAQ;IACbH,OAAO,EAAE,UAAU;IACnBC,YAAY,EAAE;EAChB,CAAC;EACD0C,SAAS,EAAE;IACTR,UAAU,EAAE,MAAM;IAClBvB,MAAM,EAAE,MAAM;IACdZ,OAAO,EAAE,cAAc;IACvBK,QAAQ,EAAE,UAAU;IACpBU,MAAM,EAAE,SAAS;IACjBT,UAAU,EAAE,KAAK;IACjBhB,UAAU,EAAE,eAAe;IAC3BD,OAAO,EAAE,MAAM;IACfoB,UAAU,EAAE,QAAQ;IACpBN,GAAG,EAAE;EACP,CAAC;EACDyC,cAAc,EAAE;IACdb,IAAI,EAAE,CAAC;IACP1C,OAAO,EAAE,MAAM;IACfa,aAAa,EAAE;EACjB,CAAC;EACD2C,aAAa,EAAE;IACb7C,OAAO,EAAE;EACX,CAAC;EACD8C,YAAY,EAAE;IACZzC,QAAQ,EAAE,SAAS;IACnBC,UAAU,EAAE,KAAK;IACjBC,MAAM,EAAE,YAAY;IACpB8B,aAAa,EAAE;EACjB,CAAC;EACDU,eAAe,EAAE;IACf1C,QAAQ,EAAE,UAAU;IACpBuB,OAAO,EAAE,GAAG;IACZrB,MAAM,EAAE,CAAC;IACTD,UAAU,EAAE;EACd,CAAC;EACD0C,cAAc,EAAE;IACdjB,IAAI,EAAE,CAAC;IACP/B,OAAO,EAAE,gBAAgB;IACzBJ,SAAS,EAAE;EACb,CAAC;EACDqD,MAAM,EAAE;IACNjD,OAAO,EAAE,WAAW;IACpBkD,SAAS,EAAE,qCAAqC;IAChD7C,QAAQ,EAAE,SAAS;IACnBuB,OAAO,EAAE,GAAG;IACZuB,SAAS,EAAE;EACb;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}