import React, { useEffect, useState } from "react";
import ImageUploader from "./components/imageUp/ImageUploader";
import DetectionArrow from "./components/DetectionArrow";
import UMLDiagrameExtractor from "./components/UmlDiagr/UMLDiagrameExtractor";
import Documentation from "./components/Documentation";
import { styles } from "./AppStyles";
import Login from "./components/Loginsignup/Login";
import UserProfile from "./components/Loginsignup/UserProfile";
import { AuthProvider, useAuth } from "./context/AuthContext";
import { HistoryProvider } from "./context/HistoryContext";
import { LanguageProvider, useLanguage } from "./context/LanguageContext";
import HistorySidebar from "./components/History/HistorySidebar";

const AppContent: React.FC = () => {
  const [activeTab, setActiveTab] = useState("upload");
  const [darkMode, setDarkMode] = useState(true);
  const [annotatedImageUrl, setAnnotatedImageUrl] = useState<string | null>(null);
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [extractedText, setExtractedText] = useState<string>("");
  const [textUrl, setTextUrl] = useState<string | null>(null);
  const [isHistoryOpen, setIsHistoryOpen] = useState(true);
  const [showLogin, setShowLogin] = useState(false);

  const { currentUser } = useAuth();
  const { language, setLanguage, t } = useLanguage();


  // Dans votre composant principal
useEffect(() => {
  const handleLoadHistoryItem = (event: any) => {
    const item = event.detail;
    // Mettre à jour l'état avec les données de l'élément d'historique
    if (item) {
      setImageUrl(item.originalImageUrl);
      setAnnotatedImageUrl(item.annotatedImageUrl || null);
      setExtractedText(item.extractedText);
      setTextUrl(null); // Pas besoin de l'URL du texte car nous avons déjà le texte extrait
      
      // Passer à l'onglet d'upload pour afficher l'image chargée
      setActiveTab("upload");
    }
  };

  window.addEventListener('loadHistoryItem', handleLoadHistoryItem);
  return () => {
    window.removeEventListener('loadHistoryItem', handleLoadHistoryItem);
  };
}, []);


  const handleAnalysisComplete = (imageUrl: string, text: string, textFileUrl: string | null) => {
    setImageUrl(imageUrl);
    
    // For annotated image, use server URL directly
    const annotatedUrl = "http://127.0.0.1:8000/annotated_image.jpg";
    setAnnotatedImageUrl(annotatedUrl);
    
    setExtractedText(text);
    setTextUrl(textFileUrl);
  };

  const handleViewAnnotatedImage = () => {
    setActiveTab("relations");
  };
  
  const handleNavigateToUMLExtractor = () => {
    setActiveTab("text");
  };

  const handleUpdateExtractedText = (newText: string) => {
    console.log("Mise à jour du texte extrait dans App.tsx");
    console.log("Nouveau texte:", newText.substring(0, 100) + "...");
    setExtractedText(newText);
    
    // Si vous utilisez ce texte ailleurs, assurez-vous qu'il est également mis à jour
    // Par exemple, si vous le passez à d'autres composants
  };

  const renderContent = () => {
    switch (activeTab) {
      case "relations":
        return (
          <DetectionArrow
            darkMode={darkMode}
            annotatedImage={annotatedImageUrl}
          />
        );
      case "text":
        return <UMLDiagrameExtractor 
                 darkMode={darkMode} 
                 textToConvert={extractedText} 
               />;
      case "doc":
        return <Documentation darkMode={darkMode} />;
      default:
        return (
          <ImageUploader 
            darkMode={darkMode} 
            onAnalysisComplete={handleAnalysisComplete} 
            savedImageUrl={imageUrl}
            savedExtractedText={extractedText}
            savedTextUrl={textUrl}
            onViewAnnotatedImage={handleViewAnnotatedImage}
            onNavigateToUMLExtractor={handleNavigateToUMLExtractor}
            onUpdateExtractedText={handleUpdateExtractedText}
          />
        );
    }
  };

  // Remplacer le mock historyItems par le vrai système

  return (
    <div style={{ 
      ...styles.container, 
      backgroundColor: darkMode ? "#121820" : "#f0f4f8",
      color: darkMode ? "#e2e8f0" : "#334155"
    }}>
      {showLogin && <Login darkMode={darkMode} onClose={() => setShowLogin(false)} />}
      
      {/* Remplacer l'ancienne sidebar par le nouveau composant */}
      <HistorySidebar 
        darkMode={darkMode} 
        isOpen={isHistoryOpen} 
        onToggle={() => setIsHistoryOpen(!isHistoryOpen)} 
      />

      <div style={{
        ...styles.mainWrapper,
        marginLeft: isHistoryOpen ? "240px" : "0"
      }}>
        <div style={styles.header}>
          <div style={styles.headerLeft}>
            <button 
              onClick={() => setIsHistoryOpen(!isHistoryOpen)} 
              style={styles.toggleHistoryButton}
            >
              {isHistoryOpen ? (
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M15 18l-6-6 6-6"></path>
                </svg>
              ) : (
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M9 18l6-6-6-6"></path>
                </svg>
              )}
            </button>
            <h1 style={styles.title}>{t('app.title')}</h1>
          </div>
          <div style={styles.headerRight}>
            <button
              onClick={() => setDarkMode(!darkMode)}
              style={styles.iconButton}
            >
              {darkMode ? (
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <circle cx="12" cy="12" r="5"></circle>
                  <line x1="12" y1="1" x2="12" y2="3"></line>
                  <line x1="12" y1="21" x2="12" y2="23"></line>
                  <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
                  <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
                  <line x1="1" y1="12" x2="3" y2="12"></line>
                  <line x1="21" y1="12" x2="23" y2="12"></line>
                  <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
                  <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
                </svg>
              ) : (
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path>
                </svg>
              )}
            </button>

            {/* Bouton de changement de langue */}
            <button
              onClick={() => setLanguage(language === 'fr' ? 'en' : 'fr')}
              style={styles.languageButton}
              title={t('language.switch')}
            >
              {t('language.current')}
            </button>
            
            {/* Conditional rendering based on auth state */}
            {currentUser ? (
              <UserProfile darkMode={darkMode} />
            ) : (
              <button 
                onClick={() => setShowLogin(true)} 
                style={styles.loginButton}
              >
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                  <circle cx="12" cy="7" r="4"></circle>
                </svg>
                {t('button.login')}
              </button>
            )}
          </div>
        </div>

        <div style={styles.tabs}>
          <button 
            onClick={() => setActiveTab("upload")} 
            style={{
              ...styles.tabButton,
              borderBottom: activeTab === "upload" ? `2px solid ${darkMode ? "#60a5fa" : "#3b82f6"}` : "none",
              color: activeTab === "upload" ? (darkMode ? "#60a5fa" : "#3b82f6") : (darkMode ? "#94a3b8" : "#64748b")
            }}
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M4 16L8.586 11.414C8.961 11.039 9.47 10.828 10 10.828C10.53 10.828 11.039 11.039 11.414 11.414L16 16M14 14L15.586 12.414C15.961 12.039 16.47 11.828 17 11.828C17.53 11.828 18.039 12.039 18.414 12.414L20 14M14 8H14.01M6 20H18C19.105 20 20 19.105 20 18V6C20 4.895 19.105 4 18 4H6C4.895 4 4 4.895 4 6V18C4 19.105 4.895 20 6 20Z" />
            </svg>
            {t('tab.upload')}
          </button>
          
          <button 
            onClick={() => setActiveTab("relations")} 
            style={{
              ...styles.tabButton,
              borderBottom: activeTab === "relations" ? `2px solid ${darkMode ? "#60a5fa" : "#3b82f6"}` : "none",
              color: activeTab === "relations" ? (darkMode ? "#60a5fa" : "#3b82f6") : (darkMode ? "#94a3b8" : "#64748b")
            }}
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M9 3V5M15 3V5M9 19V21M15 19V21M5 9H3M5 15H3M21 9H19M21 15H19M7 19H17C18.105 19 19 18.105 19 17V7C19 5.895 18.105 5 17 5H7C5.895 5 5 5.895 5 7V17C5 18.105 5.895 19 7 19Z" />
            </svg>
            {t('tab.relations')}
          </button>
          
          <button 
            onClick={() => setActiveTab("text")} 
            style={{
              ...styles.tabButton,
              borderBottom: activeTab === "text" ? `2px solid ${darkMode ? "#60a5fa" : "#3b82f6"}` : "none",
              color: activeTab === "text" ? (darkMode ? "#60a5fa" : "#3b82f6") : (darkMode ? "#94a3b8" : "#64748b")
            }}
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M12 3H5C4.46957 3 3.96086 3.21071 3.58579 3.58579C3.21071 3.96086 3 4.46957 3 5V19C3 19.5304 3.21071 20.0391 3.58579 20.4142C3.96086 20.7893 4.46957 21 5 21H19C19.5304 21 20.0391 20.7893 20.4142 20.4142C20.7893 20.0391 21 19.5304 21 19V12M19 3V8M16 5H22M9 15H15M9 9H15" />
            </svg>
            {t('tab.text')}
          </button>
          
          <button 
            onClick={() => setActiveTab("doc")} 
            style={{
              ...styles.tabButton,
              borderBottom: activeTab === "doc" ? `2px solid ${darkMode ? "#60a5fa" : "#3b82f6"}` : "none",
              color: activeTab === "doc" ? (darkMode ? "#60a5fa" : "#3b82f6") : (darkMode ? "#94a3b8" : "#64748b")
            }}
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2ZM12 11C11.4477 11 11 11.4477 11 12V17C11 17.5523 11.4477 18 12 18C12.5523 18 13 17.5523 13 17V12C13 11.4477 12.5523 11 12 11ZM12 9C12.5523 9 13 8.55228 13 8C13 7.44772 12.5523 7 12 7C11.4477 7 11 7.44772 11 8C11 8.55228 11.4477 9 12 9Z" />
            </svg>
            {t('tab.documentation')}
          </button>
        </div>

        <div style={styles.contentSection}>
          <div style={styles.contentHeader}>
            <h2 style={styles.contentTitle}>
              {activeTab === "upload" && t('content.upload.title')}
              {activeTab === "relations" && t('content.relations.title')}
              {activeTab === "text" && t('content.text.title')}
              {activeTab === "doc" && t('content.documentation.title')}
            </h2>
            <p style={styles.contentSubtitle}>
              {activeTab === "upload" && t('content.upload.subtitle')}
              {activeTab === "relations" && t('content.relations.subtitle')}
              {activeTab === "text" && t('content.text.subtitle')}
              {activeTab === "doc" && t('content.documentation.subtitle')}
            </p>
          </div>
          
          <div style={styles.contentWrapper}>
            {renderContent()}
          </div>
        </div>

        <div style={styles.footer}>
          <p>{t('footer.text')}</p>
        </div>
      </div>
    </div>
  );
};

// Wrapper avec les providers
const App: React.FC = () => {
  return (
    <LanguageProvider>
      <AuthProvider>
        <HistoryProvider>
          <AppContent />
        </HistoryProvider>
      </AuthProvider>
    </LanguageProvider>
  );
};

export default App;
