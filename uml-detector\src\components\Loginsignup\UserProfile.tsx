import React, { useState } from 'react';
import { User, LogOut } from 'lucide-react';
import { useAuth } from '../../context/AuthContext';
import { useLanguage } from '../../context/LanguageContext';

interface UserProfileProps {
  darkMode: boolean;
}

const UserProfile: React.FC<UserProfileProps> = ({ darkMode }) => {
  const { currentUser, logout } = useAuth();
  const { t } = useLanguage();
  const [showDropdown, setShowDropdown] = useState<boolean>(false);

  const styles = {
    container: {
      position: 'relative' as const,
      display: 'flex',
      alignItems: 'center',
    },
    userButton: {
      display: 'flex',
      alignItems: 'center',
      gap: '0.5rem',
      padding: '0.5rem',
      borderRadius: '9999px',
      border: 'none',
      backgroundColor: 'transparent',
      cursor: 'pointer',
      color: darkMode ? '#e2e8f0' : '#334155',
      transition: 'background-color 0.2s',
      ':hover': {
        backgroundColor: darkMode ? 'rgba(148, 163, 184, 0.1)' : 'rgba(226, 232, 240, 0.5)',
      },
    },
    avatar: {
      width: '32px',
      height: '32px',
      borderRadius: '9999px',
      objectFit: 'cover' as const,
      border: `1px solid ${darkMode ? 'rgba(148, 163, 184, 0.2)' : 'rgba(203, 213, 225, 0.8)'}`,
    },
    placeholder: {
      width: '32px',
      height: '32px',
      borderRadius: '9999px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: darkMode ? '#334155' : '#e2e8f0',
      color: darkMode ? '#e2e8f0' : '#334155',
    },
    userName: {
      fontWeight: 500,
      fontSize: '0.875rem',
      marginLeft: '0.5rem',
      display: {
        xs: 'none',
        sm: 'block',
      },
    },
    dropdown: {
      position: 'absolute' as const,
      top: '100%',
      right: 0,
      marginTop: '0.5rem',
      width: '200px',
      backgroundColor: darkMode ? '#1e293b' : '#ffffff',
      borderRadius: '0.375rem',
      boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
      zIndex: 50,
      border: `1px solid ${darkMode ? 'rgba(148, 163, 184, 0.2)' : 'rgba(203, 213, 225, 0.8)'}`,
      overflow: 'hidden',
    },
    dropdownHeader: {
      padding: '1rem',
      borderBottom: `1px solid ${darkMode ? 'rgba(148, 163, 184, 0.2)' : 'rgba(203, 213, 225, 0.8)'}`,
      display: 'flex',
      flexDirection: 'column' as const,
      alignItems: 'center',
    },
    dropdownAvatar: {
      width: '48px',
      height: '48px',
      borderRadius: '9999px',
      objectFit: 'cover' as const,
      marginBottom: '0.5rem',
      border: `1px solid ${darkMode ? 'rgba(148, 163, 184, 0.2)' : 'rgba(203, 213, 225, 0.8)'}`,
    },
    dropdownUserName: {
      fontWeight: 600,
      fontSize: '0.875rem',
      color: darkMode ? '#e2e8f0' : '#334155',
    },
    dropdownEmail: {
      fontSize: '0.75rem',
      color: darkMode ? '#94a3b8' : '#64748b',
      marginTop: '0.25rem',
    },
    dropdownItems: {
      padding: '0.5rem',
    },
    dropdownItem: {
      display: 'flex',
      alignItems: 'center',
      padding: '0.75rem',
      borderRadius: '0.375rem',
      fontSize: '0.875rem',
      color: darkMode ? '#e2e8f0' : '#334155',
      cursor: 'pointer',
      transition: 'background-color 0.2s',
      ':hover': {
        backgroundColor: darkMode ? 'rgba(148, 163, 184, 0.1)' : 'rgba(226, 232, 240, 0.5)',
      },
    },
    logoutButton: {
      display: 'flex',
      alignItems: 'center',
      gap: '0.5rem',
      width: '100%',
      padding: '0.75rem',
      borderRadius: '0.375rem',
      fontSize: '0.875rem',
      color: darkMode ? '#e2e8f0' : '#334155',
      backgroundColor: 'transparent',
      border: 'none',
      cursor: 'pointer',
      textAlign: 'left' as const,
      transition: 'background-color 0.2s',
    },
  };

  const handleLogout = async () => {
    try {
      await logout();
      setShowDropdown(false);
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  // Default avatar if no photo URL
  const defaultAvatar = (
    <div style={styles.placeholder}>
      <User size={20} />
    </div>
  );

  if (!currentUser) {
    return null;
  }

  return (
    <div style={styles.container}>
      <button 
        style={styles.userButton}
        onClick={() => setShowDropdown(!showDropdown)}
      >
        {currentUser.photoURL ? (
          <img 
            src={currentUser.photoURL} 
            alt={currentUser.displayName || 'User'} 
            style={styles.avatar}
          />
        ) : defaultAvatar}
        <span 
  style={{
    fontWeight: 500,
    fontSize: '0.875rem',
    marginLeft: '0.5rem',
    display: 'block', // ou 'none'
  }}
>
  {currentUser.displayName || currentUser.email?.split('@')[0]}
</span>

      </button>

      {showDropdown && (
        <div style={styles.dropdown}>
          <div style={styles.dropdownHeader}>
            {currentUser.photoURL ? (
              <img 
                src={currentUser.photoURL} 
                alt={currentUser.displayName || 'User'} 
                style={styles.dropdownAvatar}
              />
            ) : (
              <div style={{...styles.placeholder, width: '48px', height: '48px'}}>
                <User size={24} />
              </div>
            )}
            <div style={styles.dropdownUserName}>
              {currentUser.displayName || currentUser.email?.split('@')[0]}
            </div>
            <div style={styles.dropdownEmail}>{currentUser.email}</div>
          </div>
          <div style={styles.dropdownItems}>
            <button 
              style={styles.logoutButton}
              onClick={handleLogout}
            >
              <LogOut size={16} />
              <span>{t('auth.button.logout')}</span>
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserProfile;