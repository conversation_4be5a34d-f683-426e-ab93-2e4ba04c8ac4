{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\FixTorchUMLDGM\\\\uml-detector\\\\src\\\\components\\\\imageUp\\\\AnalysisResults.tsx\",\n  _s = $RefreshSig$();\n//AnalysisResults.tsx\n\nimport React, { useState, useRef, useEffect } from \"react\";\nimport EntityPopup from \"./EntityPopup\";\nimport { useLanguage } from \"../../context/LanguageContext\";\n\n// Définir l'interface ClassData une seule fois\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AnalysisResults = ({\n  darkMode,\n  imageUrl,\n  extractedText,\n  onViewAnnotatedImage,\n  onNavigateToUMLExtractor,\n  resetAnalysis,\n  onUpdateExtractedText\n}) => {\n  _s();\n  const {\n    t\n  } = useLanguage();\n  const [showCopyTooltip, setShowCopyTooltip] = useState(false);\n  const [showAnnotateTooltip, setShowAnnotateTooltip] = useState(false);\n  const [showConvertTooltip, setShowConvertTooltip] = useState(false);\n  const [selectedEntity, setSelectedEntity] = useState(null);\n  const [popupPosition, setPopupPosition] = useState({\n    x: 0,\n    y: 0\n  });\n  const textContentRef = useRef(null);\n\n  // Ajouter les états manquants\n  const [memoryData, setMemoryData] = useState([]);\n  const [extractedClasses, setExtractedClasses] = useState([]);\n  const [selectedClassData, setSelectedClassData] = useState({\n    memoryAttributes: [],\n    memoryMethods: [],\n    extractedAttributes: [],\n    extractedMethods: []\n  });\n\n  // Ajouter un état pour le texte extrait - C'EST LA CLEF DU PROBLÈME\n  const [localExtractedText, setLocalExtractedText] = useState(extractedText);\n\n  // Mettre à jour l'état local lorsque la prop extractedText change\n  useEffect(() => {\n    setLocalExtractedText(extractedText);\n  }, [extractedText]);\n\n  // Définir la fonction parseExtractedText en dehors des useEffect\n  const parseExtractedText = text => {\n    if (!text) return;\n    const classes = [];\n    const classSections = text.split(/class \\d+:/g);\n\n    // Remove empty first element if exists\n    if (classSections[0].trim() === '') {\n      classSections.shift();\n    }\n    classSections.forEach(section => {\n      if (!section.trim()) return;\n      const classNameMatch = section.match(/NOM_CLASSE:\\s+([^\\n]+)/);\n      if (classNameMatch) {\n        const className = classNameMatch[1].trim();\n\n        // Extract attributes\n        const attributesMatch = section.match(/ATTRIBUTS:([\\s\\S]*?)(?=MÉTHODES:|$)/);\n        const attributes = attributesMatch ? attributesMatch[1].trim().split('\\n').filter(attr => attr.trim()) : [];\n\n        // Extract methods\n        const methodsMatch = section.match(/MÉTHODES:([\\s\\S]*?)(?=class \\d+:|$)/);\n        const methods = methodsMatch ? methodsMatch[1].trim().split('\\n').filter(method => method.trim()) : [];\n        classes.push({\n          name: className,\n          attributes,\n          methods\n        });\n      }\n    });\n    setExtractedClasses(classes);\n  };\n\n  // Charger les données de memoire.txt au chargement du composant\n  useEffect(() => {\n    fetchMemoryData();\n  }, []);\n\n  // Analyser le texte extrait lorsqu'il change\n  useEffect(() => {\n    if (extractedText) {\n      parseExtractedText(extractedText);\n    }\n  }, [extractedText]);\n\n  // Dans AnalysisResults.tsx, ajoutez un useEffect pour déboguer les changements de props\n  useEffect(() => {\n    console.log(\"extractedText a changé dans AnalysisResults:\", extractedText.substring(0, 100) + \"...\");\n    // Assurez-vous que parseExtractedText est appelé lorsque extractedText change\n    if (extractedText) {\n      parseExtractedText(extractedText);\n    }\n  }, [extractedText]);\n  const copyToClipboard = () => {\n    // CORRECTION: Utiliser localExtractedText au lieu d'extractedText\n    if (localExtractedText) {\n      navigator.clipboard.writeText(localExtractedText).then(() => {\n        setShowCopyTooltip(true);\n        setTimeout(() => setShowCopyTooltip(false), 2000);\n      }).catch(err => {\n        console.error('Erreur lors de la copie dans le presse-papiers: ', err);\n      });\n    }\n  };\n  const downloadText = () => {\n    // CORRECTION: Utiliser localExtractedText au lieu d'extractedText\n    if (localExtractedText) {\n      const blob = new Blob([localExtractedText], {\n        type: 'text/plain'\n      });\n      const url = URL.createObjectURL(blob);\n      const a = document.createElement('a');\n      a.href = url;\n      a.download = 'texte_extrait_uml.txt';\n      document.body.appendChild(a);\n      a.click();\n      document.body.removeChild(a);\n      URL.revokeObjectURL(url);\n    }\n  };\n  const handleViewAnnotatedImage = () => {\n    if (onViewAnnotatedImage) {\n      onViewAnnotatedImage();\n    }\n  };\n  const handleNavigateToUMLExtractor = () => {\n    if (onNavigateToUMLExtractor) {\n      onNavigateToUMLExtractor();\n    }\n  };\n\n  // Fonction pour récupérer les données de memoire.txt\n  const fetchMemoryData = async () => {\n    try {\n      console.log(\"🔄 FORÇAGE: Récupération de memoire.txt sans cache...\");\n      // Forcer le rechargement en ajoutant un timestamp pour éviter le cache\n      const timestamp = new Date().getTime();\n      const response = await fetch(`http://127.0.0.1:8000/memoire2.txt?t=${timestamp}`, {\n        cache: 'no-cache',\n        headers: {\n          'Cache-Control': 'no-cache',\n          'Pragma': 'no-cache'\n        }\n      });\n      if (!response.ok) {\n        throw new Error(`Impossible de récupérer le fichier mémoire: ${response.status} ${response.statusText}`);\n      }\n      const text = await response.text();\n      console.log(\"📄 CONTENU RÉCUPÉRÉ de memoire.txt:\", text.substring(0, 500) + \"...\"); // Afficher les 500 premiers caractères\n      console.log(\"📊 TAILLE du fichier:\", text.length, \"caractères\");\n\n      // Nouvelle approche pour diviser le texte en sections de classes\n      // Rechercher tous les \"NOM_CLASSE:\" comme début d'une nouvelle classe\n      const classRegex = /NOM_CLASSE:/g;\n      let match;\n      const startIndices = [];\n      while ((match = classRegex.exec(text)) !== null) {\n        startIndices.push(match.index);\n      }\n      const parsedData = [];\n\n      // Traiter chaque section de classe\n      for (let i = 0; i < startIndices.length; i++) {\n        const startIdx = startIndices[i];\n        const endIdx = i < startIndices.length - 1 ? startIndices[i + 1] : text.length;\n        const classBlock = text.substring(startIdx, endIdx).trim();\n        const lines = classBlock.split('\\n');\n        let className = \"\";\n        const attributes = [];\n        const methods = [];\n        let currentSection = '';\n        for (const line of lines) {\n          if (line.startsWith('NOM_CLASSE:')) {\n            className = line.replace('NOM_CLASSE:', '').trim();\n          } else if (line.startsWith('ATTRIBUTS:')) {\n            currentSection = 'attributes';\n          } else if (line.startsWith('MÉTHODES:')) {\n            currentSection = 'methods';\n          } else if (line.trim() && currentSection === 'attributes') {\n            attributes.push(line.trim());\n          } else if (line.trim() && currentSection === 'methods') {\n            methods.push(line.trim());\n          }\n        }\n        if (className) {\n          console.log(`Classe parsée: ${className} avec ${attributes.length} attributs et ${methods.length} méthodes`);\n          parsedData.push({\n            name: className,\n            attributes,\n            methods\n          });\n        }\n      }\n      console.log(\"Toutes les classes parsées:\", parsedData.map(d => d.name).join(\", \"));\n      setMemoryData(parsedData);\n    } catch (error) {\n      console.error(\"Erreur lors de la récupération du fichier mémoire:\", error);\n    }\n  };\n\n  // Modifier la fonction handleEntityClick pour rendre la recherche insensible à la casse\n  const handleEntityClick = event => {\n    const entityName = event.currentTarget.getAttribute('data-entity');\n    if (entityName) {\n      console.log(`Classe sélectionnée: ${entityName}`);\n      console.log(`Noms de classes en mémoire: ${memoryData.map(c => c.name).join(\", \")}`);\n      const rect = event.currentTarget.getBoundingClientRect();\n      setPopupPosition({\n        x: rect.left,\n        y: rect.bottom + window.scrollY\n      });\n\n      // Trouver TOUTES les classes de même nom dans memoire.txt (insensible à la casse)\n      const memoryClasses = memoryData.filter(c => c.name.toLowerCase() === entityName.toLowerCase());\n      console.log(`🔍 RECHERCHE: Trouvé ${memoryClasses.length} classe(s) avec le nom \"${entityName}\":`, memoryClasses.map(c => c.name));\n\n      // DEBUG: Afficher le contenu de chaque classe trouvée\n      memoryClasses.forEach((cls, index) => {\n        console.log(`📋 Classe ${index + 1} \"${cls.name}\":`, {\n          attributes: cls.attributes,\n          methods: cls.methods\n        });\n      });\n\n      // Fusionner tous les attributs et méthodes des classes de même nom\n      const mergedMemoryData = {\n        attributes: [],\n        methods: []\n      };\n      memoryClasses.forEach(cls => {\n        // Ajouter les attributs (éviter les doublons)\n        cls.attributes.forEach(attr => {\n          if (!mergedMemoryData.attributes.includes(attr)) {\n            mergedMemoryData.attributes.push(attr);\n          }\n        });\n\n        // Ajouter les méthodes (éviter les doublons)\n        cls.methods.forEach(method => {\n          if (!mergedMemoryData.methods.includes(method)) {\n            mergedMemoryData.methods.push(method);\n          }\n        });\n      });\n      console.log(\"Données fusionnées de la classe:\", mergedMemoryData);\n\n      // DEBUG: Afficher les données exactes qui seront passées à EntityPopup\n      console.log(\"🔍 DEBUG - Données qui seront affichées dans EntityPopup:\");\n      console.log(\"📋 memoryAttributes:\", mergedMemoryData.attributes);\n      console.log(\"🔧 memoryMethods:\", mergedMemoryData.methods);\n\n      // VÉRIFICATION: Ces données correspondent-elles à ce qui est dans memoire.txt ?\n      console.log(\"⚠️ VÉRIFICATION: Si vous voyez des attributs comme 'idClient', 'nomClient', etc.\");\n      console.log(\"⚠️ C'est un BUG car ces attributs n'existent pas dans les classes Client de memoire.txt !\");\n\n      // Trouver les données de la classe dans le texte extrait (insensible à la casse)\n      const extractedClass = extractedClasses.find(c => c.name.toLowerCase() === entityName.toLowerCase());\n      console.log(\"Données de la classe dans le texte extrait:\", extractedClass);\n\n      // Mettre à jour les données sélectionnées avec les données fusionnées\n      const updatedData = {\n        memoryAttributes: mergedMemoryData.attributes,\n        memoryMethods: mergedMemoryData.methods,\n        extractedAttributes: (extractedClass === null || extractedClass === void 0 ? void 0 : extractedClass.attributes) || [],\n        extractedMethods: (extractedClass === null || extractedClass === void 0 ? void 0 : extractedClass.methods) || []\n      };\n      console.log(\"Données mises à jour pour le popup:\", updatedData);\n      setSelectedClassData(updatedData);\n      setSelectedEntity(entityName);\n    }\n  };\n  const closePopup = () => {\n    setSelectedEntity(null);\n  };\n\n  // Ajoutons une fonction pour envoyer le texte mis à jour au serveur\n  const saveUpdatedTextToServer = async updatedText => {\n    try {\n      console.log(\"Envoi du texte mis à jour au serveur...\");\n      const response = await fetch(\"http://127.0.0.1:8000/update_text/\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          text: updatedText\n        })\n      });\n      if (!response.ok) {\n        throw new Error(`Erreur HTTP: ${response.status}`);\n      }\n      const result = await response.json();\n      console.log(\"Texte mis à jour sur le serveur:\", result);\n      return true;\n    } catch (error) {\n      console.error(\"Erreur lors de la mise à jour du texte sur le serveur:\", error);\n      return false;\n    }\n  };\n\n  // FONCTION CORRIGÉE: handleModifyEntity\n  const handleModifyEntity = async (selectedAttributes, selectedMethods) => {\n    if (!selectedEntity || !selectedAttributes.length && !selectedMethods.length) {\n      closePopup();\n      return;\n    }\n    console.log(`Modification de l'entité: ${selectedEntity}`);\n    console.log(\"Attributs sélectionnés:\", selectedAttributes);\n    console.log(\"Méthodes sélectionnées:\", selectedMethods);\n\n    // Créer une copie du texte extrait LOCAL\n    let newExtractedText = localExtractedText;\n    console.log(\"Texte extrait original:\", newExtractedText.substring(0, 100) + \"...\");\n\n    // Rechercher le pattern de la classe spécifique\n    const classNameEscaped = selectedEntity.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n\n    // Pattern pour trouver toute la classe (du nom jusqu'à la prochaine classe ou fin)\n    const classPattern = new RegExp(`(class \\\\d+:\\\\s*NOM_CLASSE:\\\\s*${classNameEscaped}\\\\s*ATTRIBUTS:[\\\\s\\\\S]*?)(?=class \\\\d+:|$)`, 'i');\n    const classMatch = newExtractedText.match(classPattern);\n    if (!classMatch) {\n      console.error(\"Classe non trouvée dans le texte:\", selectedEntity);\n      closePopup();\n      return;\n    }\n    let classContent = classMatch[1];\n    console.log(\"Contenu de la classe trouvé:\", classContent);\n\n    // Traitement des attributs\n    if (selectedAttributes.length > 0) {\n      // Trouver la position après \"ATTRIBUTS:\" et avant \"MÉTHODES:\"\n      const attributesRegex = /(ATTRIBUTS:\\s*)([\\s\\S]*?)(?=MÉTHODES:|$)/;\n      const attributesMatch = classContent.match(attributesRegex);\n      if (attributesMatch) {\n        const attributesPrefix = attributesMatch[1]; // \"ATTRIBUTS: \"\n        const existingAttributes = attributesMatch[2].trim(); // attributs existants\n\n        // Construire la nouvelle section d'attributs\n        let newAttributesSection = attributesPrefix;\n\n        // Ajouter les attributs existants s'il y en a\n        if (existingAttributes) {\n          newAttributesSection += '\\n' + existingAttributes;\n        }\n\n        // Ajouter les nouveaux attributs\n        selectedAttributes.forEach(attr => {\n          newAttributesSection += '\\n' + attr;\n        });\n\n        // Ajouter un saut de ligne à la fin pour séparer de MÉTHODES\n        newAttributesSection += '\\n';\n\n        // CORRECTION: Remplacer SEULEMENT la section des attributs, sans ajouter MÉTHODES:\n        classContent = classContent.replace(attributesRegex, newAttributesSection);\n        console.log(\"Attributs ajoutés avec succès\");\n      }\n    }\n\n    // Traitement des méthodes\n    if (selectedMethods.length > 0) {\n      // Vérifier d'abord si MÉTHODES: existe déjà\n      const methodsRegex = /(MÉTHODES:\\s*)([\\s\\S]*?)(?=----- |$)/;\n      const methodsMatch = classContent.match(methodsRegex);\n      if (methodsMatch) {\n        // MÉTHODES: existe déjà\n        const methodsPrefix = methodsMatch[1]; // \"MÉTHODES: \"\n        const existingMethods = methodsMatch[2].trim(); // méthodes existantes\n\n        // Construire la nouvelle section de méthodes\n        let newMethodsSection = methodsPrefix;\n\n        // Ajouter les méthodes existantes s'il y en a\n        if (existingMethods) {\n          newMethodsSection += '\\n' + existingMethods;\n        }\n\n        // Ajouter les nouvelles méthodes\n        selectedMethods.forEach(method => {\n          newMethodsSection += '\\n' + method;\n        });\n\n        // Ajouter deux sauts de ligne à la fin pour bien séparer des sections suivantes\n        newMethodsSection += '\\n\\n';\n\n        // Remplacer dans le contenu de la classe\n        classContent = classContent.replace(methodsRegex, newMethodsSection);\n        console.log(\"Méthodes ajoutées avec succès\");\n      } else {\n        // MÉTHODES: n'existe pas encore, l'ajouter après ATTRIBUTS\n        const attributesEndRegex = /(ATTRIBUTS:[\\s\\S]*?)(\\n*)$/;\n        if (classContent.match(attributesEndRegex)) {\n          let methodsSection = '\\nMÉTHODES:';\n          selectedMethods.forEach(method => {\n            methodsSection += '\\n' + method;\n          });\n          methodsSection += '\\n\\n';\n          classContent = classContent.replace(attributesEndRegex, '$1' + methodsSection);\n          console.log(\"Section MÉTHODES ajoutée pour la première fois\");\n        }\n      }\n    }\n\n    // Remplacer le contenu de la classe dans le texte complet\n    const updatedText = newExtractedText.replace(classPattern, classContent);\n    console.log(\"Texte mis à jour:\", updatedText.substring(0, 500) + \"...\");\n\n    // Mettre à jour immédiatement l'état local AVANT d'envoyer au serveur\n    setLocalExtractedText(updatedText);\n    parseExtractedText(updatedText);\n\n    // Envoyer le texte mis à jour au serveur\n    const success = await saveUpdatedTextToServer(updatedText);\n    if (success) {\n      // Propager les modifications au composant parent\n      if (onUpdateExtractedText) {\n        console.log(\"Appel de onUpdateExtractedText\");\n        onUpdateExtractedText(updatedText);\n      } else {\n        console.error(\"onUpdateExtractedText n'est pas défini\");\n      }\n    } else {\n      console.error(\"Échec de la mise à jour du texte sur le serveur\");\n      // En cas d'échec, revenir au texte précédent\n      setLocalExtractedText(extractedText);\n      parseExtractedText(extractedText);\n      alert(t('analysis.error.updateFailed'));\n    }\n\n    // Fermer le popup\n    closePopup();\n  };\n\n  // Fermer le popup si on clique ailleurs sur la page\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (selectedEntity) {\n        // Vérifier si le clic est sur l'overlay du popup (pas sur le popup lui-même)\n        const target = event.target;\n\n        // Si le clic est sur l'overlay (div avec position fixed qui couvre tout l'écran)\n        if (target.style.position === 'fixed' && target.style.top === '0px' && target.style.left === '0px' && target.style.right === '0px' && target.style.bottom === '0px') {\n          closePopup();\n        }\n      }\n    };\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, [selectedEntity]);\n\n  // CORRECTION PRINCIPALE: Fonction pour rendre le texte avec des entités cliquables\n  // Utiliser localExtractedText au lieu d'extractedText\n  const renderTextWithClickableEntities = () => {\n    if (!localExtractedText) return null;\n    console.log(\"Rendu du texte avec entités cliquables:\", localExtractedText.substring(0, 100) + \"...\");\n\n    // Diviser le texte en sections pour chaque classe\n    const classSections = localExtractedText.split(/class \\d+:/g);\n\n    // Si le premier élément est vide (ce qui arrive si le texte commence par \"class X:\"), le supprimer\n    const sections = classSections[0].trim() === '' ? classSections.slice(1) : classSections;\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: sections.map((section, index) => {\n        if (!section.trim()) return null;\n\n        // Extraire le nom de la classe\n        const classNameMatch = section.match(/NOM_CLASSE:\\s+([^\\n]+)/);\n        if (!classNameMatch || classNameMatch.index === undefined) {\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"class \", index + 1, \":\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 540,\n              columnNumber: 17\n            }, this), section]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 539,\n            columnNumber: 15\n          }, this);\n        }\n        const className = classNameMatch[1].trim();\n        const matchIndex = classNameMatch.index;\n        const beforeClassName = section.substring(0, matchIndex + 11); // 11 = longueur de \"NOM_CLASSE: \"\n        const afterClassName = section.substring(matchIndex + 11 + className.length);\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [\"class \", index + 1, \":\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 553,\n            columnNumber: 15\n          }, this), beforeClassName, /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              cursor: 'pointer',\n              color: '#3b82f6',\n              fontWeight: 'bold',\n              textDecoration: 'underline'\n            },\n            \"data-entity\": className,\n            onClick: handleEntityClick,\n            children: className\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 555,\n            columnNumber: 15\n          }, this), afterClassName]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 552,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false);\n  };\n  const getStyles = () => ({\n    resultsHeader: {\n      textAlign: 'center',\n      marginBottom: '20px',\n      color: darkMode ? '#ffffff' : '#333'\n    },\n    imagePreviewContainer: {\n      position: 'relative',\n      width: '100%',\n      maxWidth: '600px',\n      margin: '20px auto'\n    },\n    imagePreview: {\n      width: '100%',\n      borderRadius: '8px',\n      display: 'block',\n      boxShadow: darkMode ? '0 4px 8px rgba(0,0,0,0.5)' : '0 4px 8px rgba(0,0,0,0.1)'\n    },\n    annotateIcon: {\n      position: 'absolute',\n      top: '10px',\n      right: '10px',\n      backgroundColor: darkMode ? 'rgba(0,0,0,0.7)' : 'rgba(255,255,255,0.7)',\n      borderRadius: '50%',\n      padding: '8px',\n      cursor: 'pointer',\n      boxShadow: '0 2px 5px rgba(0,0,0,0.2)',\n      zIndex: 2,\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center'\n    },\n    textPreviewContainer: {\n      position: 'relative',\n      margin: '20px 0',\n      borderRadius: '8px',\n      backgroundColor: darkMode ? '#222' : '#f5f7fa',\n      border: `1px solid ${darkMode ? '#444' : '#e0e0e0'}`,\n      padding: '15px'\n    },\n    textPreviewHeader: {\n      display: 'flex',\n      justifyContent: 'space-between',\n      alignItems: 'center',\n      marginBottom: '10px',\n      borderBottom: `1px solid ${darkMode ? '#444' : '#e0e0e0'}`,\n      paddingBottom: '8px'\n    },\n    textPreviewTitle: {\n      margin: 0,\n      fontSize: '16px',\n      fontWeight: '500'\n    },\n    textPreviewActions: {\n      display: 'flex',\n      gap: '10px'\n    },\n    actionButton: {\n      background: 'none',\n      border: 'none',\n      cursor: 'pointer',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      padding: '5px',\n      borderRadius: '4px',\n      color: darkMode ? '#aaa' : '#666',\n      transition: 'all 0.2s ease',\n      position: 'relative'\n    },\n    textContent: {\n      whiteSpace: 'pre-wrap',\n      fontSize: '14px',\n      maxHeight: '300px',\n      overflowY: 'auto',\n      padding: '5px'\n    },\n    tooltip: {\n      position: 'absolute',\n      bottom: '100%',\n      left: '50%',\n      transform: 'translateX(-50%)',\n      backgroundColor: darkMode ? '#333' : '#fff',\n      color: darkMode ? '#fff' : '#333',\n      padding: '5px 10px',\n      borderRadius: '4px',\n      fontSize: '12px',\n      boxShadow: '0 2px 4px rgba(0,0,0,0.2)',\n      zIndex: 1000,\n      whiteSpace: 'nowrap'\n    },\n    annotateTooltip: {\n      position: 'absolute',\n      top: '-40px',\n      right: '0',\n      backgroundColor: darkMode ? '#333' : '#fff',\n      color: darkMode ? '#fff' : '#333',\n      padding: '6px 12px',\n      borderRadius: '4px',\n      fontSize: '12px',\n      boxShadow: '0 2px 4px rgba(0,0,0,0.2)',\n      zIndex: 1000,\n      whiteSpace: 'nowrap',\n      width: '180px',\n      textAlign: 'center'\n    },\n    convertTooltip: {\n      position: 'absolute',\n      bottom: '100%',\n      left: '50%',\n      transform: 'translateX(-50%)',\n      backgroundColor: darkMode ? '#333' : '#fff',\n      color: darkMode ? '#fff' : '#333',\n      padding: '6px 12px',\n      borderRadius: '4px',\n      fontSize: '12px',\n      boxShadow: '0 2px 4px rgba(0,0,0,0.2)',\n      zIndex: 1000,\n      width: '220px',\n      textAlign: 'center',\n      marginBottom: '5px',\n      whiteSpace: 'normal'\n    },\n    checkboxContainer: {\n      display: 'flex',\n      flexDirection: 'column',\n      gap: '15px',\n      marginTop: '30px'\n    },\n    checkboxLabel: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '10px',\n      cursor: 'pointer'\n    }\n  });\n  const styles = getStyles();\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      style: styles.resultsHeader,\n      children: t('analysis.imageAnalyzed')\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 716,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        marginBottom: '20px'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 717,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.imagePreviewContainer,\n      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n        src: imageUrl,\n        alt: t('analysis.diagramAlt'),\n        style: styles.imagePreview\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 721,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.annotateIcon,\n        onClick: handleViewAnnotatedImage,\n        onMouseEnter: () => setShowAnnotateTooltip(true),\n        onMouseLeave: () => setShowAnnotateTooltip(false),\n        children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n          width: \"20\",\n          height: \"20\",\n          viewBox: \"0 0 24 24\",\n          fill: \"none\",\n          stroke: darkMode ? \"white\" : \"#333\",\n          strokeWidth: \"2\",\n          strokeLinecap: \"round\",\n          strokeLinejoin: \"round\",\n          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 729,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 730,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 728,\n          columnNumber: 11\n        }, this), showAnnotateTooltip && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.annotateTooltip,\n          children: t('analysis.tooltip.viewAnnotated')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 733,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 722,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 720,\n      columnNumber: 7\n    }, this), extractedText && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.textPreviewContainer,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.textPreviewHeader,\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: styles.textPreviewTitle,\n          children: t('analysis.extractedText')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 743,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.textPreviewActions,\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: copyToClipboard,\n            style: styles.actionButton,\n            onMouseOver: e => e.currentTarget.style.backgroundColor = darkMode ? '#444' : '#eee',\n            onMouseOut: e => e.currentTarget.style.backgroundColor = 'transparent',\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"20\",\n              height: \"20\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              children: [/*#__PURE__*/_jsxDEV(\"rect\", {\n                x: \"9\",\n                y: \"9\",\n                width: \"13\",\n                height: \"13\",\n                rx: \"2\",\n                ry: \"2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 752,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 753,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 751,\n              columnNumber: 17\n            }, this), showCopyTooltip && /*#__PURE__*/_jsxDEV(\"span\", {\n              style: styles.tooltip,\n              children: \"Copi\\xE9!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 756,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 745,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: downloadText,\n            style: styles.actionButton,\n            onMouseOver: e => e.currentTarget.style.backgroundColor = darkMode ? '#444' : '#eee',\n            onMouseOut: e => e.currentTarget.style.backgroundColor = 'transparent',\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"20\",\n              height: \"20\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 766,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"polyline\", {\n                points: \"7 10 12 15 17 10\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 767,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n                x1: \"12\",\n                y1: \"15\",\n                x2: \"12\",\n                y2: \"3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 768,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 765,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 759,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleNavigateToUMLExtractor,\n            style: styles.actionButton,\n            onMouseOver: e => {\n              e.currentTarget.style.backgroundColor = darkMode ? '#444' : '#eee';\n              setShowConvertTooltip(true);\n            },\n            onMouseOut: e => {\n              e.currentTarget.style.backgroundColor = 'transparent';\n              setShowConvertTooltip(false);\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"20\",\n              height: \"20\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M5 12h14M13 5l7 7-7 7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 784,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 783,\n              columnNumber: 17\n            }, this), showConvertTooltip && /*#__PURE__*/_jsxDEV(\"span\", {\n              style: styles.convertTooltip,\n              children: t('analysis.tooltip.convertDiagram')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 787,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 771,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 744,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 742,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.textContent,\n        ref: textContentRef,\n        children: renderTextWithClickableEntities()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 794,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 741,\n      columnNumber: 9\n    }, this), selectedEntity && /*#__PURE__*/_jsxDEV(EntityPopup, {\n      darkMode: darkMode,\n      entityName: selectedEntity,\n      position: popupPosition,\n      onClose: closePopup,\n      onModify: handleModifyEntity,\n      memoryAttributes: selectedClassData.memoryAttributes,\n      memoryMethods: selectedClassData.memoryMethods,\n      extractedAttributes: selectedClassData.extractedAttributes,\n      extractedMethods: selectedClassData.extractedMethods,\n      currentDiagramText: extractedText\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 802,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.checkboxContainer,\n      children: /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          display: \"flex\",\n          alignItems: \"center\",\n          justifyContent: \"center\",\n          gap: \"6px\",\n          padding: \"0.5rem\",\n          backgroundColor: \"rgba(59, 130, 246, 0.1)\",\n          color: \"#60a5fa\",\n          border: \"1px solid rgba(96, 165, 250, 0.2)\",\n          borderRadius: \"6px\",\n          fontSize: \"0.875rem\",\n          fontWeight: \"500\",\n          cursor: \"pointer\",\n          width: \"100%\",\n          boxSizing: \"border-box\"\n        },\n        onClick: resetAnalysis,\n        children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n          width: \"14\",\n          height: \"14\",\n          viewBox: \"0 0 24 24\",\n          fill: \"none\",\n          stroke: \"currentColor\",\n          strokeWidth: \"2\",\n          children: [/*#__PURE__*/_jsxDEV(\"line\", {\n            x1: \"12\",\n            y1: \"5\",\n            x2: \"12\",\n            y2: \"19\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 837,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n            x1: \"5\",\n            y1: \"12\",\n            x2: \"19\",\n            y2: \"12\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 838,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 836,\n          columnNumber: 11\n        }, this), \"Nouveau Projet\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 817,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 816,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(AnalysisResults, \"qK+Rz/LrQbUkB43SZryJj28o3uc=\", false, function () {\n  return [useLanguage];\n});\n_c = AnalysisResults;\nexport default AnalysisResults;\nvar _c;\n$RefreshReg$(_c, \"AnalysisResults\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "EntityPopup", "useLanguage", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AnalysisResults", "darkMode", "imageUrl", "extractedText", "onViewAnnotatedImage", "onNavigateToUMLExtractor", "resetAnalysis", "onUpdateExtractedText", "_s", "t", "showCopyTooltip", "setShowCopyTooltip", "showAnnotateTooltip", "setShowAnnotateTooltip", "showConvertTooltip", "setShowConvertTooltip", "selected<PERSON><PERSON><PERSON>", "setSelectedEntity", "popupPosition", "setPopupPosition", "x", "y", "textContentRef", "memoryData", "setMemoryData", "extractedClasses", "setExtractedClasses", "selectedClassData", "setSelectedClassData", "memoryAttributes", "memoryMethods", "extractedAttributes", "extractedMethods", "localExtractedText", "setLocalExtractedText", "parseExtractedText", "text", "classes", "classSections", "split", "trim", "shift", "for<PERSON>ach", "section", "classNameMatch", "match", "className", "attributesMatch", "attributes", "filter", "attr", "methodsMatch", "methods", "method", "push", "name", "fetchMemoryData", "console", "log", "substring", "copyToClipboard", "navigator", "clipboard", "writeText", "then", "setTimeout", "catch", "err", "error", "downloadText", "blob", "Blob", "type", "url", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "handleViewAnnotatedImage", "handleNavigateToUMLExtractor", "timestamp", "Date", "getTime", "response", "fetch", "cache", "headers", "ok", "Error", "status", "statusText", "length", "classRegex", "startIndices", "exec", "index", "parsedData", "i", "startIdx", "endIdx", "classBlock", "lines", "currentSection", "line", "startsWith", "replace", "map", "d", "join", "handleEntityClick", "event", "entityName", "currentTarget", "getAttribute", "c", "rect", "getBoundingClientRect", "left", "bottom", "window", "scrollY", "memoryClasses", "toLowerCase", "cls", "mergedMemoryData", "includes", "extractedClass", "find", "updatedData", "closePopup", "saveUpdatedTextToServer", "updatedText", "JSON", "stringify", "result", "json", "handleModifyEntity", "selectedAttributes", "selectedMethods", "newExtractedText", "classNameEscaped", "classPattern", "RegExp", "classMatch", "classContent", "attributesRegex", "attributesPrefix", "existingAttributes", "newAttributesSection", "methodsRegex", "methodsPrefix", "existingMethods", "newMethodsSection", "attributesEndRegex", "methodsSection", "success", "alert", "handleClickOutside", "target", "style", "position", "top", "right", "addEventListener", "removeEventListener", "renderTextWithClickableEntities", "sections", "slice", "children", "undefined", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "matchIndex", "beforeClassName", "afterClassName", "cursor", "color", "fontWeight", "textDecoration", "onClick", "getStyles", "resultsHeader", "textAlign", "marginBottom", "imagePreviewContainer", "width", "max<PERSON><PERSON><PERSON>", "margin", "imagePreview", "borderRadius", "display", "boxShadow", "annotateIcon", "backgroundColor", "padding", "zIndex", "alignItems", "justifyContent", "textPreviewContainer", "border", "textPreviewHeader", "borderBottom", "paddingBottom", "textPreviewTitle", "fontSize", "textPreviewActions", "gap", "actionButton", "background", "transition", "textContent", "whiteSpace", "maxHeight", "overflowY", "tooltip", "transform", "annotateTooltip", "convertTooltip", "checkboxContainer", "flexDirection", "marginTop", "checkboxLabel", "styles", "src", "alt", "onMouseEnter", "onMouseLeave", "height", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "cx", "cy", "r", "onMouseOver", "e", "onMouseOut", "rx", "ry", "points", "x1", "y1", "x2", "y2", "ref", "onClose", "onModify", "currentDiagramText", "boxSizing", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/FixTorchUMLDGM/uml-detector/src/components/imageUp/AnalysisResults.tsx"], "sourcesContent": ["//AnalysisResults.tsx\r\n\r\nimport React, { useState, useRef, useEffect } from \"react\";\r\nimport EntityPopup from \"./EntityPopup\";\r\nimport { useLanguage } from \"../../context/LanguageContext\";\r\n\r\n// Définir l'interface ClassData une seule fois\r\ninterface ClassData {\r\n  name: string;\r\n  attributes: string[];\r\n  methods: string[];\r\n}\r\n\r\ninterface SelectedClassData {\r\n  memoryAttributes: string[];\r\n  memoryMethods: string[];\r\n  extractedAttributes: string[];\r\n  extractedMethods: string[];\r\n}\r\n\r\ninterface AnalysisResultsProps {\r\n  darkMode: boolean;\r\n  imageUrl: string;\r\n  extractedText: string;\r\n  onViewAnnotatedImage?: () => void;\r\n  onNavigateToUMLExtractor?: () => void;\r\n  resetAnalysis: () => void;\r\n  onUpdateExtractedText?: (newText: string) => void; // Nouvelle prop pour mettre à jour le texte extrait\r\n}\r\n\r\nconst AnalysisResults: React.FC<AnalysisResultsProps> = ({\r\n  darkMode,\r\n  imageUrl,\r\n  extractedText,\r\n  onViewAnnotatedImage,\r\n  onNavigateToUMLExtractor,\r\n  resetAnalysis,\r\n  onUpdateExtractedText\r\n}) => {\r\n  const { t } = useLanguage();\r\n  const [showCopyTooltip, setShowCopyTooltip] = useState(false);\r\n  const [showAnnotateTooltip, setShowAnnotateTooltip] = useState(false);\r\n  const [showConvertTooltip, setShowConvertTooltip] = useState(false);\r\n  const [selectedEntity, setSelectedEntity] = useState<string | null>(null);\r\n  const [popupPosition, setPopupPosition] = useState({ x: 0, y: 0 });\r\n  const textContentRef = useRef<HTMLDivElement>(null);\r\n  \r\n  // Ajouter les états manquants\r\n  const [memoryData, setMemoryData] = useState<ClassData[]>([]);\r\n  const [extractedClasses, setExtractedClasses] = useState<ClassData[]>([]);\r\n  const [selectedClassData, setSelectedClassData] = useState<SelectedClassData>({\r\n    memoryAttributes: [],\r\n    memoryMethods: [],\r\n    extractedAttributes: [],\r\n    extractedMethods: []\r\n  });\r\n\r\n  // Ajouter un état pour le texte extrait - C'EST LA CLEF DU PROBLÈME\r\n  const [localExtractedText, setLocalExtractedText] = useState<string>(extractedText);\r\n\r\n  // Mettre à jour l'état local lorsque la prop extractedText change\r\n  useEffect(() => {\r\n    setLocalExtractedText(extractedText);\r\n  }, [extractedText]);\r\n\r\n  // Définir la fonction parseExtractedText en dehors des useEffect\r\n  const parseExtractedText = (text: string) => {\r\n    if (!text) return;\r\n    \r\n    const classes: ClassData[] = [];\r\n    const classSections = text.split(/class \\d+:/g);\r\n    \r\n    // Remove empty first element if exists\r\n    if (classSections[0].trim() === '') {\r\n      classSections.shift();\r\n    }\r\n\r\n    classSections.forEach(section => {\r\n      if (!section.trim()) return;\r\n      \r\n      const classNameMatch = section.match(/NOM_CLASSE:\\s+([^\\n]+)/);\r\n      if (classNameMatch) {\r\n        const className = classNameMatch[1].trim();\r\n        \r\n        // Extract attributes\r\n        const attributesMatch = section.match(/ATTRIBUTS:([\\s\\S]*?)(?=MÉTHODES:|$)/);\r\n        const attributes = attributesMatch ? \r\n          attributesMatch[1].trim().split('\\n').filter(attr => attr.trim()) : [];\r\n        \r\n        // Extract methods\r\n        const methodsMatch = section.match(/MÉTHODES:([\\s\\S]*?)(?=class \\d+:|$)/);\r\n        const methods = methodsMatch ?\r\n          methodsMatch[1].trim().split('\\n').filter(method => method.trim()) : [];\r\n        \r\n        classes.push({\r\n          name: className,\r\n          attributes,\r\n          methods\r\n        });\r\n      }\r\n    });\r\n\r\n    setExtractedClasses(classes);\r\n  };\r\n\r\n  // Charger les données de memoire.txt au chargement du composant\r\n  useEffect(() => {\r\n    fetchMemoryData();\r\n  }, []);\r\n\r\n  // Analyser le texte extrait lorsqu'il change\r\n  useEffect(() => {\r\n    if (extractedText) {\r\n      parseExtractedText(extractedText);\r\n    }\r\n  }, [extractedText]);\r\n\r\n  // Dans AnalysisResults.tsx, ajoutez un useEffect pour déboguer les changements de props\r\n  useEffect(() => {\r\n    console.log(\"extractedText a changé dans AnalysisResults:\", extractedText.substring(0, 100) + \"...\");\r\n    // Assurez-vous que parseExtractedText est appelé lorsque extractedText change\r\n    if (extractedText) {\r\n      parseExtractedText(extractedText);\r\n    }\r\n  }, [extractedText]);\r\n\r\n  const copyToClipboard = () => {\r\n    // CORRECTION: Utiliser localExtractedText au lieu d'extractedText\r\n    if (localExtractedText) {\r\n      navigator.clipboard.writeText(localExtractedText)\r\n        .then(() => {\r\n          setShowCopyTooltip(true);\r\n          setTimeout(() => setShowCopyTooltip(false), 2000);\r\n        })\r\n        .catch(err => {\r\n          console.error('Erreur lors de la copie dans le presse-papiers: ', err);\r\n        });\r\n    }\r\n  };\r\n\r\n  const downloadText = () => {\r\n    // CORRECTION: Utiliser localExtractedText au lieu d'extractedText\r\n    if (localExtractedText) {\r\n      const blob = new Blob([localExtractedText], { type: 'text/plain' });\r\n      const url = URL.createObjectURL(blob);\r\n      const a = document.createElement('a');\r\n      a.href = url;\r\n      a.download = 'texte_extrait_uml.txt';\r\n      document.body.appendChild(a);\r\n      a.click();\r\n      document.body.removeChild(a);\r\n      URL.revokeObjectURL(url);\r\n    }\r\n  };\r\n\r\n  const handleViewAnnotatedImage = () => {\r\n    if (onViewAnnotatedImage) {\r\n      onViewAnnotatedImage();\r\n    }\r\n  };\r\n\r\n  const handleNavigateToUMLExtractor = () => {\r\n    if (onNavigateToUMLExtractor) {\r\n      onNavigateToUMLExtractor();\r\n    }\r\n  };\r\n\r\n  // Fonction pour récupérer les données de memoire.txt\r\n  const fetchMemoryData = async () => {\r\n    try {\r\n      console.log(\"🔄 FORÇAGE: Récupération de memoire.txt sans cache...\");\r\n      // Forcer le rechargement en ajoutant un timestamp pour éviter le cache\r\n      const timestamp = new Date().getTime();\r\n      const response = await fetch(`http://127.0.0.1:8000/memoire2.txt?t=${timestamp}`, {\r\n        cache: 'no-cache',\r\n        headers: {\r\n          'Cache-Control': 'no-cache',\r\n          'Pragma': 'no-cache'\r\n        }\r\n      });\r\n      if (!response.ok) {\r\n        throw new Error(`Impossible de récupérer le fichier mémoire: ${response.status} ${response.statusText}`);\r\n      }\r\n\r\n      const text = await response.text();\r\n      console.log(\"📄 CONTENU RÉCUPÉRÉ de memoire.txt:\", text.substring(0, 500) + \"...\"); // Afficher les 500 premiers caractères\r\n      console.log(\"📊 TAILLE du fichier:\", text.length, \"caractères\");\r\n      \r\n      // Nouvelle approche pour diviser le texte en sections de classes\r\n      // Rechercher tous les \"NOM_CLASSE:\" comme début d'une nouvelle classe\r\n      const classRegex = /NOM_CLASSE:/g;\r\n      let match;\r\n      const startIndices = [];\r\n      \r\n      while ((match = classRegex.exec(text)) !== null) {\r\n        startIndices.push(match.index);\r\n      }\r\n      \r\n      const parsedData: ClassData[] = [];\r\n      \r\n      // Traiter chaque section de classe\r\n      for (let i = 0; i < startIndices.length; i++) {\r\n        const startIdx = startIndices[i];\r\n        const endIdx = i < startIndices.length - 1 ? startIndices[i + 1] : text.length;\r\n        const classBlock = text.substring(startIdx, endIdx).trim();\r\n        \r\n        const lines = classBlock.split('\\n');\r\n        let className = \"\";\r\n        const attributes: string[] = [];\r\n        const methods: string[] = [];\r\n        \r\n        let currentSection = '';\r\n        \r\n        for (const line of lines) {\r\n          if (line.startsWith('NOM_CLASSE:')) {\r\n            className = line.replace('NOM_CLASSE:', '').trim();\r\n          } else if (line.startsWith('ATTRIBUTS:')) {\r\n            currentSection = 'attributes';\r\n          } else if (line.startsWith('MÉTHODES:')) {\r\n            currentSection = 'methods';\r\n          } else if (line.trim() && currentSection === 'attributes') {\r\n            attributes.push(line.trim());\r\n          } else if (line.trim() && currentSection === 'methods') {\r\n            methods.push(line.trim());\r\n          }\r\n        }\r\n        \r\n        if (className) {\r\n          console.log(`Classe parsée: ${className} avec ${attributes.length} attributs et ${methods.length} méthodes`);\r\n          parsedData.push({ name: className, attributes, methods });\r\n        }\r\n      }\r\n      \r\n      console.log(\"Toutes les classes parsées:\", parsedData.map(d => d.name).join(\", \"));\r\n      setMemoryData(parsedData);\r\n    } catch (error) {\r\n      console.error(\"Erreur lors de la récupération du fichier mémoire:\", error);\r\n    }\r\n  };\r\n\r\n  // Modifier la fonction handleEntityClick pour rendre la recherche insensible à la casse\r\n  const handleEntityClick = (event: React.MouseEvent<HTMLSpanElement>) => {\r\n    const entityName = event.currentTarget.getAttribute('data-entity');\r\n    if (entityName) {\r\n      console.log(`Classe sélectionnée: ${entityName}`);\r\n      console.log(`Noms de classes en mémoire: ${memoryData.map(c => c.name).join(\", \")}`);\r\n      \r\n      const rect = event.currentTarget.getBoundingClientRect();\r\n      setPopupPosition({\r\n        x: rect.left,\r\n        y: rect.bottom + window.scrollY\r\n      });\r\n      \r\n      // Trouver TOUTES les classes de même nom dans memoire.txt (insensible à la casse)\r\n      const memoryClasses = memoryData.filter(c => c.name.toLowerCase() === entityName.toLowerCase());\r\n      console.log(`🔍 RECHERCHE: Trouvé ${memoryClasses.length} classe(s) avec le nom \"${entityName}\":`, memoryClasses.map(c => c.name));\r\n\r\n      // DEBUG: Afficher le contenu de chaque classe trouvée\r\n      memoryClasses.forEach((cls, index) => {\r\n        console.log(`📋 Classe ${index + 1} \"${cls.name}\":`, {\r\n          attributes: cls.attributes,\r\n          methods: cls.methods\r\n        });\r\n      });\r\n\r\n      // Fusionner tous les attributs et méthodes des classes de même nom\r\n      const mergedMemoryData = {\r\n        attributes: [] as string[],\r\n        methods: [] as string[]\r\n      };\r\n\r\n      memoryClasses.forEach(cls => {\r\n        // Ajouter les attributs (éviter les doublons)\r\n        cls.attributes.forEach(attr => {\r\n          if (!mergedMemoryData.attributes.includes(attr)) {\r\n            mergedMemoryData.attributes.push(attr);\r\n          }\r\n        });\r\n\r\n        // Ajouter les méthodes (éviter les doublons)\r\n        cls.methods.forEach(method => {\r\n          if (!mergedMemoryData.methods.includes(method)) {\r\n            mergedMemoryData.methods.push(method);\r\n          }\r\n        });\r\n      });\r\n\r\n      console.log(\"Données fusionnées de la classe:\", mergedMemoryData);\r\n\r\n      // DEBUG: Afficher les données exactes qui seront passées à EntityPopup\r\n      console.log(\"🔍 DEBUG - Données qui seront affichées dans EntityPopup:\");\r\n      console.log(\"📋 memoryAttributes:\", mergedMemoryData.attributes);\r\n      console.log(\"🔧 memoryMethods:\", mergedMemoryData.methods);\r\n\r\n      // VÉRIFICATION: Ces données correspondent-elles à ce qui est dans memoire.txt ?\r\n      console.log(\"⚠️ VÉRIFICATION: Si vous voyez des attributs comme 'idClient', 'nomClient', etc.\");\r\n      console.log(\"⚠️ C'est un BUG car ces attributs n'existent pas dans les classes Client de memoire.txt !\");\r\n      \r\n      // Trouver les données de la classe dans le texte extrait (insensible à la casse)\r\n      const extractedClass = extractedClasses.find(c => c.name.toLowerCase() === entityName.toLowerCase());\r\n      console.log(\"Données de la classe dans le texte extrait:\", extractedClass);\r\n      \r\n      // Mettre à jour les données sélectionnées avec les données fusionnées\r\n      const updatedData = {\r\n        memoryAttributes: mergedMemoryData.attributes,\r\n        memoryMethods: mergedMemoryData.methods,\r\n        extractedAttributes: extractedClass?.attributes || [],\r\n        extractedMethods: extractedClass?.methods || []\r\n      };\r\n      \r\n      console.log(\"Données mises à jour pour le popup:\", updatedData);\r\n      setSelectedClassData(updatedData);\r\n      \r\n      setSelectedEntity(entityName);\r\n    }\r\n  };\r\n\r\n  const closePopup = () => {\r\n    setSelectedEntity(null);\r\n  };\r\n\r\n  // Ajoutons une fonction pour envoyer le texte mis à jour au serveur\r\n  const saveUpdatedTextToServer = async (updatedText: string) => {\r\n    try {\r\n      console.log(\"Envoi du texte mis à jour au serveur...\");\r\n      const response = await fetch(\"http://127.0.0.1:8000/update_text/\", {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n        body: JSON.stringify({ text: updatedText }),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`Erreur HTTP: ${response.status}`);\r\n      }\r\n\r\n      const result = await response.json();\r\n      console.log(\"Texte mis à jour sur le serveur:\", result);\r\n      return true;\r\n    } catch (error) {\r\n      console.error(\"Erreur lors de la mise à jour du texte sur le serveur:\", error);\r\n      return false;\r\n    }\r\n  };\r\n\r\n  // FONCTION CORRIGÉE: handleModifyEntity\r\nconst handleModifyEntity = async (selectedAttributes: string[], selectedMethods: string[]) => {\r\n  if (!selectedEntity || (!selectedAttributes.length && !selectedMethods.length)) {\r\n    closePopup();\r\n    return;\r\n  }\r\n  \r\n  console.log(`Modification de l'entité: ${selectedEntity}`);\r\n  console.log(\"Attributs sélectionnés:\", selectedAttributes);\r\n  console.log(\"Méthodes sélectionnées:\", selectedMethods);\r\n  \r\n  // Créer une copie du texte extrait LOCAL\r\n  let newExtractedText = localExtractedText;\r\n  console.log(\"Texte extrait original:\", newExtractedText.substring(0, 100) + \"...\");\r\n  \r\n  // Rechercher le pattern de la classe spécifique\r\n  const classNameEscaped = selectedEntity.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\r\n  \r\n  // Pattern pour trouver toute la classe (du nom jusqu'à la prochaine classe ou fin)\r\n  const classPattern = new RegExp(\r\n    `(class \\\\d+:\\\\s*NOM_CLASSE:\\\\s*${classNameEscaped}\\\\s*ATTRIBUTS:[\\\\s\\\\S]*?)(?=class \\\\d+:|$)`,\r\n    'i'\r\n  );\r\n  \r\n  const classMatch = newExtractedText.match(classPattern);\r\n  \r\n  if (!classMatch) {\r\n    console.error(\"Classe non trouvée dans le texte:\", selectedEntity);\r\n    closePopup();\r\n    return;\r\n  }\r\n  \r\n  let classContent = classMatch[1];\r\n  console.log(\"Contenu de la classe trouvé:\", classContent);\r\n  \r\n  // Traitement des attributs\r\n  if (selectedAttributes.length > 0) {\r\n    // Trouver la position après \"ATTRIBUTS:\" et avant \"MÉTHODES:\"\r\n    const attributesRegex = /(ATTRIBUTS:\\s*)([\\s\\S]*?)(?=MÉTHODES:|$)/;\r\n    const attributesMatch = classContent.match(attributesRegex);\r\n    \r\n    if (attributesMatch) {\r\n      const attributesPrefix = attributesMatch[1]; // \"ATTRIBUTS: \"\r\n      const existingAttributes = attributesMatch[2].trim(); // attributs existants\r\n      \r\n      // Construire la nouvelle section d'attributs\r\n      let newAttributesSection = attributesPrefix;\r\n      \r\n      // Ajouter les attributs existants s'il y en a\r\n      if (existingAttributes) {\r\n        newAttributesSection += '\\n' + existingAttributes;\r\n      }\r\n      \r\n      // Ajouter les nouveaux attributs\r\n      selectedAttributes.forEach(attr => {\r\n        newAttributesSection += '\\n' + attr;\r\n      });\r\n      \r\n      // Ajouter un saut de ligne à la fin pour séparer de MÉTHODES\r\n      newAttributesSection += '\\n';\r\n      \r\n      // CORRECTION: Remplacer SEULEMENT la section des attributs, sans ajouter MÉTHODES:\r\n      classContent = classContent.replace(attributesRegex, newAttributesSection);\r\n      \r\n      console.log(\"Attributs ajoutés avec succès\");\r\n    }\r\n  }\r\n  \r\n  // Traitement des méthodes\r\n  if (selectedMethods.length > 0) {\r\n    // Vérifier d'abord si MÉTHODES: existe déjà\r\n    const methodsRegex = /(MÉTHODES:\\s*)([\\s\\S]*?)(?=----- |$)/;\r\n    const methodsMatch = classContent.match(methodsRegex);\r\n    \r\n    if (methodsMatch) {\r\n      // MÉTHODES: existe déjà\r\n      const methodsPrefix = methodsMatch[1]; // \"MÉTHODES: \"\r\n      const existingMethods = methodsMatch[2].trim(); // méthodes existantes\r\n      \r\n      // Construire la nouvelle section de méthodes\r\n      let newMethodsSection = methodsPrefix;\r\n      \r\n      // Ajouter les méthodes existantes s'il y en a\r\n      if (existingMethods) {\r\n        newMethodsSection += '\\n' + existingMethods;\r\n      }\r\n      \r\n      // Ajouter les nouvelles méthodes\r\n      selectedMethods.forEach(method => {\r\n        newMethodsSection += '\\n' + method;\r\n      });\r\n      \r\n      // Ajouter deux sauts de ligne à la fin pour bien séparer des sections suivantes\r\n      newMethodsSection += '\\n\\n';\r\n      \r\n      // Remplacer dans le contenu de la classe\r\n      classContent = classContent.replace(methodsRegex, newMethodsSection);\r\n      \r\n      console.log(\"Méthodes ajoutées avec succès\");\r\n    } else {\r\n      // MÉTHODES: n'existe pas encore, l'ajouter après ATTRIBUTS\r\n      const attributesEndRegex = /(ATTRIBUTS:[\\s\\S]*?)(\\n*)$/;\r\n      if (classContent.match(attributesEndRegex)) {\r\n        let methodsSection = '\\nMÉTHODES:';\r\n        selectedMethods.forEach(method => {\r\n          methodsSection += '\\n' + method;\r\n        });\r\n        methodsSection += '\\n\\n';\r\n        \r\n        classContent = classContent.replace(attributesEndRegex, '$1' + methodsSection);\r\n        console.log(\"Section MÉTHODES ajoutée pour la première fois\");\r\n      }\r\n    }\r\n  }\r\n  \r\n  // Remplacer le contenu de la classe dans le texte complet\r\n  const updatedText = newExtractedText.replace(classPattern, classContent);\r\n  \r\n  console.log(\"Texte mis à jour:\", updatedText.substring(0, 500) + \"...\");\r\n  \r\n  // Mettre à jour immédiatement l'état local AVANT d'envoyer au serveur\r\n  setLocalExtractedText(updatedText);\r\n  parseExtractedText(updatedText);\r\n  \r\n  // Envoyer le texte mis à jour au serveur\r\n  const success = await saveUpdatedTextToServer(updatedText);\r\n  \r\n  if (success) {\r\n    // Propager les modifications au composant parent\r\n    if (onUpdateExtractedText) {\r\n      console.log(\"Appel de onUpdateExtractedText\");\r\n      onUpdateExtractedText(updatedText);\r\n    } else {\r\n      console.error(\"onUpdateExtractedText n'est pas défini\");\r\n    }\r\n  } else {\r\n    console.error(\"Échec de la mise à jour du texte sur le serveur\");\r\n    // En cas d'échec, revenir au texte précédent\r\n    setLocalExtractedText(extractedText);\r\n    parseExtractedText(extractedText);\r\n    alert(t('analysis.error.updateFailed'));\r\n  }\r\n  \r\n  // Fermer le popup\r\n  closePopup();\r\n};\r\n\r\n  // Fermer le popup si on clique ailleurs sur la page\r\n  useEffect(() => {\r\n    const handleClickOutside = (event: MouseEvent) => {\r\n      if (selectedEntity) {\r\n        // Vérifier si le clic est sur l'overlay du popup (pas sur le popup lui-même)\r\n        const target = event.target as HTMLElement;\r\n        \r\n        // Si le clic est sur l'overlay (div avec position fixed qui couvre tout l'écran)\r\n        if (target.style.position === 'fixed' && \r\n            target.style.top === '0px' && \r\n            target.style.left === '0px' && \r\n            target.style.right === '0px' && \r\n            target.style.bottom === '0px') {\r\n          closePopup();\r\n        }\r\n      }\r\n    };\r\n\r\n    document.addEventListener('mousedown', handleClickOutside);\r\n    return () => {\r\n      document.removeEventListener('mousedown', handleClickOutside);\r\n    };\r\n  }, [selectedEntity]);\r\n\r\n  // CORRECTION PRINCIPALE: Fonction pour rendre le texte avec des entités cliquables\r\n  // Utiliser localExtractedText au lieu d'extractedText\r\n  const renderTextWithClickableEntities = () => {\r\n    if (!localExtractedText) return null;\r\n    console.log(\"Rendu du texte avec entités cliquables:\", localExtractedText.substring(0, 100) + \"...\");\r\n    \r\n    // Diviser le texte en sections pour chaque classe\r\n    const classSections = localExtractedText.split(/class \\d+:/g);\r\n    \r\n    // Si le premier élément est vide (ce qui arrive si le texte commence par \"class X:\"), le supprimer\r\n    const sections = classSections[0].trim() === '' ? classSections.slice(1) : classSections;\r\n    \r\n    return (\r\n      <>\r\n        {sections.map((section, index) => {\r\n          if (!section.trim()) return null;\r\n          \r\n          // Extraire le nom de la classe\r\n          const classNameMatch = section.match(/NOM_CLASSE:\\s+([^\\n]+)/);\r\n          if (!classNameMatch || classNameMatch.index === undefined) {\r\n            return (\r\n              <div key={index}>\r\n                <div>class {index+1}:</div>\r\n                {section}\r\n              </div>\r\n            );\r\n          }\r\n          \r\n          const className = classNameMatch[1].trim();\r\n          const matchIndex = classNameMatch.index;\r\n          const beforeClassName = section.substring(0, matchIndex + 11); // 11 = longueur de \"NOM_CLASSE: \"\r\n          const afterClassName = section.substring(matchIndex + 11 + className.length);\r\n          \r\n          return (\r\n            <div key={index}>\r\n              <div>class {index+1}:</div>\r\n              {beforeClassName}\r\n              <span \r\n                style={{\r\n                  cursor: 'pointer',\r\n                  color: '#3b82f6',\r\n                  fontWeight: 'bold',\r\n                  textDecoration: 'underline'\r\n                }}\r\n                data-entity={className}\r\n                onClick={handleEntityClick}\r\n              >\r\n                {className}\r\n              </span>\r\n              {afterClassName}\r\n            </div>\r\n          );\r\n        })}\r\n      </>\r\n    );\r\n  };\r\n\r\n  const getStyles = () => ({\r\n    resultsHeader: {\r\n      textAlign: 'center' as const,\r\n      marginBottom: '20px',\r\n      color: darkMode ? '#ffffff' : '#333'\r\n    },\r\n    imagePreviewContainer: {\r\n      position: 'relative' as const,\r\n      width: '100%',\r\n      maxWidth: '600px',\r\n      margin: '20px auto'\r\n    },\r\n    imagePreview: {\r\n      width: '100%',\r\n      borderRadius: '8px',\r\n      display: 'block',\r\n      boxShadow: darkMode ? '0 4px 8px rgba(0,0,0,0.5)' : '0 4px 8px rgba(0,0,0,0.1)'\r\n    },\r\n    annotateIcon: {\r\n      position: 'absolute' as const,\r\n      top: '10px',\r\n      right: '10px',\r\n      backgroundColor: darkMode ? 'rgba(0,0,0,0.7)' : 'rgba(255,255,255,0.7)',\r\n      borderRadius: '50%',\r\n      padding: '8px',\r\n      cursor: 'pointer',\r\n      boxShadow: '0 2px 5px rgba(0,0,0,0.2)',\r\n      zIndex: 2,\r\n      display: 'flex',\r\n      alignItems: 'center',\r\n      justifyContent: 'center'\r\n    },\r\n    textPreviewContainer: {\r\n      position: 'relative' as const,\r\n      margin: '20px 0',\r\n      borderRadius: '8px',\r\n      backgroundColor: darkMode ? '#222' : '#f5f7fa',\r\n      border: `1px solid ${darkMode ? '#444' : '#e0e0e0'}`,\r\n      padding: '15px'\r\n    },\r\n    textPreviewHeader: {\r\n      display: 'flex',\r\n      justifyContent: 'space-between',\r\n      alignItems: 'center',\r\n      marginBottom: '10px',\r\n      borderBottom: `1px solid ${darkMode ? '#444' : '#e0e0e0'}`,\r\n      paddingBottom: '8px'\r\n    },\r\n    textPreviewTitle: {\r\n      margin: 0,\r\n      fontSize: '16px',\r\n      fontWeight: '500'\r\n    },\r\n    textPreviewActions: {\r\n      display: 'flex',\r\n      gap: '10px'\r\n    },\r\n    actionButton: {\r\n      background: 'none',\r\n      border: 'none',\r\n      cursor: 'pointer',\r\n      display: 'flex',\r\n      alignItems: 'center',\r\n      justifyContent: 'center',\r\n      padding: '5px',\r\n      borderRadius: '4px',\r\n      color: darkMode ? '#aaa' : '#666',\r\n      transition: 'all 0.2s ease',\r\n      position: 'relative' as const\r\n    },\r\n    textContent: {\r\n      whiteSpace: 'pre-wrap' as const,\r\n      fontSize: '14px',\r\n      maxHeight: '300px',\r\n      overflowY: 'auto' as const,\r\n      padding: '5px'\r\n    },\r\n    tooltip: {\r\n      position: 'absolute' as const,\r\n      bottom: '100%',\r\n      left: '50%',\r\n      transform: 'translateX(-50%)',\r\n      backgroundColor: darkMode ? '#333' : '#fff',\r\n      color: darkMode ? '#fff' : '#333',\r\n      padding: '5px 10px',\r\n      borderRadius: '4px',\r\n      fontSize: '12px',\r\n      boxShadow: '0 2px 4px rgba(0,0,0,0.2)',\r\n      zIndex: 1000,\r\n      whiteSpace: 'nowrap' as const\r\n    },\r\n    annotateTooltip: {\r\n      position: 'absolute' as const,\r\n      top: '-40px',\r\n      right: '0',\r\n      backgroundColor: darkMode ? '#333' : '#fff',\r\n      color: darkMode ? '#fff' : '#333',\r\n      padding: '6px 12px',\r\n      borderRadius: '4px',\r\n      fontSize: '12px',\r\n      boxShadow: '0 2px 4px rgba(0,0,0,0.2)',\r\n      zIndex: 1000,\r\n      whiteSpace: 'nowrap' as const,\r\n      width: '180px',\r\n      textAlign: 'center' as const\r\n    },\r\n    convertTooltip: {\r\n      position: 'absolute' as const,\r\n      bottom: '100%',\r\n      left: '50%',\r\n      transform: 'translateX(-50%)',\r\n      backgroundColor: darkMode ? '#333' : '#fff',\r\n      color: darkMode ? '#fff' : '#333',\r\n      padding: '6px 12px',\r\n      borderRadius: '4px',\r\n      fontSize: '12px',\r\n      boxShadow: '0 2px 4px rgba(0,0,0,0.2)',\r\n      zIndex: 1000,\r\n      width: '220px',\r\n      textAlign: 'center' as const,\r\n      marginBottom: '5px',\r\n      whiteSpace: 'normal' as const\r\n    },\r\n    checkboxContainer: {\r\n      display: 'flex',\r\n      flexDirection: 'column' as const,\r\n      gap: '15px',\r\n      marginTop: '30px'\r\n    },\r\n    checkboxLabel: {\r\n      display: 'flex',\r\n      alignItems: 'center',\r\n      gap: '10px',\r\n      cursor: 'pointer'\r\n    }\r\n  });\r\n\r\n  const styles = getStyles();\r\n\r\n  return (\r\n    <>\r\n      <h2 style={styles.resultsHeader}>{t('analysis.imageAnalyzed')}</h2>\r\n      <div style={{textAlign: 'center', marginBottom: '20px'}}>\r\n      </div>\r\n      \r\n      <div style={styles.imagePreviewContainer}>\r\n        <img src={imageUrl} alt={t('analysis.diagramAlt')} style={styles.imagePreview} />\r\n        <div \r\n          style={styles.annotateIcon}\r\n          onClick={handleViewAnnotatedImage}\r\n          onMouseEnter={() => setShowAnnotateTooltip(true)}\r\n          onMouseLeave={() => setShowAnnotateTooltip(false)}\r\n        >\r\n          <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke={darkMode ? \"white\" : \"#333\"} strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\r\n            <path d=\"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z\"></path>\r\n            <circle cx=\"12\" cy=\"12\" r=\"3\"></circle>\r\n          </svg>\r\n          {showAnnotateTooltip && (\r\n            <div style={styles.annotateTooltip}>\r\n              {t('analysis.tooltip.viewAnnotated')}\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {extractedText && (\r\n        <div style={styles.textPreviewContainer}>\r\n          <div style={styles.textPreviewHeader}>\r\n            <h3 style={styles.textPreviewTitle}>{t('analysis.extractedText')}</h3>\r\n            <div style={styles.textPreviewActions}>\r\n              <button \r\n                onClick={copyToClipboard}\r\n                style={styles.actionButton}\r\n                onMouseOver={(e) => e.currentTarget.style.backgroundColor = darkMode ? '#444' : '#eee'}\r\n                onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'transparent'}\r\n              >\r\n                <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\r\n                  <rect x=\"9\" y=\"9\" width=\"13\" height=\"13\" rx=\"2\" ry=\"2\"></rect>\r\n                  <path d=\"M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1\"></path>\r\n                </svg>\r\n                {showCopyTooltip && (\r\n                  <span style={styles.tooltip}>Copié!</span>\r\n                )}\r\n              </button>\r\n              <button \r\n                onClick={downloadText}\r\n                style={styles.actionButton}\r\n                onMouseOver={(e) => e.currentTarget.style.backgroundColor = darkMode ? '#444' : '#eee'}\r\n                onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'transparent'}\r\n              >\r\n                <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\r\n                  <path d=\"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4\"></path>\r\n                  <polyline points=\"7 10 12 15 17 10\"></polyline>\r\n                  <line x1=\"12\" y1=\"15\" x2=\"12\" y2=\"3\"></line>\r\n                </svg>\r\n              </button>\r\n              <button \r\n                onClick={handleNavigateToUMLExtractor}\r\n                style={styles.actionButton}\r\n                onMouseOver={(e) => {\r\n                  e.currentTarget.style.backgroundColor = darkMode ? '#444' : '#eee';\r\n                  setShowConvertTooltip(true);\r\n                }}\r\n                onMouseOut={(e) => {\r\n                  e.currentTarget.style.backgroundColor = 'transparent';\r\n                  setShowConvertTooltip(false);\r\n                }}\r\n              >\r\n                <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\r\n                  <path d=\"M5 12h14M13 5l7 7-7 7\" />\r\n                </svg>\r\n                {showConvertTooltip && (\r\n                  <span style={styles.convertTooltip}>\r\n                    {t('analysis.tooltip.convertDiagram')}\r\n                  </span>\r\n                )}\r\n              </button>\r\n            </div>\r\n          </div>\r\n          <div style={styles.textContent} ref={textContentRef}>\r\n            {renderTextWithClickableEntities()}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Popup pour l'entité sélectionnée */}\r\n      {selectedEntity && (\r\n        <EntityPopup\r\n          darkMode={darkMode}\r\n          entityName={selectedEntity}\r\n          position={popupPosition}\r\n          onClose={closePopup}\r\n          onModify={handleModifyEntity}\r\n          memoryAttributes={selectedClassData.memoryAttributes}\r\n          memoryMethods={selectedClassData.memoryMethods}\r\n          extractedAttributes={selectedClassData.extractedAttributes}\r\n          extractedMethods={selectedClassData.extractedMethods}\r\n          currentDiagramText={extractedText}\r\n        />\r\n      )}\r\n\r\n      <div style={styles.checkboxContainer}>\r\n        <button \r\n          style={{\r\n            display: \"flex\",\r\n            alignItems: \"center\",\r\n            justifyContent: \"center\",\r\n            gap: \"6px\",\r\n            padding: \"0.5rem\",\r\n            backgroundColor: \"rgba(59, 130, 246, 0.1)\",\r\n            color: \"#60a5fa\",\r\n            border: \"1px solid rgba(96, 165, 250, 0.2)\",\r\n            borderRadius: \"6px\",\r\n            fontSize: \"0.875rem\",\r\n            fontWeight: \"500\",\r\n            cursor: \"pointer\",\r\n            width: \"100%\",\r\n            boxSizing: \"border-box\"\r\n          }}\r\n          onClick={resetAnalysis}\r\n        >\r\n          <svg width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\r\n            <line x1=\"12\" y1=\"5\" x2=\"12\" y2=\"19\"></line>\r\n            <line x1=\"5\" y1=\"12\" x2=\"19\" y2=\"12\"></line>\r\n          </svg>\r\n          Nouveau Projet\r\n        </button>\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default AnalysisResults;\r\n"], "mappings": ";;AAAA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,WAAW,QAAQ,+BAA+B;;AAE3D;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAwBA,MAAMC,eAA+C,GAAGA,CAAC;EACvDC,QAAQ;EACRC,QAAQ;EACRC,aAAa;EACbC,oBAAoB;EACpBC,wBAAwB;EACxBC,aAAa;EACbC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM;IAAEC;EAAE,CAAC,GAAGd,WAAW,CAAC,CAAC;EAC3B,MAAM,CAACe,eAAe,EAAEC,kBAAkB,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACqB,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACuB,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACyB,cAAc,EAAEC,iBAAiB,CAAC,GAAG1B,QAAQ,CAAgB,IAAI,CAAC;EACzE,MAAM,CAAC2B,aAAa,EAAEC,gBAAgB,CAAC,GAAG5B,QAAQ,CAAC;IAAE6B,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;EAClE,MAAMC,cAAc,GAAG9B,MAAM,CAAiB,IAAI,CAAC;;EAEnD;EACA,MAAM,CAAC+B,UAAU,EAAEC,aAAa,CAAC,GAAGjC,QAAQ,CAAc,EAAE,CAAC;EAC7D,MAAM,CAACkC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnC,QAAQ,CAAc,EAAE,CAAC;EACzE,MAAM,CAACoC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrC,QAAQ,CAAoB;IAC5EsC,gBAAgB,EAAE,EAAE;IACpBC,aAAa,EAAE,EAAE;IACjBC,mBAAmB,EAAE,EAAE;IACvBC,gBAAgB,EAAE;EACpB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG3C,QAAQ,CAASY,aAAa,CAAC;;EAEnF;EACAV,SAAS,CAAC,MAAM;IACdyC,qBAAqB,CAAC/B,aAAa,CAAC;EACtC,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;;EAEnB;EACA,MAAMgC,kBAAkB,GAAIC,IAAY,IAAK;IAC3C,IAAI,CAACA,IAAI,EAAE;IAEX,MAAMC,OAAoB,GAAG,EAAE;IAC/B,MAAMC,aAAa,GAAGF,IAAI,CAACG,KAAK,CAAC,aAAa,CAAC;;IAE/C;IACA,IAAID,aAAa,CAAC,CAAC,CAAC,CAACE,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAClCF,aAAa,CAACG,KAAK,CAAC,CAAC;IACvB;IAEAH,aAAa,CAACI,OAAO,CAACC,OAAO,IAAI;MAC/B,IAAI,CAACA,OAAO,CAACH,IAAI,CAAC,CAAC,EAAE;MAErB,MAAMI,cAAc,GAAGD,OAAO,CAACE,KAAK,CAAC,wBAAwB,CAAC;MAC9D,IAAID,cAAc,EAAE;QAClB,MAAME,SAAS,GAAGF,cAAc,CAAC,CAAC,CAAC,CAACJ,IAAI,CAAC,CAAC;;QAE1C;QACA,MAAMO,eAAe,GAAGJ,OAAO,CAACE,KAAK,CAAC,qCAAqC,CAAC;QAC5E,MAAMG,UAAU,GAAGD,eAAe,GAChCA,eAAe,CAAC,CAAC,CAAC,CAACP,IAAI,CAAC,CAAC,CAACD,KAAK,CAAC,IAAI,CAAC,CAACU,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACV,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE;;QAExE;QACA,MAAMW,YAAY,GAAGR,OAAO,CAACE,KAAK,CAAC,qCAAqC,CAAC;QACzE,MAAMO,OAAO,GAAGD,YAAY,GAC1BA,YAAY,CAAC,CAAC,CAAC,CAACX,IAAI,CAAC,CAAC,CAACD,KAAK,CAAC,IAAI,CAAC,CAACU,MAAM,CAACI,MAAM,IAAIA,MAAM,CAACb,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE;QAEzEH,OAAO,CAACiB,IAAI,CAAC;UACXC,IAAI,EAAET,SAAS;UACfE,UAAU;UACVI;QACF,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF1B,mBAAmB,CAACW,OAAO,CAAC;EAC9B,CAAC;;EAED;EACA5C,SAAS,CAAC,MAAM;IACd+D,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA/D,SAAS,CAAC,MAAM;IACd,IAAIU,aAAa,EAAE;MACjBgC,kBAAkB,CAAChC,aAAa,CAAC;IACnC;EACF,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;;EAEnB;EACAV,SAAS,CAAC,MAAM;IACdgE,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEvD,aAAa,CAACwD,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC;IACpG;IACA,IAAIxD,aAAa,EAAE;MACjBgC,kBAAkB,CAAChC,aAAa,CAAC;IACnC;EACF,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;EAEnB,MAAMyD,eAAe,GAAGA,CAAA,KAAM;IAC5B;IACA,IAAI3B,kBAAkB,EAAE;MACtB4B,SAAS,CAACC,SAAS,CAACC,SAAS,CAAC9B,kBAAkB,CAAC,CAC9C+B,IAAI,CAAC,MAAM;QACVrD,kBAAkB,CAAC,IAAI,CAAC;QACxBsD,UAAU,CAAC,MAAMtD,kBAAkB,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;MACnD,CAAC,CAAC,CACDuD,KAAK,CAACC,GAAG,IAAI;QACZV,OAAO,CAACW,KAAK,CAAC,kDAAkD,EAAED,GAAG,CAAC;MACxE,CAAC,CAAC;IACN;EACF,CAAC;EAED,MAAME,YAAY,GAAGA,CAAA,KAAM;IACzB;IACA,IAAIpC,kBAAkB,EAAE;MACtB,MAAMqC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACtC,kBAAkB,CAAC,EAAE;QAAEuC,IAAI,EAAE;MAAa,CAAC,CAAC;MACnE,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;MACrC,MAAMM,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACrCF,CAAC,CAACG,IAAI,GAAGN,GAAG;MACZG,CAAC,CAACI,QAAQ,GAAG,uBAAuB;MACpCH,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,CAAC,CAAC;MAC5BA,CAAC,CAACO,KAAK,CAAC,CAAC;MACTN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,CAAC,CAAC;MAC5BF,GAAG,CAACW,eAAe,CAACZ,GAAG,CAAC;IAC1B;EACF,CAAC;EAED,MAAMa,wBAAwB,GAAGA,CAAA,KAAM;IACrC,IAAIlF,oBAAoB,EAAE;MACxBA,oBAAoB,CAAC,CAAC;IACxB;EACF,CAAC;EAED,MAAMmF,4BAA4B,GAAGA,CAAA,KAAM;IACzC,IAAIlF,wBAAwB,EAAE;MAC5BA,wBAAwB,CAAC,CAAC;IAC5B;EACF,CAAC;;EAED;EACA,MAAMmD,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;MACpE;MACA,MAAM8B,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;MACtC,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,wCAAwCJ,SAAS,EAAE,EAAE;QAChFK,KAAK,EAAE,UAAU;QACjBC,OAAO,EAAE;UACP,eAAe,EAAE,UAAU;UAC3B,QAAQ,EAAE;QACZ;MACF,CAAC,CAAC;MACF,IAAI,CAACH,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,+CAA+CL,QAAQ,CAACM,MAAM,IAAIN,QAAQ,CAACO,UAAU,EAAE,CAAC;MAC1G;MAEA,MAAM9D,IAAI,GAAG,MAAMuD,QAAQ,CAACvD,IAAI,CAAC,CAAC;MAClCqB,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEtB,IAAI,CAACuB,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;MACpFF,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEtB,IAAI,CAAC+D,MAAM,EAAE,YAAY,CAAC;;MAE/D;MACA;MACA,MAAMC,UAAU,GAAG,cAAc;MACjC,IAAIvD,KAAK;MACT,MAAMwD,YAAY,GAAG,EAAE;MAEvB,OAAO,CAACxD,KAAK,GAAGuD,UAAU,CAACE,IAAI,CAAClE,IAAI,CAAC,MAAM,IAAI,EAAE;QAC/CiE,YAAY,CAAC/C,IAAI,CAACT,KAAK,CAAC0D,KAAK,CAAC;MAChC;MAEA,MAAMC,UAAuB,GAAG,EAAE;;MAElC;MACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,YAAY,CAACF,MAAM,EAAEM,CAAC,EAAE,EAAE;QAC5C,MAAMC,QAAQ,GAAGL,YAAY,CAACI,CAAC,CAAC;QAChC,MAAME,MAAM,GAAGF,CAAC,GAAGJ,YAAY,CAACF,MAAM,GAAG,CAAC,GAAGE,YAAY,CAACI,CAAC,GAAG,CAAC,CAAC,GAAGrE,IAAI,CAAC+D,MAAM;QAC9E,MAAMS,UAAU,GAAGxE,IAAI,CAACuB,SAAS,CAAC+C,QAAQ,EAAEC,MAAM,CAAC,CAACnE,IAAI,CAAC,CAAC;QAE1D,MAAMqE,KAAK,GAAGD,UAAU,CAACrE,KAAK,CAAC,IAAI,CAAC;QACpC,IAAIO,SAAS,GAAG,EAAE;QAClB,MAAME,UAAoB,GAAG,EAAE;QAC/B,MAAMI,OAAiB,GAAG,EAAE;QAE5B,IAAI0D,cAAc,GAAG,EAAE;QAEvB,KAAK,MAAMC,IAAI,IAAIF,KAAK,EAAE;UACxB,IAAIE,IAAI,CAACC,UAAU,CAAC,aAAa,CAAC,EAAE;YAClClE,SAAS,GAAGiE,IAAI,CAACE,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,CAACzE,IAAI,CAAC,CAAC;UACpD,CAAC,MAAM,IAAIuE,IAAI,CAACC,UAAU,CAAC,YAAY,CAAC,EAAE;YACxCF,cAAc,GAAG,YAAY;UAC/B,CAAC,MAAM,IAAIC,IAAI,CAACC,UAAU,CAAC,WAAW,CAAC,EAAE;YACvCF,cAAc,GAAG,SAAS;UAC5B,CAAC,MAAM,IAAIC,IAAI,CAACvE,IAAI,CAAC,CAAC,IAAIsE,cAAc,KAAK,YAAY,EAAE;YACzD9D,UAAU,CAACM,IAAI,CAACyD,IAAI,CAACvE,IAAI,CAAC,CAAC,CAAC;UAC9B,CAAC,MAAM,IAAIuE,IAAI,CAACvE,IAAI,CAAC,CAAC,IAAIsE,cAAc,KAAK,SAAS,EAAE;YACtD1D,OAAO,CAACE,IAAI,CAACyD,IAAI,CAACvE,IAAI,CAAC,CAAC,CAAC;UAC3B;QACF;QAEA,IAAIM,SAAS,EAAE;UACbW,OAAO,CAACC,GAAG,CAAC,kBAAkBZ,SAAS,SAASE,UAAU,CAACmD,MAAM,iBAAiB/C,OAAO,CAAC+C,MAAM,WAAW,CAAC;UAC5GK,UAAU,CAAClD,IAAI,CAAC;YAAEC,IAAI,EAAET,SAAS;YAAEE,UAAU;YAAEI;UAAQ,CAAC,CAAC;QAC3D;MACF;MAEAK,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE8C,UAAU,CAACU,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC5D,IAAI,CAAC,CAAC6D,IAAI,CAAC,IAAI,CAAC,CAAC;MAClF5F,aAAa,CAACgF,UAAU,CAAC;IAC3B,CAAC,CAAC,OAAOpC,KAAK,EAAE;MACdX,OAAO,CAACW,KAAK,CAAC,oDAAoD,EAAEA,KAAK,CAAC;IAC5E;EACF,CAAC;;EAED;EACA,MAAMiD,iBAAiB,GAAIC,KAAwC,IAAK;IACtE,MAAMC,UAAU,GAAGD,KAAK,CAACE,aAAa,CAACC,YAAY,CAAC,aAAa,CAAC;IAClE,IAAIF,UAAU,EAAE;MACd9D,OAAO,CAACC,GAAG,CAAC,wBAAwB6D,UAAU,EAAE,CAAC;MACjD9D,OAAO,CAACC,GAAG,CAAC,+BAA+BnC,UAAU,CAAC2F,GAAG,CAACQ,CAAC,IAAIA,CAAC,CAACnE,IAAI,CAAC,CAAC6D,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;MAEpF,MAAMO,IAAI,GAAGL,KAAK,CAACE,aAAa,CAACI,qBAAqB,CAAC,CAAC;MACxDzG,gBAAgB,CAAC;QACfC,CAAC,EAAEuG,IAAI,CAACE,IAAI;QACZxG,CAAC,EAAEsG,IAAI,CAACG,MAAM,GAAGC,MAAM,CAACC;MAC1B,CAAC,CAAC;;MAEF;MACA,MAAMC,aAAa,GAAG1G,UAAU,CAAC0B,MAAM,CAACyE,CAAC,IAAIA,CAAC,CAACnE,IAAI,CAAC2E,WAAW,CAAC,CAAC,KAAKX,UAAU,CAACW,WAAW,CAAC,CAAC,CAAC;MAC/FzE,OAAO,CAACC,GAAG,CAAC,wBAAwBuE,aAAa,CAAC9B,MAAM,2BAA2BoB,UAAU,IAAI,EAAEU,aAAa,CAACf,GAAG,CAACQ,CAAC,IAAIA,CAAC,CAACnE,IAAI,CAAC,CAAC;;MAElI;MACA0E,aAAa,CAACvF,OAAO,CAAC,CAACyF,GAAG,EAAE5B,KAAK,KAAK;QACpC9C,OAAO,CAACC,GAAG,CAAC,aAAa6C,KAAK,GAAG,CAAC,KAAK4B,GAAG,CAAC5E,IAAI,IAAI,EAAE;UACnDP,UAAU,EAAEmF,GAAG,CAACnF,UAAU;UAC1BI,OAAO,EAAE+E,GAAG,CAAC/E;QACf,CAAC,CAAC;MACJ,CAAC,CAAC;;MAEF;MACA,MAAMgF,gBAAgB,GAAG;QACvBpF,UAAU,EAAE,EAAc;QAC1BI,OAAO,EAAE;MACX,CAAC;MAED6E,aAAa,CAACvF,OAAO,CAACyF,GAAG,IAAI;QAC3B;QACAA,GAAG,CAACnF,UAAU,CAACN,OAAO,CAACQ,IAAI,IAAI;UAC7B,IAAI,CAACkF,gBAAgB,CAACpF,UAAU,CAACqF,QAAQ,CAACnF,IAAI,CAAC,EAAE;YAC/CkF,gBAAgB,CAACpF,UAAU,CAACM,IAAI,CAACJ,IAAI,CAAC;UACxC;QACF,CAAC,CAAC;;QAEF;QACAiF,GAAG,CAAC/E,OAAO,CAACV,OAAO,CAACW,MAAM,IAAI;UAC5B,IAAI,CAAC+E,gBAAgB,CAAChF,OAAO,CAACiF,QAAQ,CAAChF,MAAM,CAAC,EAAE;YAC9C+E,gBAAgB,CAAChF,OAAO,CAACE,IAAI,CAACD,MAAM,CAAC;UACvC;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;MAEFI,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE0E,gBAAgB,CAAC;;MAEjE;MACA3E,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;MACxED,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE0E,gBAAgB,CAACpF,UAAU,CAAC;MAChES,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE0E,gBAAgB,CAAChF,OAAO,CAAC;;MAE1D;MACAK,OAAO,CAACC,GAAG,CAAC,kFAAkF,CAAC;MAC/FD,OAAO,CAACC,GAAG,CAAC,2FAA2F,CAAC;;MAExG;MACA,MAAM4E,cAAc,GAAG7G,gBAAgB,CAAC8G,IAAI,CAACb,CAAC,IAAIA,CAAC,CAACnE,IAAI,CAAC2E,WAAW,CAAC,CAAC,KAAKX,UAAU,CAACW,WAAW,CAAC,CAAC,CAAC;MACpGzE,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAE4E,cAAc,CAAC;;MAE1E;MACA,MAAME,WAAW,GAAG;QAClB3G,gBAAgB,EAAEuG,gBAAgB,CAACpF,UAAU;QAC7ClB,aAAa,EAAEsG,gBAAgB,CAAChF,OAAO;QACvCrB,mBAAmB,EAAE,CAAAuG,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEtF,UAAU,KAAI,EAAE;QACrDhB,gBAAgB,EAAE,CAAAsG,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAElF,OAAO,KAAI;MAC/C,CAAC;MAEDK,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE8E,WAAW,CAAC;MAC/D5G,oBAAoB,CAAC4G,WAAW,CAAC;MAEjCvH,iBAAiB,CAACsG,UAAU,CAAC;IAC/B;EACF,CAAC;EAED,MAAMkB,UAAU,GAAGA,CAAA,KAAM;IACvBxH,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,MAAMyH,uBAAuB,GAAG,MAAOC,WAAmB,IAAK;IAC7D,IAAI;MACFlF,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MACtD,MAAMiC,QAAQ,GAAG,MAAMC,KAAK,CAAC,oCAAoC,EAAE;QACjEvC,MAAM,EAAE,MAAM;QACdyC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDb,IAAI,EAAE2D,IAAI,CAACC,SAAS,CAAC;UAAEzG,IAAI,EAAEuG;QAAY,CAAC;MAC5C,CAAC,CAAC;MAEF,IAAI,CAAChD,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,gBAAgBL,QAAQ,CAACM,MAAM,EAAE,CAAC;MACpD;MAEA,MAAM6C,MAAM,GAAG,MAAMnD,QAAQ,CAACoD,IAAI,CAAC,CAAC;MACpCtF,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEoF,MAAM,CAAC;MACvD,OAAO,IAAI;IACb,CAAC,CAAC,OAAO1E,KAAK,EAAE;MACdX,OAAO,CAACW,KAAK,CAAC,wDAAwD,EAAEA,KAAK,CAAC;MAC9E,OAAO,KAAK;IACd;EACF,CAAC;;EAED;EACF,MAAM4E,kBAAkB,GAAG,MAAAA,CAAOC,kBAA4B,EAAEC,eAAyB,KAAK;IAC5F,IAAI,CAAClI,cAAc,IAAK,CAACiI,kBAAkB,CAAC9C,MAAM,IAAI,CAAC+C,eAAe,CAAC/C,MAAO,EAAE;MAC9EsC,UAAU,CAAC,CAAC;MACZ;IACF;IAEAhF,OAAO,CAACC,GAAG,CAAC,6BAA6B1C,cAAc,EAAE,CAAC;IAC1DyC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEuF,kBAAkB,CAAC;IAC1DxF,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEwF,eAAe,CAAC;;IAEvD;IACA,IAAIC,gBAAgB,GAAGlH,kBAAkB;IACzCwB,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEyF,gBAAgB,CAACxF,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC;;IAElF;IACA,MAAMyF,gBAAgB,GAAGpI,cAAc,CAACiG,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC;;IAE9E;IACA,MAAMoC,YAAY,GAAG,IAAIC,MAAM,CAC7B,kCAAkCF,gBAAgB,4CAA4C,EAC9F,GACF,CAAC;IAED,MAAMG,UAAU,GAAGJ,gBAAgB,CAACtG,KAAK,CAACwG,YAAY,CAAC;IAEvD,IAAI,CAACE,UAAU,EAAE;MACf9F,OAAO,CAACW,KAAK,CAAC,mCAAmC,EAAEpD,cAAc,CAAC;MAClEyH,UAAU,CAAC,CAAC;MACZ;IACF;IAEA,IAAIe,YAAY,GAAGD,UAAU,CAAC,CAAC,CAAC;IAChC9F,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE8F,YAAY,CAAC;;IAEzD;IACA,IAAIP,kBAAkB,CAAC9C,MAAM,GAAG,CAAC,EAAE;MACjC;MACA,MAAMsD,eAAe,GAAG,0CAA0C;MAClE,MAAM1G,eAAe,GAAGyG,YAAY,CAAC3G,KAAK,CAAC4G,eAAe,CAAC;MAE3D,IAAI1G,eAAe,EAAE;QACnB,MAAM2G,gBAAgB,GAAG3G,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7C,MAAM4G,kBAAkB,GAAG5G,eAAe,CAAC,CAAC,CAAC,CAACP,IAAI,CAAC,CAAC,CAAC,CAAC;;QAEtD;QACA,IAAIoH,oBAAoB,GAAGF,gBAAgB;;QAE3C;QACA,IAAIC,kBAAkB,EAAE;UACtBC,oBAAoB,IAAI,IAAI,GAAGD,kBAAkB;QACnD;;QAEA;QACAV,kBAAkB,CAACvG,OAAO,CAACQ,IAAI,IAAI;UACjC0G,oBAAoB,IAAI,IAAI,GAAG1G,IAAI;QACrC,CAAC,CAAC;;QAEF;QACA0G,oBAAoB,IAAI,IAAI;;QAE5B;QACAJ,YAAY,GAAGA,YAAY,CAACvC,OAAO,CAACwC,eAAe,EAAEG,oBAAoB,CAAC;QAE1EnG,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC9C;IACF;;IAEA;IACA,IAAIwF,eAAe,CAAC/C,MAAM,GAAG,CAAC,EAAE;MAC9B;MACA,MAAM0D,YAAY,GAAG,sCAAsC;MAC3D,MAAM1G,YAAY,GAAGqG,YAAY,CAAC3G,KAAK,CAACgH,YAAY,CAAC;MAErD,IAAI1G,YAAY,EAAE;QAChB;QACA,MAAM2G,aAAa,GAAG3G,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;QACvC,MAAM4G,eAAe,GAAG5G,YAAY,CAAC,CAAC,CAAC,CAACX,IAAI,CAAC,CAAC,CAAC,CAAC;;QAEhD;QACA,IAAIwH,iBAAiB,GAAGF,aAAa;;QAErC;QACA,IAAIC,eAAe,EAAE;UACnBC,iBAAiB,IAAI,IAAI,GAAGD,eAAe;QAC7C;;QAEA;QACAb,eAAe,CAACxG,OAAO,CAACW,MAAM,IAAI;UAChC2G,iBAAiB,IAAI,IAAI,GAAG3G,MAAM;QACpC,CAAC,CAAC;;QAEF;QACA2G,iBAAiB,IAAI,MAAM;;QAE3B;QACAR,YAAY,GAAGA,YAAY,CAACvC,OAAO,CAAC4C,YAAY,EAAEG,iBAAiB,CAAC;QAEpEvG,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC9C,CAAC,MAAM;QACL;QACA,MAAMuG,kBAAkB,GAAG,4BAA4B;QACvD,IAAIT,YAAY,CAAC3G,KAAK,CAACoH,kBAAkB,CAAC,EAAE;UAC1C,IAAIC,cAAc,GAAG,aAAa;UAClChB,eAAe,CAACxG,OAAO,CAACW,MAAM,IAAI;YAChC6G,cAAc,IAAI,IAAI,GAAG7G,MAAM;UACjC,CAAC,CAAC;UACF6G,cAAc,IAAI,MAAM;UAExBV,YAAY,GAAGA,YAAY,CAACvC,OAAO,CAACgD,kBAAkB,EAAE,IAAI,GAAGC,cAAc,CAAC;UAC9EzG,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;QAC/D;MACF;IACF;;IAEA;IACA,MAAMiF,WAAW,GAAGQ,gBAAgB,CAAClC,OAAO,CAACoC,YAAY,EAAEG,YAAY,CAAC;IAExE/F,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEiF,WAAW,CAAChF,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC;;IAEvE;IACAzB,qBAAqB,CAACyG,WAAW,CAAC;IAClCxG,kBAAkB,CAACwG,WAAW,CAAC;;IAE/B;IACA,MAAMwB,OAAO,GAAG,MAAMzB,uBAAuB,CAACC,WAAW,CAAC;IAE1D,IAAIwB,OAAO,EAAE;MACX;MACA,IAAI5J,qBAAqB,EAAE;QACzBkD,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;QAC7CnD,qBAAqB,CAACoI,WAAW,CAAC;MACpC,CAAC,MAAM;QACLlF,OAAO,CAACW,KAAK,CAAC,wCAAwC,CAAC;MACzD;IACF,CAAC,MAAM;MACLX,OAAO,CAACW,KAAK,CAAC,iDAAiD,CAAC;MAChE;MACAlC,qBAAqB,CAAC/B,aAAa,CAAC;MACpCgC,kBAAkB,CAAChC,aAAa,CAAC;MACjCiK,KAAK,CAAC3J,CAAC,CAAC,6BAA6B,CAAC,CAAC;IACzC;;IAEA;IACAgI,UAAU,CAAC,CAAC;EACd,CAAC;;EAEC;EACAhJ,SAAS,CAAC,MAAM;IACd,MAAM4K,kBAAkB,GAAI/C,KAAiB,IAAK;MAChD,IAAItG,cAAc,EAAE;QAClB;QACA,MAAMsJ,MAAM,GAAGhD,KAAK,CAACgD,MAAqB;;QAE1C;QACA,IAAIA,MAAM,CAACC,KAAK,CAACC,QAAQ,KAAK,OAAO,IACjCF,MAAM,CAACC,KAAK,CAACE,GAAG,KAAK,KAAK,IAC1BH,MAAM,CAACC,KAAK,CAAC1C,IAAI,KAAK,KAAK,IAC3ByC,MAAM,CAACC,KAAK,CAACG,KAAK,KAAK,KAAK,IAC5BJ,MAAM,CAACC,KAAK,CAACzC,MAAM,KAAK,KAAK,EAAE;UACjCW,UAAU,CAAC,CAAC;QACd;MACF;IACF,CAAC;IAED5D,QAAQ,CAAC8F,gBAAgB,CAAC,WAAW,EAAEN,kBAAkB,CAAC;IAC1D,OAAO,MAAM;MACXxF,QAAQ,CAAC+F,mBAAmB,CAAC,WAAW,EAAEP,kBAAkB,CAAC;IAC/D,CAAC;EACH,CAAC,EAAE,CAACrJ,cAAc,CAAC,CAAC;;EAEpB;EACA;EACA,MAAM6J,+BAA+B,GAAGA,CAAA,KAAM;IAC5C,IAAI,CAAC5I,kBAAkB,EAAE,OAAO,IAAI;IACpCwB,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEzB,kBAAkB,CAAC0B,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC;;IAEpG;IACA,MAAMrB,aAAa,GAAGL,kBAAkB,CAACM,KAAK,CAAC,aAAa,CAAC;;IAE7D;IACA,MAAMuI,QAAQ,GAAGxI,aAAa,CAAC,CAAC,CAAC,CAACE,IAAI,CAAC,CAAC,KAAK,EAAE,GAAGF,aAAa,CAACyI,KAAK,CAAC,CAAC,CAAC,GAAGzI,aAAa;IAExF,oBACEzC,OAAA,CAAAE,SAAA;MAAAiL,QAAA,EACGF,QAAQ,CAAC5D,GAAG,CAAC,CAACvE,OAAO,EAAE4D,KAAK,KAAK;QAChC,IAAI,CAAC5D,OAAO,CAACH,IAAI,CAAC,CAAC,EAAE,OAAO,IAAI;;QAEhC;QACA,MAAMI,cAAc,GAAGD,OAAO,CAACE,KAAK,CAAC,wBAAwB,CAAC;QAC9D,IAAI,CAACD,cAAc,IAAIA,cAAc,CAAC2D,KAAK,KAAK0E,SAAS,EAAE;UACzD,oBACEpL,OAAA;YAAAmL,QAAA,gBACEnL,OAAA;cAAAmL,QAAA,GAAK,QAAM,EAACzE,KAAK,GAAC,CAAC,EAAC,GAAC;YAAA;cAAA2E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EAC1B1I,OAAO;UAAA,GAFA4D,KAAK;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGV,CAAC;QAEV;QAEA,MAAMvI,SAAS,GAAGF,cAAc,CAAC,CAAC,CAAC,CAACJ,IAAI,CAAC,CAAC;QAC1C,MAAM8I,UAAU,GAAG1I,cAAc,CAAC2D,KAAK;QACvC,MAAMgF,eAAe,GAAG5I,OAAO,CAACgB,SAAS,CAAC,CAAC,EAAE2H,UAAU,GAAG,EAAE,CAAC,CAAC,CAAC;QAC/D,MAAME,cAAc,GAAG7I,OAAO,CAACgB,SAAS,CAAC2H,UAAU,GAAG,EAAE,GAAGxI,SAAS,CAACqD,MAAM,CAAC;QAE5E,oBACEtG,OAAA;UAAAmL,QAAA,gBACEnL,OAAA;YAAAmL,QAAA,GAAK,QAAM,EAACzE,KAAK,GAAC,CAAC,EAAC,GAAC;UAAA;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,EAC1BE,eAAe,eAChB1L,OAAA;YACE0K,KAAK,EAAE;cACLkB,MAAM,EAAE,SAAS;cACjBC,KAAK,EAAE,SAAS;cAChBC,UAAU,EAAE,MAAM;cAClBC,cAAc,EAAE;YAClB,CAAE;YACF,eAAa9I,SAAU;YACvB+I,OAAO,EAAExE,iBAAkB;YAAA2D,QAAA,EAE1BlI;UAAS;YAAAoI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EACNG,cAAc;QAAA,GAfPjF,KAAK;UAAA2E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAgBV,CAAC;MAEV,CAAC;IAAC,gBACF,CAAC;EAEP,CAAC;EAED,MAAMS,SAAS,GAAGA,CAAA,MAAO;IACvBC,aAAa,EAAE;MACbC,SAAS,EAAE,QAAiB;MAC5BC,YAAY,EAAE,MAAM;MACpBP,KAAK,EAAEzL,QAAQ,GAAG,SAAS,GAAG;IAChC,CAAC;IACDiM,qBAAqB,EAAE;MACrB1B,QAAQ,EAAE,UAAmB;MAC7B2B,KAAK,EAAE,MAAM;MACbC,QAAQ,EAAE,OAAO;MACjBC,MAAM,EAAE;IACV,CAAC;IACDC,YAAY,EAAE;MACZH,KAAK,EAAE,MAAM;MACbI,YAAY,EAAE,KAAK;MACnBC,OAAO,EAAE,OAAO;MAChBC,SAAS,EAAExM,QAAQ,GAAG,2BAA2B,GAAG;IACtD,CAAC;IACDyM,YAAY,EAAE;MACZlC,QAAQ,EAAE,UAAmB;MAC7BC,GAAG,EAAE,MAAM;MACXC,KAAK,EAAE,MAAM;MACbiC,eAAe,EAAE1M,QAAQ,GAAG,iBAAiB,GAAG,uBAAuB;MACvEsM,YAAY,EAAE,KAAK;MACnBK,OAAO,EAAE,KAAK;MACdnB,MAAM,EAAE,SAAS;MACjBgB,SAAS,EAAE,2BAA2B;MACtCI,MAAM,EAAE,CAAC;MACTL,OAAO,EAAE,MAAM;MACfM,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE;IAClB,CAAC;IACDC,oBAAoB,EAAE;MACpBxC,QAAQ,EAAE,UAAmB;MAC7B6B,MAAM,EAAE,QAAQ;MAChBE,YAAY,EAAE,KAAK;MACnBI,eAAe,EAAE1M,QAAQ,GAAG,MAAM,GAAG,SAAS;MAC9CgN,MAAM,EAAE,aAAahN,QAAQ,GAAG,MAAM,GAAG,SAAS,EAAE;MACpD2M,OAAO,EAAE;IACX,CAAC;IACDM,iBAAiB,EAAE;MACjBV,OAAO,EAAE,MAAM;MACfO,cAAc,EAAE,eAAe;MAC/BD,UAAU,EAAE,QAAQ;MACpBb,YAAY,EAAE,MAAM;MACpBkB,YAAY,EAAE,aAAalN,QAAQ,GAAG,MAAM,GAAG,SAAS,EAAE;MAC1DmN,aAAa,EAAE;IACjB,CAAC;IACDC,gBAAgB,EAAE;MAChBhB,MAAM,EAAE,CAAC;MACTiB,QAAQ,EAAE,MAAM;MAChB3B,UAAU,EAAE;IACd,CAAC;IACD4B,kBAAkB,EAAE;MAClBf,OAAO,EAAE,MAAM;MACfgB,GAAG,EAAE;IACP,CAAC;IACDC,YAAY,EAAE;MACZC,UAAU,EAAE,MAAM;MAClBT,MAAM,EAAE,MAAM;MACdxB,MAAM,EAAE,SAAS;MACjBe,OAAO,EAAE,MAAM;MACfM,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBH,OAAO,EAAE,KAAK;MACdL,YAAY,EAAE,KAAK;MACnBb,KAAK,EAAEzL,QAAQ,GAAG,MAAM,GAAG,MAAM;MACjC0N,UAAU,EAAE,eAAe;MAC3BnD,QAAQ,EAAE;IACZ,CAAC;IACDoD,WAAW,EAAE;MACXC,UAAU,EAAE,UAAmB;MAC/BP,QAAQ,EAAE,MAAM;MAChBQ,SAAS,EAAE,OAAO;MAClBC,SAAS,EAAE,MAAe;MAC1BnB,OAAO,EAAE;IACX,CAAC;IACDoB,OAAO,EAAE;MACPxD,QAAQ,EAAE,UAAmB;MAC7B1C,MAAM,EAAE,MAAM;MACdD,IAAI,EAAE,KAAK;MACXoG,SAAS,EAAE,kBAAkB;MAC7BtB,eAAe,EAAE1M,QAAQ,GAAG,MAAM,GAAG,MAAM;MAC3CyL,KAAK,EAAEzL,QAAQ,GAAG,MAAM,GAAG,MAAM;MACjC2M,OAAO,EAAE,UAAU;MACnBL,YAAY,EAAE,KAAK;MACnBe,QAAQ,EAAE,MAAM;MAChBb,SAAS,EAAE,2BAA2B;MACtCI,MAAM,EAAE,IAAI;MACZgB,UAAU,EAAE;IACd,CAAC;IACDK,eAAe,EAAE;MACf1D,QAAQ,EAAE,UAAmB;MAC7BC,GAAG,EAAE,OAAO;MACZC,KAAK,EAAE,GAAG;MACViC,eAAe,EAAE1M,QAAQ,GAAG,MAAM,GAAG,MAAM;MAC3CyL,KAAK,EAAEzL,QAAQ,GAAG,MAAM,GAAG,MAAM;MACjC2M,OAAO,EAAE,UAAU;MACnBL,YAAY,EAAE,KAAK;MACnBe,QAAQ,EAAE,MAAM;MAChBb,SAAS,EAAE,2BAA2B;MACtCI,MAAM,EAAE,IAAI;MACZgB,UAAU,EAAE,QAAiB;MAC7B1B,KAAK,EAAE,OAAO;MACdH,SAAS,EAAE;IACb,CAAC;IACDmC,cAAc,EAAE;MACd3D,QAAQ,EAAE,UAAmB;MAC7B1C,MAAM,EAAE,MAAM;MACdD,IAAI,EAAE,KAAK;MACXoG,SAAS,EAAE,kBAAkB;MAC7BtB,eAAe,EAAE1M,QAAQ,GAAG,MAAM,GAAG,MAAM;MAC3CyL,KAAK,EAAEzL,QAAQ,GAAG,MAAM,GAAG,MAAM;MACjC2M,OAAO,EAAE,UAAU;MACnBL,YAAY,EAAE,KAAK;MACnBe,QAAQ,EAAE,MAAM;MAChBb,SAAS,EAAE,2BAA2B;MACtCI,MAAM,EAAE,IAAI;MACZV,KAAK,EAAE,OAAO;MACdH,SAAS,EAAE,QAAiB;MAC5BC,YAAY,EAAE,KAAK;MACnB4B,UAAU,EAAE;IACd,CAAC;IACDO,iBAAiB,EAAE;MACjB5B,OAAO,EAAE,MAAM;MACf6B,aAAa,EAAE,QAAiB;MAChCb,GAAG,EAAE,MAAM;MACXc,SAAS,EAAE;IACb,CAAC;IACDC,aAAa,EAAE;MACb/B,OAAO,EAAE,MAAM;MACfM,UAAU,EAAE,QAAQ;MACpBU,GAAG,EAAE,MAAM;MACX/B,MAAM,EAAE;IACV;EACF,CAAC,CAAC;EAEF,MAAM+C,MAAM,GAAG1C,SAAS,CAAC,CAAC;EAE1B,oBACEjM,OAAA,CAAAE,SAAA;IAAAiL,QAAA,gBACEnL,OAAA;MAAI0K,KAAK,EAAEiE,MAAM,CAACzC,aAAc;MAAAf,QAAA,EAAEvK,CAAC,CAAC,wBAAwB;IAAC;MAAAyK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACnExL,OAAA;MAAK0K,KAAK,EAAE;QAACyB,SAAS,EAAE,QAAQ;QAAEC,YAAY,EAAE;MAAM;IAAE;MAAAf,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnD,CAAC,eAENxL,OAAA;MAAK0K,KAAK,EAAEiE,MAAM,CAACtC,qBAAsB;MAAAlB,QAAA,gBACvCnL,OAAA;QAAK4O,GAAG,EAAEvO,QAAS;QAACwO,GAAG,EAAEjO,CAAC,CAAC,qBAAqB,CAAE;QAAC8J,KAAK,EAAEiE,MAAM,CAAClC;MAAa;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACjFxL,OAAA;QACE0K,KAAK,EAAEiE,MAAM,CAAC9B,YAAa;QAC3Bb,OAAO,EAAEvG,wBAAyB;QAClCqJ,YAAY,EAAEA,CAAA,KAAM9N,sBAAsB,CAAC,IAAI,CAAE;QACjD+N,YAAY,EAAEA,CAAA,KAAM/N,sBAAsB,CAAC,KAAK,CAAE;QAAAmK,QAAA,gBAElDnL,OAAA;UAAKsM,KAAK,EAAC,IAAI;UAAC0C,MAAM,EAAC,IAAI;UAACC,OAAO,EAAC,WAAW;UAACC,IAAI,EAAC,MAAM;UAACC,MAAM,EAAE/O,QAAQ,GAAG,OAAO,GAAG,MAAO;UAACgP,WAAW,EAAC,GAAG;UAACC,aAAa,EAAC,OAAO;UAACC,cAAc,EAAC,OAAO;UAAAnE,QAAA,gBAC3JnL,OAAA;YAAMsH,CAAC,EAAC;UAA8C;YAAA+D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC9DxL,OAAA;YAAQuP,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,IAAI;YAACC,CAAC,EAAC;UAAG;YAAApE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,EACLzK,mBAAmB,iBAClBf,OAAA;UAAK0K,KAAK,EAAEiE,MAAM,CAACN,eAAgB;UAAAlD,QAAA,EAChCvK,CAAC,CAAC,gCAAgC;QAAC;UAAAyK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELlL,aAAa,iBACZN,OAAA;MAAK0K,KAAK,EAAEiE,MAAM,CAACxB,oBAAqB;MAAAhC,QAAA,gBACtCnL,OAAA;QAAK0K,KAAK,EAAEiE,MAAM,CAACtB,iBAAkB;QAAAlC,QAAA,gBACnCnL,OAAA;UAAI0K,KAAK,EAAEiE,MAAM,CAACnB,gBAAiB;UAAArC,QAAA,EAAEvK,CAAC,CAAC,wBAAwB;QAAC;UAAAyK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACtExL,OAAA;UAAK0K,KAAK,EAAEiE,MAAM,CAACjB,kBAAmB;UAAAvC,QAAA,gBACpCnL,OAAA;YACEgM,OAAO,EAAEjI,eAAgB;YACzB2G,KAAK,EAAEiE,MAAM,CAACf,YAAa;YAC3B8B,WAAW,EAAGC,CAAC,IAAKA,CAAC,CAAChI,aAAa,CAAC+C,KAAK,CAACoC,eAAe,GAAG1M,QAAQ,GAAG,MAAM,GAAG,MAAO;YACvFwP,UAAU,EAAGD,CAAC,IAAKA,CAAC,CAAChI,aAAa,CAAC+C,KAAK,CAACoC,eAAe,GAAG,aAAc;YAAA3B,QAAA,gBAEzEnL,OAAA;cAAKsM,KAAK,EAAC,IAAI;cAAC0C,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC,GAAG;cAACC,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAAAnE,QAAA,gBAC5InL,OAAA;gBAAMuB,CAAC,EAAC,GAAG;gBAACC,CAAC,EAAC,GAAG;gBAAC8K,KAAK,EAAC,IAAI;gBAAC0C,MAAM,EAAC,IAAI;gBAACa,EAAE,EAAC,GAAG;gBAACC,EAAE,EAAC;cAAG;gBAAAzE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9DxL,OAAA;gBAAMsH,CAAC,EAAC;cAAyD;gBAAA+D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC,EACL3K,eAAe,iBACdb,OAAA;cAAM0K,KAAK,EAAEiE,MAAM,CAACR,OAAQ;cAAAhD,QAAA,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAC1C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC,eACTxL,OAAA;YACEgM,OAAO,EAAExH,YAAa;YACtBkG,KAAK,EAAEiE,MAAM,CAACf,YAAa;YAC3B8B,WAAW,EAAGC,CAAC,IAAKA,CAAC,CAAChI,aAAa,CAAC+C,KAAK,CAACoC,eAAe,GAAG1M,QAAQ,GAAG,MAAM,GAAG,MAAO;YACvFwP,UAAU,EAAGD,CAAC,IAAKA,CAAC,CAAChI,aAAa,CAAC+C,KAAK,CAACoC,eAAe,GAAG,aAAc;YAAA3B,QAAA,eAEzEnL,OAAA;cAAKsM,KAAK,EAAC,IAAI;cAAC0C,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC,GAAG;cAACC,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAAAnE,QAAA,gBAC5InL,OAAA;gBAAMsH,CAAC,EAAC;cAA2C;gBAAA+D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3DxL,OAAA;gBAAU+P,MAAM,EAAC;cAAkB;gBAAA1E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC/CxL,OAAA;gBAAMgQ,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC;cAAG;gBAAA9E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACTxL,OAAA;YACEgM,OAAO,EAAEtG,4BAA6B;YACtCgF,KAAK,EAAEiE,MAAM,CAACf,YAAa;YAC3B8B,WAAW,EAAGC,CAAC,IAAK;cAClBA,CAAC,CAAChI,aAAa,CAAC+C,KAAK,CAACoC,eAAe,GAAG1M,QAAQ,GAAG,MAAM,GAAG,MAAM;cAClEc,qBAAqB,CAAC,IAAI,CAAC;YAC7B,CAAE;YACF0O,UAAU,EAAGD,CAAC,IAAK;cACjBA,CAAC,CAAChI,aAAa,CAAC+C,KAAK,CAACoC,eAAe,GAAG,aAAa;cACrD5L,qBAAqB,CAAC,KAAK,CAAC;YAC9B,CAAE;YAAAiK,QAAA,gBAEFnL,OAAA;cAAKsM,KAAK,EAAC,IAAI;cAAC0C,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC,GAAG;cAACC,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAAAnE,QAAA,eAC5InL,OAAA;gBAAMsH,CAAC,EAAC;cAAuB;gBAAA+D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC,EACLvK,kBAAkB,iBACjBjB,OAAA;cAAM0K,KAAK,EAAEiE,MAAM,CAACL,cAAe;cAAAnD,QAAA,EAChCvK,CAAC,CAAC,iCAAiC;YAAC;cAAAyK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNxL,OAAA;QAAK0K,KAAK,EAAEiE,MAAM,CAACZ,WAAY;QAACqC,GAAG,EAAE3O,cAAe;QAAA0J,QAAA,EACjDH,+BAA+B,CAAC;MAAC;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGArK,cAAc,iBACbnB,OAAA,CAACH,WAAW;MACVO,QAAQ,EAAEA,QAAS;MACnBsH,UAAU,EAAEvG,cAAe;MAC3BwJ,QAAQ,EAAEtJ,aAAc;MACxBgP,OAAO,EAAEzH,UAAW;MACpB0H,QAAQ,EAAEnH,kBAAmB;MAC7BnH,gBAAgB,EAAEF,iBAAiB,CAACE,gBAAiB;MACrDC,aAAa,EAAEH,iBAAiB,CAACG,aAAc;MAC/CC,mBAAmB,EAAEJ,iBAAiB,CAACI,mBAAoB;MAC3DC,gBAAgB,EAAEL,iBAAiB,CAACK,gBAAiB;MACrDoO,kBAAkB,EAAEjQ;IAAc;MAAA+K,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnC,CACF,eAEDxL,OAAA;MAAK0K,KAAK,EAAEiE,MAAM,CAACJ,iBAAkB;MAAApD,QAAA,eACnCnL,OAAA;QACE0K,KAAK,EAAE;UACLiC,OAAO,EAAE,MAAM;UACfM,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBS,GAAG,EAAE,KAAK;UACVZ,OAAO,EAAE,QAAQ;UACjBD,eAAe,EAAE,yBAAyB;UAC1CjB,KAAK,EAAE,SAAS;UAChBuB,MAAM,EAAE,mCAAmC;UAC3CV,YAAY,EAAE,KAAK;UACnBe,QAAQ,EAAE,UAAU;UACpB3B,UAAU,EAAE,KAAK;UACjBF,MAAM,EAAE,SAAS;UACjBU,KAAK,EAAE,MAAM;UACbkE,SAAS,EAAE;QACb,CAAE;QACFxE,OAAO,EAAEvL,aAAc;QAAA0K,QAAA,gBAEvBnL,OAAA;UAAKsM,KAAK,EAAC,IAAI;UAAC0C,MAAM,EAAC,IAAI;UAACC,OAAO,EAAC,WAAW;UAACC,IAAI,EAAC,MAAM;UAACC,MAAM,EAAC,cAAc;UAACC,WAAW,EAAC,GAAG;UAAAjE,QAAA,gBAC/FnL,OAAA;YAAMgQ,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,GAAG;YAACC,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC;UAAI;YAAA9E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5CxL,OAAA;YAAMgQ,EAAE,EAAC,GAAG;YAACC,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC;UAAI;YAAA9E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,kBAER;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAAC7K,EAAA,CA9yBIR,eAA+C;EAAA,QASrCL,WAAW;AAAA;AAAA2Q,EAAA,GATrBtQ,eAA+C;AAgzBrD,eAAeA,eAAe;AAAC,IAAAsQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}