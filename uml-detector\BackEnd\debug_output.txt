=== DÉMARRAGE DÉTECTION ===
Exécution du modèle 1 (PT)...
Résultats modèle 1: 1 éléments traités
Modèle 1: 19 boîtes détectées
  Détection: class, confiance: 0.95
  [OK] Classe acceptée avec confiance 0.95 >= 0.25
  Détection: class, confiance: 0.95
  [OK] Classe acceptée avec confiance 0.95 >= 0.25
  Détection: class, confiance: 0.94
  [OK] Classe acceptée avec confiance 0.94 >= 0.25
  Détection: class, confiance: 0.94
  [OK] Classe acceptée avec confiance 0.94 >= 0.25
  Détection: class, confiance: 0.94
  [OK] Classe acceptée avec confiance 0.94 >= 0.25
  Détection: class, confiance: 0.94
  [OK] Classe acceptée avec confiance 0.94 >= 0.25
  Détection: class, confiance: 0.94
  [OK] Classe acceptée avec confiance 0.94 >= 0.25
  Détection: class, confiance: 0.94
  [OK] Classe acceptée avec confiance 0.94 >= 0.25
  Détection: arrow, confiance: 0.91
  [OK] Flèche acceptée avec confiance 0.91 >= 0.4
  Détection: arrow, confiance: 0.81
  [OK] Flèche acceptée avec confiance 0.81 >= 0.4
  Détection: arrow, confiance: 0.70
  [OK] Flèche acceptée avec confiance 0.70 >= 0.4
  Détection: arrow, confiance: 0.66
  [OK] Flèche acceptée avec confiance 0.66 >= 0.4
  Détection: arrow, confiance: 0.65
  [OK] Flèche acceptée avec confiance 0.65 >= 0.4
  Détection: arrow, confiance: 0.63
  [OK] Flèche acceptée avec confiance 0.63 >= 0.4
  Détection: arrow, confiance: 0.61
  [OK] Flèche acceptée avec confiance 0.61 >= 0.4
  Détection: arrow, confiance: 0.56
  [OK] Flèche acceptée avec confiance 0.56 >= 0.4
  Détection: arrow, confiance: 0.53
  [OK] Flèche acceptée avec confiance 0.53 >= 0.4
  Détection: arrow, confiance: 0.53
  [OK] Flèche acceptée avec confiance 0.53 >= 0.4
  Détection: arrow, confiance: 0.38
  [!] Confiance trop basse pour arrow: 0.38 < 0.4

Exécution du modèle 2 (ONNX)...
Passage de l'image brute (numpy array) au modèle ONNX...
Résultats modèle 2: 1 éléments traités
  Nombre de détections: 19
  Classe détectée: endpoin
  Classe détectée: endpoin
  Classe détectée: endpoin
  Classe détectée: endpoin
  Classe détectée: endpoin
  Classe détectée: endpoin
  Classe détectée: endpoin
  Classe détectée: endpoin
  Classe détectée: endpoin
  Classe détectée: endpoin
  Classe détectée: endpoin
  Classe détectée: endpoin
  Classe détectée: endpoin
  Classe détectée: endpoin
  Classe détectée: endpoin
  Classe détectée: endpoin
  Classe détectée: endpoin
  Classe détectée: endpoin
  Classe détectée: endpoin
Modèle 2: 19 boîtes détectées
  Box 0: coords=943,1901,1009,1961 class_idx=3 conf=0.59
  Détection: endpoin, confiance: 0.59
  Box 1: coords=1263,1904,1323,1959 class_idx=3 conf=0.57
  Détection: endpoin, confiance: 0.57
  Box 2: coords=496,1187,540,1233 class_idx=3 conf=0.56
  Détection: endpoin, confiance: 0.56
  Box 3: coords=1690,1079,1738,1126 class_idx=3 conf=0.55
  Détection: endpoin, confiance: 0.55
  Box 4: coords=2274,1378,2333,1439 class_idx=3 conf=0.53
  Détection: endpoin, confiance: 0.53
  Box 5: coords=1941,1949,2015,2019 class_idx=3 conf=0.52
  Détection: endpoin, confiance: 0.52
  Box 6: coords=1487,1608,1545,1669 class_idx=3 conf=0.52
  Détection: endpoin, confiance: 0.52
  Box 7: coords=532,2007,597,2074 class_idx=3 conf=0.51
  Détection: endpoin, confiance: 0.51
  Box 8: coords=1222,376,1264,417 class_idx=3 conf=0.50
  Détection: endpoin, confiance: 0.50
  Box 9: coords=496,1567,554,1627 class_idx=3 conf=0.49
  Détection: endpoin, confiance: 0.49
  Box 10: coords=455,563,491,600 class_idx=3 conf=0.48
  Détection: endpoin, confiance: 0.48
  Box 11: coords=755,973,811,1021 class_idx=3 conf=0.48
  Détection: endpoin, confiance: 0.48
  Box 12: coords=564,2526,636,2597 class_idx=3 conf=0.48
  Détection: endpoin, confiance: 0.48
  Box 13: coords=1017,964,1071,1012 class_idx=3 conf=0.47
  Détection: endpoin, confiance: 0.47
  Box 14: coords=1471,1381,1521,1431 class_idx=3 conf=0.46
  Détection: endpoin, confiance: 0.46
  Box 15: coords=1442,734,1491,780 class_idx=3 conf=0.41
  Détection: endpoin, confiance: 0.41
  Box 16: coords=440,749,500,803 class_idx=3 conf=0.39
  Détection: endpoin, confiance: 0.39
  Box 17: coords=2026,1088,2081,1138 class_idx=3 conf=0.38
  Détection: endpoin, confiance: 0.38
  Box 18: coords=836,371,882,410 class_idx=3 conf=0.38
  Détection: endpoin, confiance: 0.38

Traitement des relations entre classes...

Résumé des détections:
  Modèle 1: {'class': 8, 'arrow': 10}
  Modèle 2: {'endpoin': 19}

=== FIN DÉTECTION ===
