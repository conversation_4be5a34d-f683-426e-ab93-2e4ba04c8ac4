=== DÉMARRAGE DÉTECTION ===
Exécution du modèle 1 (PT)...
Résultats modèle 1: 1 éléments traités
Modèle 1: 13 boîtes détectées
  Détection: class, confiance: 0.95
  [OK] Classe acceptée avec confiance 0.95 >= 0.25
  Détection: class, confiance: 0.94
  [OK] Classe acceptée avec confiance 0.94 >= 0.25
  Détection: class, confiance: 0.93
  [OK] Classe acceptée avec confiance 0.93 >= 0.25
  Détection: class, confiance: 0.93
  [OK] Classe acceptée avec confiance 0.93 >= 0.25
  Détection: class, confiance: 0.93
  [OK] Classe acceptée avec confiance 0.93 >= 0.25
  Détection: class, confiance: 0.93
  [OK] Classe acceptée avec confiance 0.93 >= 0.25
  Détection: class, confiance: 0.92
  [OK] Classe acceptée avec confiance 0.92 >= 0.25
  Détection: arrow, confiance: 0.91
  [OK] Flèche acceptée avec confiance 0.91 >= 0.4
  Détection: arrow, confiance: 0.91
  [OK] Flèche acceptée avec confiance 0.91 >= 0.4
  Détection: arrow, confiance: 0.90
  [OK] Flèche acceptée avec confiance 0.90 >= 0.4
  Détection: arrow, confiance: 0.83
  [OK] Flèche acceptée avec confiance 0.83 >= 0.4
  Détection: arrow, confiance: 0.62
  [OK] Flèche acceptée avec confiance 0.62 >= 0.4
  Détection: arrow, confiance: 0.50
  [OK] Flèche acceptée avec confiance 0.50 >= 0.4

Exécution du modèle 2 (ONNX)...
Passage de l'image brute (numpy array) au modèle ONNX...
Résultats modèle 2: 1 éléments traités
  Nombre de détections: 9
  Classe détectée: generalization
  Classe détectée: generalization
  Classe détectée: endpoin
  Classe détectée: endpoin
  Classe détectée: endpoin
  Classe détectée: endpoin
  Classe détectée: endpoin
  Classe détectée: endpoin
  Classe détectée: endpoin
Modèle 2: 9 boîtes détectées
  Box 0: coords=764,638,897,758 class_idx=2 conf=0.67
  Détection: generalization, confiance: 0.67
  Box 1: coords=2700,620,2835,728 class_idx=2 conf=0.65
  Détection: generalization, confiance: 0.65
  Box 2: coords=1314,900,1386,967 class_idx=3 conf=0.63
  Détection: endpoin, confiance: 0.63
  Box 3: coords=498,949,561,1009 class_idx=3 conf=0.55
  Détection: endpoin, confiance: 0.55
  Box 4: coords=1263,458,1321,514 class_idx=3 conf=0.54
  Détection: endpoin, confiance: 0.54
  Box 5: coords=1896,417,1951,467 class_idx=3 conf=0.54
  Détection: endpoin, confiance: 0.54
  Box 6: coords=3305,887,3378,957 class_idx=3 conf=0.50
  Détection: endpoin, confiance: 0.50
  Box 7: coords=876,488,934,544 class_idx=3 conf=0.49
  Détection: endpoin, confiance: 0.49
  Box 8: coords=2443,424,2500,479 class_idx=3 conf=0.45
  Détection: endpoin, confiance: 0.45

Traitement des relations entre classes...

Résumé des détections:
  Modèle 1: {'class': 7, 'arrow': 6}
  Modèle 2: {'generalization': 2, 'endpoin': 7}

=== FIN DÉTECTION ===
