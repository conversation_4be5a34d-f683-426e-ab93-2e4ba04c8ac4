=== DÉMARRAGE DÉTECTION ===
Exécution du modèle 1 (PT)...
Résultats modèle 1: 1 éléments traités
Modèle 1: 8 boîtes détectées
  Détection: class, confiance: 0.90
  [OK] Classe acceptée avec confiance 0.90 >= 0.25
  Détection: class, confiance: 0.90
  [OK] Classe acceptée avec confiance 0.90 >= 0.25
  Détection: class, confiance: 0.89
  [OK] Classe acceptée avec confiance 0.89 >= 0.25
  Détection: class, confiance: 0.89
  [OK] Classe acceptée avec confiance 0.89 >= 0.25
  Détection: arrow, confiance: 0.72
  [OK] Flèche acceptée avec confiance 0.72 >= 0.4
  Détection: arrow, confiance: 0.69
  [OK] Flèche acceptée avec confiance 0.69 >= 0.4
  Détection: arrow, confiance: 0.63
  [OK] Flèche acceptée avec confiance 0.63 >= 0.4
  Détection: arrow, confiance: 0.41
  [OK] Flèche acceptée avec confiance 0.41 >= 0.4

Exécution du modèle 2 (ONNX)...
Passage de l'image brute (numpy array) au modèle ONNX...
Résultats modèle 2: 1 éléments traités
  Nombre de détections: 6
  Classe détectée: composition
  Classe détectée: endpoin
  Classe détectée: endpoin
  Classe détectée: endpoin
  Classe détectée: endpoin
  Classe détectée: endpoin
Modèle 2: 6 boîtes détectées
  Box 0: coords=133,152,142,158 class_idx=5 conf=0.80
  Détection: composition, confiance: 0.80
  Box 1: coords=52,23,60,29 class_idx=3 conf=0.56
  Détection: endpoin, confiance: 0.56
  Box 2: coords=53,152,60,159 class_idx=3 conf=0.54
  Détection: endpoin, confiance: 0.54
  Box 3: coords=132,22,139,29 class_idx=3 conf=0.51
  Détection: endpoin, confiance: 0.51
  Box 4: coords=160,124,166,130 class_idx=3 conf=0.49
  Détection: endpoin, confiance: 0.49
  Box 5: coords=160,73,166,80 class_idx=3 conf=0.47
  Détection: endpoin, confiance: 0.47

Traitement des relations entre classes...

Résumé des détections:
  Modèle 1: {'class': 4, 'arrow': 4}
  Modèle 2: {'composition': 1, 'endpoin': 5}

=== FIN DÉTECTION ===
