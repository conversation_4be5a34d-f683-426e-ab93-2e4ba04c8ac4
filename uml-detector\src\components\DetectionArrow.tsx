import React, { useState } from "react";

interface DetectionArrowProps {
  darkMode: boolean;
  annotatedImage: string | null;
}

const DetectionArrow: React.FC<DetectionArrowProps> = ({ darkMode, annotatedImage }) => {
  const [imageLoading, setImageLoading] = useState(true);
  const [imageError, setImageError] = useState(false);

  const styles = {
    container: {
      maxWidth: "800px",
      margin: "0 auto",
      padding: "20px",
      color: darkMode ? "#e0e0e0" : "#333"
    },
    header: {
      textAlign: "center" as const,
      marginBottom: "20px"
    },
    imageContainer: {
      display: "flex",
      justifyContent: "center",
      marginBottom: "20px"
    },
    annotatedImage: {
      maxWidth: "100%",
      borderRadius: "8px",
      boxShadow: darkMode ? "0 4px 8px rgba(0,0,0,0.5)" : "0 4px 8px rgba(0,0,0,0.1)"
    },
    loadingContainer: {
      textAlign: "center" as const,
      padding: "40px",
      backgroundColor: darkMode ? "#2d2d2d" : "#f5f5f5",
      borderRadius: "8px",
      margin: "20px auto",
      maxWidth: "600px"
    },
    errorContainer: {
      textAlign: "center" as const,
      padding: "40px",
      backgroundColor: darkMode ? "#402020" : "#fff5f5",
      borderRadius: "8px",
      margin: "20px auto",
      maxWidth: "600px",
      border: `1px solid ${darkMode ? "#ff6b6b" : "#f56565"}`
    },
    tip: {
      backgroundColor: darkMode ? "#333" : "#f8f9fa",
      padding: "15px",
      borderRadius: "8px",
      marginTop: "30px",
      border: `1px solid ${darkMode ? "#444" : "#e0e0e0"}`
    },
    tipTitle: {
      fontWeight: "600",
      marginBottom: "8px",
      display: "flex",
      alignItems: "center",
      gap: "8px"
    },
    explanationContainer: {
      backgroundColor: darkMode ? "#333" : "#fff",
      borderRadius: "8px",
      padding: "20px",
      marginTop: "30px",
      boxShadow: darkMode ? "0 2px 10px rgba(0,0,0,0.5)" : "0 2px 10px rgba(0,0,0,0.1)"
    },
    explanationTitle: {
      fontSize: "18px",
      fontWeight: "600",
      marginBottom: "15px"
    },
    explanationText: {
      lineHeight: "1.6"
    },
    legendContainer: {
      display: "flex",
      flexWrap: "wrap" as const,
      gap: "10px",
      marginTop: "20px"
    },
    legendItem: {
      display: "flex",
      alignItems: "center",
      gap: "8px"
    },
    legendColor: {
      width: "20px",
      height: "20px",
      borderRadius: "4px"
    }
  };

  const handleImageLoad = () => {
    setImageLoading(false);
    setImageError(false);
  };

  const handleImageError = () => {
    setImageLoading(false);
    setImageError(true);
  };

  return (
    <div style={styles.container}>
      <h2 style={styles.header}>Détection de Relations UML</h2>

      {annotatedImage ? (
        <>
          {imageLoading && (
            <div style={styles.loadingContainer}>
              <p>Chargement de l'image annotée...</p>
            </div>
          )}

          {imageError ? (
            <div style={styles.errorContainer}>
              <p>Impossible de charger l'image annotée. Veuillez vérifier que le serveur est en cours d'exécution.</p>
              <p>URL: {annotatedImage}</p>
            </div>
          ) : (
            <div style={styles.imageContainer}>
              <img
                src={annotatedImage}
                alt="Diagramme UML annoté avec relations détectées"
                style={{
                  ...styles.annotatedImage,
                  display: imageLoading ? "none" : "block"
                }}
                onLoad={handleImageLoad}
                onError={handleImageError}
              />
            </div>
          )}

          <div style={styles.explanationContainer}>
            <h3 style={styles.explanationTitle}>Analyse des Relations</h3>
            <p style={styles.explanationText}>
              L'image ci-dessus montre les relations détectées entre les différentes classes de votre diagramme UML.
              Notre système d'IA a identifié les connexions et les a marquées selon leur type.
            </p>

            <div style={styles.legendContainer}>
              <div style={styles.legendItem}>
                <div style={{...styles.legendColor, backgroundColor: "#4CAF50"}}></div>
                <span>Classe</span>
              </div>
              <div style={styles.legendItem}>
                <div style={{...styles.legendColor, backgroundColor: "#2196F3"}}></div>
                <span>Relation</span>
              </div>
              <div style={styles.legendItem}>
                <div style={{...styles.legendColor, backgroundColor: "#A52A2A"}}></div>
                <span>Agrégation</span>
              </div>
              <div style={styles.legendItem}>
                <div style={{...styles.legendColor, backgroundColor: "#FFFF00"}}></div>
                <span>Composition</span>
              </div>
              <div style={styles.legendItem}>
                <div style={{...styles.legendColor, backgroundColor: "#9C27B0"}}></div>
                <span>Généralisation</span>
              </div>
            </div>
          </div>

          <div style={styles.tip}>
            <div style={styles.tipTitle}>
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke={darkMode ? "#e0e0e0" : "#333"} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="12" y1="16" x2="12" y2="12"></line>
                <line x1="12" y1="8" x2="12.01" y2="8"></line>
              </svg>
              Astuce
            </div>
            <p>Pour une analyse plus détaillée, consultez l'onglet "Extraction de texte UML" qui contient les informations textuelles complètes de toutes les classes détectées.</p>
          </div>
        </>
      ) : (
        <div style={styles.loadingContainer}>
          <p>Aucune image annotée n'est disponible. Veuillez d'abord télécharger et analyser une image de diagramme UML.</p>
        </div>
      )}
    </div>
  );
};

export default DetectionArrow;