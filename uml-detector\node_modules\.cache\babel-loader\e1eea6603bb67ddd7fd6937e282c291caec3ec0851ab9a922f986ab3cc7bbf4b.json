{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\FixTorchUMLDGM\\\\uml-detector\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport ImageUploader from \"./components/imageUp/ImageUploader\";\nimport DetectionArrow from \"./components/DetectionArrow\";\nimport UMLDiagrameExtractor from \"./components/UmlDiagr/UMLDiagrameExtractor\";\nimport Documentation from \"./components/Documentation\";\nimport { styles } from \"./AppStyles\";\nimport Login from \"./components/Loginsignup/Login\";\nimport UserProfile from \"./components/Loginsignup/UserProfile\";\nimport { AuthProvider, useAuth } from \"./context/AuthContext\";\nimport { HistoryProvider } from \"./context/HistoryContext\";\nimport { LanguageProvider, useLanguage } from \"./context/LanguageContext\";\nimport HistorySidebar from \"./components/History/HistorySidebar\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AppContent = () => {\n  _s();\n  const [activeTab, setActiveTab] = useState(\"upload\");\n  const [darkMode, setDarkMode] = useState(true);\n  const [annotatedImageUrl, setAnnotatedImageUrl] = useState(null);\n  const [imageUrl, setImageUrl] = useState(null);\n  const [extractedText, setExtractedText] = useState(\"\");\n  const [textUrl, setTextUrl] = useState(null);\n  const [isHistoryOpen, setIsHistoryOpen] = useState(true);\n  const [showLogin, setShowLogin] = useState(false);\n  const {\n    currentUser\n  } = useAuth();\n  const {\n    language,\n    setLanguage,\n    t\n  } = useLanguage();\n\n  // Dans votre composant principal\n  useEffect(() => {\n    const handleLoadHistoryItem = event => {\n      const item = event.detail;\n      // Mettre à jour l'état avec les données de l'élément d'historique\n      if (item) {\n        setImageUrl(item.originalImageUrl);\n        setAnnotatedImageUrl(item.annotatedImageUrl || null);\n        setExtractedText(item.extractedText);\n        setTextUrl(null); // Pas besoin de l'URL du texte car nous avons déjà le texte extrait\n\n        // Passer à l'onglet d'upload pour afficher l'image chargée\n        setActiveTab(\"upload\");\n      }\n    };\n    window.addEventListener('loadHistoryItem', handleLoadHistoryItem);\n    return () => {\n      window.removeEventListener('loadHistoryItem', handleLoadHistoryItem);\n    };\n  }, []);\n  const handleAnalysisComplete = (imageUrl, text, textFileUrl) => {\n    setImageUrl(imageUrl);\n\n    // For annotated image, use server URL directly\n    const annotatedUrl = \"http://127.0.0.1:8000/annotated_image.jpg\";\n    setAnnotatedImageUrl(annotatedUrl);\n    setExtractedText(text);\n    setTextUrl(textFileUrl);\n  };\n  const handleViewAnnotatedImage = () => {\n    setActiveTab(\"relations\");\n  };\n  const handleNavigateToUMLExtractor = () => {\n    setActiveTab(\"text\");\n  };\n  const handleUpdateExtractedText = newText => {\n    console.log(\"Mise à jour du texte extrait dans App.tsx\");\n    console.log(\"Nouveau texte:\", newText.substring(0, 100) + \"...\");\n    setExtractedText(newText);\n\n    // Si vous utilisez ce texte ailleurs, assurez-vous qu'il est également mis à jour\n    // Par exemple, si vous le passez à d'autres composants\n  };\n  const renderContent = () => {\n    switch (activeTab) {\n      case \"relations\":\n        return /*#__PURE__*/_jsxDEV(DetectionArrow, {\n          darkMode: darkMode,\n          annotatedImage: annotatedImageUrl\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this);\n      case \"text\":\n        return /*#__PURE__*/_jsxDEV(UMLDiagrameExtractor, {\n          darkMode: darkMode,\n          textToConvert: extractedText\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 16\n        }, this);\n      case \"doc\":\n        return /*#__PURE__*/_jsxDEV(Documentation, {\n          darkMode: darkMode\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(ImageUploader, {\n          darkMode: darkMode,\n          onAnalysisComplete: handleAnalysisComplete,\n          savedImageUrl: imageUrl,\n          savedExtractedText: extractedText,\n          savedTextUrl: textUrl,\n          onViewAnnotatedImage: handleViewAnnotatedImage,\n          onNavigateToUMLExtractor: handleNavigateToUMLExtractor,\n          onUpdateExtractedText: handleUpdateExtractedText\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this);\n    }\n  };\n\n  // Remplacer le mock historyItems par le vrai système\n\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      ...styles.container,\n      backgroundColor: darkMode ? \"#121820\" : \"#f0f4f8\",\n      color: darkMode ? \"#e2e8f0\" : \"#334155\"\n    },\n    children: [showLogin && /*#__PURE__*/_jsxDEV(Login, {\n      darkMode: darkMode,\n      onClose: () => setShowLogin(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 21\n    }, this), /*#__PURE__*/_jsxDEV(HistorySidebar, {\n      darkMode: darkMode,\n      isOpen: isHistoryOpen,\n      onToggle: () => setIsHistoryOpen(!isHistoryOpen)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        ...styles.mainWrapper,\n        marginLeft: isHistoryOpen ? \"240px\" : \"0\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.header,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.headerLeft,\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setIsHistoryOpen(!isHistoryOpen),\n            style: styles.toggleHistoryButton,\n            children: isHistoryOpen ? /*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"20\",\n              height: \"20\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M15 18l-6-6 6-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"20\",\n              height: \"20\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M9 18l6-6-6-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            style: styles.title,\n            children: t('app.title')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.headerRight,\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setDarkMode(!darkMode),\n            style: styles.iconButton,\n            children: darkMode ? /*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"18\",\n              height: \"18\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                cx: \"12\",\n                cy: \"12\",\n                r: \"5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n                x1: \"12\",\n                y1: \"1\",\n                x2: \"12\",\n                y2: \"3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n                x1: \"12\",\n                y1: \"21\",\n                x2: \"12\",\n                y2: \"23\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n                x1: \"4.22\",\n                y1: \"4.22\",\n                x2: \"5.64\",\n                y2: \"5.64\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n                x1: \"18.36\",\n                y1: \"18.36\",\n                x2: \"19.78\",\n                y2: \"19.78\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n                x1: \"1\",\n                y1: \"12\",\n                x2: \"3\",\n                y2: \"12\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n                x1: \"21\",\n                y1: \"12\",\n                x2: \"23\",\n                y2: \"12\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n                x1: \"4.22\",\n                y1: \"19.78\",\n                x2: \"5.64\",\n                y2: \"18.36\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n                x1: \"18.36\",\n                y1: \"5.64\",\n                x2: \"19.78\",\n                y2: \"4.22\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"18\",\n              height: \"18\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setLanguage(language === 'fr' ? 'en' : 'fr'),\n            style: styles.languageButton,\n            title: t('language.switch'),\n            children: t('language.current')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this), currentUser ? /*#__PURE__*/_jsxDEV(UserProfile, {\n            darkMode: darkMode\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowLogin(true),\n            style: styles.loginButton,\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"18\",\n              height: \"18\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                cx: \"12\",\n                cy: \"7\",\n                r: \"4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 17\n            }, this), t('button.login')]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.tabs,\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab(\"upload\"),\n          style: {\n            ...styles.tabButton,\n            borderBottom: activeTab === \"upload\" ? `2px solid ${darkMode ? \"#60a5fa\" : \"#3b82f6\"}` : \"none\",\n            color: activeTab === \"upload\" ? darkMode ? \"#60a5fa\" : \"#3b82f6\" : darkMode ? \"#94a3b8\" : \"#64748b\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"20\",\n            height: \"20\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            strokeWidth: \"2\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M4 16L8.586 11.414C8.961 11.039 9.47 10.828 10 10.828C10.53 10.828 11.039 11.039 11.414 11.414L16 16M14 14L15.586 12.414C15.961 12.039 16.47 11.828 17 11.828C17.53 11.828 18.039 12.039 18.414 12.414L20 14M14 8H14.01M6 20H18C19.105 20 20 19.105 20 18V6C20 4.895 19.105 4 18 4H6C4.895 4 4 4.895 4 6V18C4 19.105 4.895 20 6 20Z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this), t('tab.upload')]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab(\"relations\"),\n          style: {\n            ...styles.tabButton,\n            borderBottom: activeTab === \"relations\" ? `2px solid ${darkMode ? \"#60a5fa\" : \"#3b82f6\"}` : \"none\",\n            color: activeTab === \"relations\" ? darkMode ? \"#60a5fa\" : \"#3b82f6\" : darkMode ? \"#94a3b8\" : \"#64748b\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"20\",\n            height: \"20\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            strokeWidth: \"2\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M9 3V5M15 3V5M9 19V21M15 19V21M5 9H3M5 15H3M21 9H19M21 15H19M7 19H17C18.105 19 19 18.105 19 17V7C19 5.895 18.105 5 17 5H7C5.895 5 5 5.895 5 7V17C5 18.105 5.895 19 7 19Z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this), t('tab.relations')]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab(\"text\"),\n          style: {\n            ...styles.tabButton,\n            borderBottom: activeTab === \"text\" ? `2px solid ${darkMode ? \"#60a5fa\" : \"#3b82f6\"}` : \"none\",\n            color: activeTab === \"text\" ? darkMode ? \"#60a5fa\" : \"#3b82f6\" : darkMode ? \"#94a3b8\" : \"#64748b\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"20\",\n            height: \"20\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            strokeWidth: \"2\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M12 3H5C4.46957 3 3.96086 3.21071 3.58579 3.58579C3.21071 3.96086 3 4.46957 3 5V19C3 19.5304 3.21071 20.0391 3.58579 20.4142C3.96086 20.7893 4.46957 21 5 21H19C19.5304 21 20.0391 20.7893 20.4142 20.4142C20.7893 20.0391 21 19.5304 21 19V12M19 3V8M16 5H22M9 15H15M9 9H15\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 13\n          }, this), t('tab.text')]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab(\"doc\"),\n          style: {\n            ...styles.tabButton,\n            borderBottom: activeTab === \"doc\" ? `2px solid ${darkMode ? \"#60a5fa\" : \"#3b82f6\"}` : \"none\",\n            color: activeTab === \"doc\" ? darkMode ? \"#60a5fa\" : \"#3b82f6\" : darkMode ? \"#94a3b8\" : \"#64748b\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"20\",\n            height: \"20\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            strokeWidth: \"2\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2ZM12 11C11.4477 11 11 11.4477 11 12V17C11 17.5523 11.4477 18 12 18C12.5523 18 13 17.5523 13 17V12C13 11.4477 12.5523 11 12 11ZM12 9C12.5523 9 13 8.55228 13 8C13 7.44772 12.5523 7 12 7C11.4477 7 11 7.44772 11 8C11 8.55228 11.4477 9 12 9Z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 13\n          }, this), t('tab.documentation')]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.contentSection,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.contentHeader,\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: styles.contentTitle,\n            children: [activeTab === \"upload\" && t('content.upload.title'), activeTab === \"relations\" && t('content.relations.title'), activeTab === \"text\" && t('content.text.title'), activeTab === \"doc\" && t('content.documentation.title')]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: styles.contentSubtitle,\n            children: [activeTab === \"upload\" && t('content.upload.subtitle'), activeTab === \"relations\" && t('content.relations.subtitle'), activeTab === \"text\" && t('content.text.subtitle'), activeTab === \"doc\" && t('content.documentation.subtitle')]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.contentWrapper,\n          children: renderContent()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.footer,\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: t('footer.text')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 114,\n    columnNumber: 5\n  }, this);\n};\n\n// Wrapper avec les providers\n_s(AppContent, \"7xHZf5c5tOcfPUXYgIp5X7D2AdA=\", false, function () {\n  return [useAuth, useLanguage];\n});\n_c = AppContent;\nconst App = () => {\n  return /*#__PURE__*/_jsxDEV(LanguageProvider, {\n    children: /*#__PURE__*/_jsxDEV(AuthProvider, {\n      children: /*#__PURE__*/_jsxDEV(HistoryProvider, {\n        children: /*#__PURE__*/_jsxDEV(AppContent, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 292,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 291,\n    columnNumber: 5\n  }, this);\n};\n_c2 = App;\nexport default App;\nvar _c, _c2;\n$RefreshReg$(_c, \"AppContent\");\n$RefreshReg$(_c2, \"App\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "ImageUploader", "DetectionArrow", "UMLDiagrameExtractor", "Documentation", "styles", "<PERSON><PERSON>", "UserProfile", "<PERSON>th<PERSON><PERSON><PERSON>", "useAuth", "HistoryProvider", "LanguageProvider", "useLanguage", "HistorySidebar", "jsxDEV", "_jsxDEV", "A<PERSON><PERSON><PERSON>nt", "_s", "activeTab", "setActiveTab", "darkMode", "setDarkMode", "annotatedImageUrl", "setAnnotatedImageUrl", "imageUrl", "setImageUrl", "extractedText", "setExtractedText", "textUrl", "setTextUrl", "isHistoryOpen", "setIsHistoryOpen", "showLogin", "setShow<PERSON><PERSON>in", "currentUser", "language", "setLanguage", "t", "handleLoadHistoryItem", "event", "item", "detail", "originalImageUrl", "window", "addEventListener", "removeEventListener", "handleAnalysisComplete", "text", "textFileUrl", "annotatedUrl", "handleViewAnnotatedImage", "handleNavigateToUMLExtractor", "handleUpdateExtractedText", "newText", "console", "log", "substring", "renderContent", "annotatedImage", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "textToConvert", "onAnalysisComplete", "savedImageUrl", "savedExtractedText", "savedTextUrl", "onViewAnnotatedImage", "onNavigateToUMLExtractor", "onUpdateExtractedText", "style", "container", "backgroundColor", "color", "children", "onClose", "isOpen", "onToggle", "mainWrapper", "marginLeft", "header", "headerLeft", "onClick", "to<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "width", "height", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "d", "title", "headerRight", "iconButton", "cx", "cy", "r", "x1", "y1", "x2", "y2", "languageButton", "loginButton", "tabs", "tabButton", "borderBottom", "contentSection", "contentHeader", "contentTitle", "contentSubtitle", "contentWrapper", "footer", "_c", "App", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/FixTorchUMLDGM/uml-detector/src/App.tsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport ImageUploader from \"./components/imageUp/ImageUploader\";\r\nimport DetectionArrow from \"./components/DetectionArrow\";\r\nimport UMLDiagrameExtractor from \"./components/UmlDiagr/UMLDiagrameExtractor\";\r\nimport Documentation from \"./components/Documentation\";\r\nimport { styles } from \"./AppStyles\";\r\nimport Login from \"./components/Loginsignup/Login\";\r\nimport UserProfile from \"./components/Loginsignup/UserProfile\";\r\nimport { AuthProvider, useAuth } from \"./context/AuthContext\";\r\nimport { HistoryProvider } from \"./context/HistoryContext\";\r\nimport { LanguageProvider, useLanguage } from \"./context/LanguageContext\";\r\nimport HistorySidebar from \"./components/History/HistorySidebar\";\r\n\r\nconst AppContent: React.FC = () => {\r\n  const [activeTab, setActiveTab] = useState(\"upload\");\r\n  const [darkMode, setDarkMode] = useState(true);\r\n  const [annotatedImageUrl, setAnnotatedImageUrl] = useState<string | null>(null);\r\n  const [imageUrl, setImageUrl] = useState<string | null>(null);\r\n  const [extractedText, setExtractedText] = useState<string>(\"\");\r\n  const [textUrl, setTextUrl] = useState<string | null>(null);\r\n  const [isHistoryOpen, setIsHistoryOpen] = useState(true);\r\n  const [showLogin, setShowLogin] = useState(false);\r\n\r\n  const { currentUser } = useAuth();\r\n  const { language, setLanguage, t } = useLanguage();\r\n\r\n\r\n  // Dans votre composant principal\r\nuseEffect(() => {\r\n  const handleLoadHistoryItem = (event: any) => {\r\n    const item = event.detail;\r\n    // Mettre à jour l'état avec les données de l'élément d'historique\r\n    if (item) {\r\n      setImageUrl(item.originalImageUrl);\r\n      setAnnotatedImageUrl(item.annotatedImageUrl || null);\r\n      setExtractedText(item.extractedText);\r\n      setTextUrl(null); // Pas besoin de l'URL du texte car nous avons déjà le texte extrait\r\n      \r\n      // Passer à l'onglet d'upload pour afficher l'image chargée\r\n      setActiveTab(\"upload\");\r\n    }\r\n  };\r\n\r\n  window.addEventListener('loadHistoryItem', handleLoadHistoryItem);\r\n  return () => {\r\n    window.removeEventListener('loadHistoryItem', handleLoadHistoryItem);\r\n  };\r\n}, []);\r\n\r\n\r\n  const handleAnalysisComplete = (imageUrl: string, text: string, textFileUrl: string | null) => {\r\n    setImageUrl(imageUrl);\r\n    \r\n    // For annotated image, use server URL directly\r\n    const annotatedUrl = \"http://127.0.0.1:8000/annotated_image.jpg\";\r\n    setAnnotatedImageUrl(annotatedUrl);\r\n    \r\n    setExtractedText(text);\r\n    setTextUrl(textFileUrl);\r\n  };\r\n\r\n  const handleViewAnnotatedImage = () => {\r\n    setActiveTab(\"relations\");\r\n  };\r\n  \r\n  const handleNavigateToUMLExtractor = () => {\r\n    setActiveTab(\"text\");\r\n  };\r\n\r\n  const handleUpdateExtractedText = (newText: string) => {\r\n    console.log(\"Mise à jour du texte extrait dans App.tsx\");\r\n    console.log(\"Nouveau texte:\", newText.substring(0, 100) + \"...\");\r\n    setExtractedText(newText);\r\n    \r\n    // Si vous utilisez ce texte ailleurs, assurez-vous qu'il est également mis à jour\r\n    // Par exemple, si vous le passez à d'autres composants\r\n  };\r\n\r\n  const renderContent = () => {\r\n    switch (activeTab) {\r\n      case \"relations\":\r\n        return (\r\n          <DetectionArrow\r\n            darkMode={darkMode}\r\n            annotatedImage={annotatedImageUrl}\r\n          />\r\n        );\r\n      case \"text\":\r\n        return <UMLDiagrameExtractor \r\n                 darkMode={darkMode} \r\n                 textToConvert={extractedText} \r\n               />;\r\n      case \"doc\":\r\n        return <Documentation darkMode={darkMode} />;\r\n      default:\r\n        return (\r\n          <ImageUploader \r\n            darkMode={darkMode} \r\n            onAnalysisComplete={handleAnalysisComplete} \r\n            savedImageUrl={imageUrl}\r\n            savedExtractedText={extractedText}\r\n            savedTextUrl={textUrl}\r\n            onViewAnnotatedImage={handleViewAnnotatedImage}\r\n            onNavigateToUMLExtractor={handleNavigateToUMLExtractor}\r\n            onUpdateExtractedText={handleUpdateExtractedText}\r\n          />\r\n        );\r\n    }\r\n  };\r\n\r\n  // Remplacer le mock historyItems par le vrai système\r\n\r\n  return (\r\n    <div style={{ \r\n      ...styles.container, \r\n      backgroundColor: darkMode ? \"#121820\" : \"#f0f4f8\",\r\n      color: darkMode ? \"#e2e8f0\" : \"#334155\"\r\n    }}>\r\n      {showLogin && <Login darkMode={darkMode} onClose={() => setShowLogin(false)} />}\r\n      \r\n      {/* Remplacer l'ancienne sidebar par le nouveau composant */}\r\n      <HistorySidebar \r\n        darkMode={darkMode} \r\n        isOpen={isHistoryOpen} \r\n        onToggle={() => setIsHistoryOpen(!isHistoryOpen)} \r\n      />\r\n\r\n      <div style={{\r\n        ...styles.mainWrapper,\r\n        marginLeft: isHistoryOpen ? \"240px\" : \"0\"\r\n      }}>\r\n        <div style={styles.header}>\r\n          <div style={styles.headerLeft}>\r\n            <button \r\n              onClick={() => setIsHistoryOpen(!isHistoryOpen)} \r\n              style={styles.toggleHistoryButton}\r\n            >\r\n              {isHistoryOpen ? (\r\n                <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\r\n                  <path d=\"M15 18l-6-6 6-6\"></path>\r\n                </svg>\r\n              ) : (\r\n                <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\r\n                  <path d=\"M9 18l6-6-6-6\"></path>\r\n                </svg>\r\n              )}\r\n            </button>\r\n            <h1 style={styles.title}>{t('app.title')}</h1>\r\n          </div>\r\n          <div style={styles.headerRight}>\r\n            <button\r\n              onClick={() => setDarkMode(!darkMode)}\r\n              style={styles.iconButton}\r\n            >\r\n              {darkMode ? (\r\n                <svg width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\r\n                  <circle cx=\"12\" cy=\"12\" r=\"5\"></circle>\r\n                  <line x1=\"12\" y1=\"1\" x2=\"12\" y2=\"3\"></line>\r\n                  <line x1=\"12\" y1=\"21\" x2=\"12\" y2=\"23\"></line>\r\n                  <line x1=\"4.22\" y1=\"4.22\" x2=\"5.64\" y2=\"5.64\"></line>\r\n                  <line x1=\"18.36\" y1=\"18.36\" x2=\"19.78\" y2=\"19.78\"></line>\r\n                  <line x1=\"1\" y1=\"12\" x2=\"3\" y2=\"12\"></line>\r\n                  <line x1=\"21\" y1=\"12\" x2=\"23\" y2=\"12\"></line>\r\n                  <line x1=\"4.22\" y1=\"19.78\" x2=\"5.64\" y2=\"18.36\"></line>\r\n                  <line x1=\"18.36\" y1=\"5.64\" x2=\"19.78\" y2=\"4.22\"></line>\r\n                </svg>\r\n              ) : (\r\n                <svg width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\r\n                  <path d=\"M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z\"></path>\r\n                </svg>\r\n              )}\r\n            </button>\r\n\r\n            {/* Bouton de changement de langue */}\r\n            <button\r\n              onClick={() => setLanguage(language === 'fr' ? 'en' : 'fr')}\r\n              style={styles.languageButton}\r\n              title={t('language.switch')}\r\n            >\r\n              {t('language.current')}\r\n            </button>\r\n            \r\n            {/* Conditional rendering based on auth state */}\r\n            {currentUser ? (\r\n              <UserProfile darkMode={darkMode} />\r\n            ) : (\r\n              <button \r\n                onClick={() => setShowLogin(true)} \r\n                style={styles.loginButton}\r\n              >\r\n                <svg width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\r\n                  <path d=\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"></path>\r\n                  <circle cx=\"12\" cy=\"7\" r=\"4\"></circle>\r\n                </svg>\r\n                {t('button.login')}\r\n              </button>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        <div style={styles.tabs}>\r\n          <button \r\n            onClick={() => setActiveTab(\"upload\")} \r\n            style={{\r\n              ...styles.tabButton,\r\n              borderBottom: activeTab === \"upload\" ? `2px solid ${darkMode ? \"#60a5fa\" : \"#3b82f6\"}` : \"none\",\r\n              color: activeTab === \"upload\" ? (darkMode ? \"#60a5fa\" : \"#3b82f6\") : (darkMode ? \"#94a3b8\" : \"#64748b\")\r\n            }}\r\n          >\r\n            <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\r\n              <path d=\"M4 16L8.586 11.414C8.961 11.039 9.47 10.828 10 10.828C10.53 10.828 11.039 11.039 11.414 11.414L16 16M14 14L15.586 12.414C15.961 12.039 16.47 11.828 17 11.828C17.53 11.828 18.039 12.039 18.414 12.414L20 14M14 8H14.01M6 20H18C19.105 20 20 19.105 20 18V6C20 4.895 19.105 4 18 4H6C4.895 4 4 4.895 4 6V18C4 19.105 4.895 20 6 20Z\" />\r\n            </svg>\r\n            {t('tab.upload')}\r\n          </button>\r\n          \r\n          <button \r\n            onClick={() => setActiveTab(\"relations\")} \r\n            style={{\r\n              ...styles.tabButton,\r\n              borderBottom: activeTab === \"relations\" ? `2px solid ${darkMode ? \"#60a5fa\" : \"#3b82f6\"}` : \"none\",\r\n              color: activeTab === \"relations\" ? (darkMode ? \"#60a5fa\" : \"#3b82f6\") : (darkMode ? \"#94a3b8\" : \"#64748b\")\r\n            }}\r\n          >\r\n            <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\r\n              <path d=\"M9 3V5M15 3V5M9 19V21M15 19V21M5 9H3M5 15H3M21 9H19M21 15H19M7 19H17C18.105 19 19 18.105 19 17V7C19 5.895 18.105 5 17 5H7C5.895 5 5 5.895 5 7V17C5 18.105 5.895 19 7 19Z\" />\r\n            </svg>\r\n            {t('tab.relations')}\r\n          </button>\r\n          \r\n          <button \r\n            onClick={() => setActiveTab(\"text\")} \r\n            style={{\r\n              ...styles.tabButton,\r\n              borderBottom: activeTab === \"text\" ? `2px solid ${darkMode ? \"#60a5fa\" : \"#3b82f6\"}` : \"none\",\r\n              color: activeTab === \"text\" ? (darkMode ? \"#60a5fa\" : \"#3b82f6\") : (darkMode ? \"#94a3b8\" : \"#64748b\")\r\n            }}\r\n          >\r\n            <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\r\n              <path d=\"M12 3H5C4.46957 3 3.96086 3.21071 3.58579 3.58579C3.21071 3.96086 3 4.46957 3 5V19C3 19.5304 3.21071 20.0391 3.58579 20.4142C3.96086 20.7893 4.46957 21 5 21H19C19.5304 21 20.0391 20.7893 20.4142 20.4142C20.7893 20.0391 21 19.5304 21 19V12M19 3V8M16 5H22M9 15H15M9 9H15\" />\r\n            </svg>\r\n            {t('tab.text')}\r\n          </button>\r\n          \r\n          <button \r\n            onClick={() => setActiveTab(\"doc\")} \r\n            style={{\r\n              ...styles.tabButton,\r\n              borderBottom: activeTab === \"doc\" ? `2px solid ${darkMode ? \"#60a5fa\" : \"#3b82f6\"}` : \"none\",\r\n              color: activeTab === \"doc\" ? (darkMode ? \"#60a5fa\" : \"#3b82f6\") : (darkMode ? \"#94a3b8\" : \"#64748b\")\r\n            }}\r\n          >\r\n            <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\r\n              <path d=\"M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2ZM12 11C11.4477 11 11 11.4477 11 12V17C11 17.5523 11.4477 18 12 18C12.5523 18 13 17.5523 13 17V12C13 11.4477 12.5523 11 12 11ZM12 9C12.5523 9 13 8.55228 13 8C13 7.44772 12.5523 7 12 7C11.4477 7 11 7.44772 11 8C11 8.55228 11.4477 9 12 9Z\" />\r\n            </svg>\r\n            {t('tab.documentation')}\r\n          </button>\r\n        </div>\r\n\r\n        <div style={styles.contentSection}>\r\n          <div style={styles.contentHeader}>\r\n            <h2 style={styles.contentTitle}>\r\n              {activeTab === \"upload\" && t('content.upload.title')}\r\n              {activeTab === \"relations\" && t('content.relations.title')}\r\n              {activeTab === \"text\" && t('content.text.title')}\r\n              {activeTab === \"doc\" && t('content.documentation.title')}\r\n            </h2>\r\n            <p style={styles.contentSubtitle}>\r\n              {activeTab === \"upload\" && t('content.upload.subtitle')}\r\n              {activeTab === \"relations\" && t('content.relations.subtitle')}\r\n              {activeTab === \"text\" && t('content.text.subtitle')}\r\n              {activeTab === \"doc\" && t('content.documentation.subtitle')}\r\n            </p>\r\n          </div>\r\n          \r\n          <div style={styles.contentWrapper}>\r\n            {renderContent()}\r\n          </div>\r\n        </div>\r\n\r\n        <div style={styles.footer}>\r\n          <p>{t('footer.text')}</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\n// Wrapper avec les providers\r\nconst App: React.FC = () => {\r\n  return (\r\n    <LanguageProvider>\r\n      <AuthProvider>\r\n        <HistoryProvider>\r\n          <AppContent />\r\n        </HistoryProvider>\r\n      </AuthProvider>\r\n    </LanguageProvider>\r\n  );\r\n};\r\n\r\nexport default App;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,aAAa,MAAM,oCAAoC;AAC9D,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,oBAAoB,MAAM,4CAA4C;AAC7E,OAAOC,aAAa,MAAM,4BAA4B;AACtD,SAASC,MAAM,QAAQ,aAAa;AACpC,OAAOC,KAAK,MAAM,gCAAgC;AAClD,OAAOC,WAAW,MAAM,sCAAsC;AAC9D,SAASC,YAAY,EAAEC,OAAO,QAAQ,uBAAuB;AAC7D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,gBAAgB,EAAEC,WAAW,QAAQ,2BAA2B;AACzE,OAAOC,cAAc,MAAM,qCAAqC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjE,MAAMC,UAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGnB,QAAQ,CAAC,QAAQ,CAAC;EACpD,MAAM,CAACoB,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACsB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGvB,QAAQ,CAAgB,IAAI,CAAC;EAC/E,MAAM,CAACwB,QAAQ,EAAEC,WAAW,CAAC,GAAGzB,QAAQ,CAAgB,IAAI,CAAC;EAC7D,MAAM,CAAC0B,aAAa,EAAEC,gBAAgB,CAAC,GAAG3B,QAAQ,CAAS,EAAE,CAAC;EAC9D,MAAM,CAAC4B,OAAO,EAAEC,UAAU,CAAC,GAAG7B,QAAQ,CAAgB,IAAI,CAAC;EAC3D,MAAM,CAAC8B,aAAa,EAAEC,gBAAgB,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACgC,SAAS,EAAEC,YAAY,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAM;IAAEkC;EAAY,CAAC,GAAGzB,OAAO,CAAC,CAAC;EACjC,MAAM;IAAE0B,QAAQ;IAAEC,WAAW;IAAEC;EAAE,CAAC,GAAGzB,WAAW,CAAC,CAAC;;EAGlD;EACFb,SAAS,CAAC,MAAM;IACd,MAAMuC,qBAAqB,GAAIC,KAAU,IAAK;MAC5C,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM;MACzB;MACA,IAAID,IAAI,EAAE;QACRf,WAAW,CAACe,IAAI,CAACE,gBAAgB,CAAC;QAClCnB,oBAAoB,CAACiB,IAAI,CAAClB,iBAAiB,IAAI,IAAI,CAAC;QACpDK,gBAAgB,CAACa,IAAI,CAACd,aAAa,CAAC;QACpCG,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;;QAElB;QACAV,YAAY,CAAC,QAAQ,CAAC;MACxB;IACF,CAAC;IAEDwB,MAAM,CAACC,gBAAgB,CAAC,iBAAiB,EAAEN,qBAAqB,CAAC;IACjE,OAAO,MAAM;MACXK,MAAM,CAACE,mBAAmB,CAAC,iBAAiB,EAAEP,qBAAqB,CAAC;IACtE,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAGJ,MAAMQ,sBAAsB,GAAGA,CAACtB,QAAgB,EAAEuB,IAAY,EAAEC,WAA0B,KAAK;IAC7FvB,WAAW,CAACD,QAAQ,CAAC;;IAErB;IACA,MAAMyB,YAAY,GAAG,2CAA2C;IAChE1B,oBAAoB,CAAC0B,YAAY,CAAC;IAElCtB,gBAAgB,CAACoB,IAAI,CAAC;IACtBlB,UAAU,CAACmB,WAAW,CAAC;EACzB,CAAC;EAED,MAAME,wBAAwB,GAAGA,CAAA,KAAM;IACrC/B,YAAY,CAAC,WAAW,CAAC;EAC3B,CAAC;EAED,MAAMgC,4BAA4B,GAAGA,CAAA,KAAM;IACzChC,YAAY,CAAC,MAAM,CAAC;EACtB,CAAC;EAED,MAAMiC,yBAAyB,GAAIC,OAAe,IAAK;IACrDC,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;IACxDD,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEF,OAAO,CAACG,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC;IAChE7B,gBAAgB,CAAC0B,OAAO,CAAC;;IAEzB;IACA;EACF,CAAC;EAED,MAAMI,aAAa,GAAGA,CAAA,KAAM;IAC1B,QAAQvC,SAAS;MACf,KAAK,WAAW;QACd,oBACEH,OAAA,CAACb,cAAc;UACbkB,QAAQ,EAAEA,QAAS;UACnBsC,cAAc,EAAEpC;QAAkB;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAEN,KAAK,MAAM;QACT,oBAAO/C,OAAA,CAACZ,oBAAoB;UACnBiB,QAAQ,EAAEA,QAAS;UACnB2C,aAAa,EAAErC;QAAc;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC;MACX,KAAK,KAAK;QACR,oBAAO/C,OAAA,CAACX,aAAa;UAACgB,QAAQ,EAAEA;QAAS;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC9C;QACE,oBACE/C,OAAA,CAACd,aAAa;UACZmB,QAAQ,EAAEA,QAAS;UACnB4C,kBAAkB,EAAElB,sBAAuB;UAC3CmB,aAAa,EAAEzC,QAAS;UACxB0C,kBAAkB,EAAExC,aAAc;UAClCyC,YAAY,EAAEvC,OAAQ;UACtBwC,oBAAoB,EAAElB,wBAAyB;UAC/CmB,wBAAwB,EAAElB,4BAA6B;UACvDmB,qBAAqB,EAAElB;QAA0B;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC;IAER;EACF,CAAC;;EAED;;EAEA,oBACE/C,OAAA;IAAKwD,KAAK,EAAE;MACV,GAAGlE,MAAM,CAACmE,SAAS;MACnBC,eAAe,EAAErD,QAAQ,GAAG,SAAS,GAAG,SAAS;MACjDsD,KAAK,EAAEtD,QAAQ,GAAG,SAAS,GAAG;IAChC,CAAE;IAAAuD,QAAA,GACC3C,SAAS,iBAAIjB,OAAA,CAACT,KAAK;MAACc,QAAQ,EAAEA,QAAS;MAACwD,OAAO,EAAEA,CAAA,KAAM3C,YAAY,CAAC,KAAK;IAAE;MAAA0B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAG/E/C,OAAA,CAACF,cAAc;MACbO,QAAQ,EAAEA,QAAS;MACnByD,MAAM,EAAE/C,aAAc;MACtBgD,QAAQ,EAAEA,CAAA,KAAM/C,gBAAgB,CAAC,CAACD,aAAa;IAAE;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD,CAAC,eAEF/C,OAAA;MAAKwD,KAAK,EAAE;QACV,GAAGlE,MAAM,CAAC0E,WAAW;QACrBC,UAAU,EAAElD,aAAa,GAAG,OAAO,GAAG;MACxC,CAAE;MAAA6C,QAAA,gBACA5D,OAAA;QAAKwD,KAAK,EAAElE,MAAM,CAAC4E,MAAO;QAAAN,QAAA,gBACxB5D,OAAA;UAAKwD,KAAK,EAAElE,MAAM,CAAC6E,UAAW;UAAAP,QAAA,gBAC5B5D,OAAA;YACEoE,OAAO,EAAEA,CAAA,KAAMpD,gBAAgB,CAAC,CAACD,aAAa,CAAE;YAChDyC,KAAK,EAAElE,MAAM,CAAC+E,mBAAoB;YAAAT,QAAA,EAEjC7C,aAAa,gBACZf,OAAA;cAAKsE,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC,GAAG;cAACC,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAAAjB,QAAA,eAC5I5D,OAAA;gBAAM8E,CAAC,EAAC;cAAiB;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,gBAEN/C,OAAA;cAAKsE,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC,GAAG;cAACC,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAAAjB,QAAA,eAC5I5D,OAAA;gBAAM8E,CAAC,EAAC;cAAe;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC,eACT/C,OAAA;YAAIwD,KAAK,EAAElE,MAAM,CAACyF,KAAM;YAAAnB,QAAA,EAAEtC,CAAC,CAAC,WAAW;UAAC;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC,eACN/C,OAAA;UAAKwD,KAAK,EAAElE,MAAM,CAAC0F,WAAY;UAAApB,QAAA,gBAC7B5D,OAAA;YACEoE,OAAO,EAAEA,CAAA,KAAM9D,WAAW,CAAC,CAACD,QAAQ,CAAE;YACtCmD,KAAK,EAAElE,MAAM,CAAC2F,UAAW;YAAArB,QAAA,EAExBvD,QAAQ,gBACPL,OAAA;cAAKsE,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC,GAAG;cAACC,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAAAjB,QAAA,gBAC5I5D,OAAA;gBAAQkF,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,IAAI;gBAACC,CAAC,EAAC;cAAG;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,eACvC/C,OAAA;gBAAMqF,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,GAAG;gBAACC,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC;cAAG;gBAAA5C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3C/C,OAAA;gBAAMqF,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC;cAAI;gBAAA5C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC7C/C,OAAA;gBAAMqF,EAAE,EAAC,MAAM;gBAACC,EAAE,EAAC,MAAM;gBAACC,EAAE,EAAC,MAAM;gBAACC,EAAE,EAAC;cAAM;gBAAA5C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrD/C,OAAA;gBAAMqF,EAAE,EAAC,OAAO;gBAACC,EAAE,EAAC,OAAO;gBAACC,EAAE,EAAC,OAAO;gBAACC,EAAE,EAAC;cAAO;gBAAA5C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACzD/C,OAAA;gBAAMqF,EAAE,EAAC,GAAG;gBAACC,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,GAAG;gBAACC,EAAE,EAAC;cAAI;gBAAA5C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3C/C,OAAA;gBAAMqF,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC;cAAI;gBAAA5C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC7C/C,OAAA;gBAAMqF,EAAE,EAAC,MAAM;gBAACC,EAAE,EAAC,OAAO;gBAACC,EAAE,EAAC,MAAM;gBAACC,EAAE,EAAC;cAAO;gBAAA5C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvD/C,OAAA;gBAAMqF,EAAE,EAAC,OAAO;gBAACC,EAAE,EAAC,MAAM;gBAACC,EAAE,EAAC,OAAO;gBAACC,EAAE,EAAC;cAAM;gBAAA5C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,gBAEN/C,OAAA;cAAKsE,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC,GAAG;cAACC,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAAAjB,QAAA,eAC5I5D,OAAA;gBAAM8E,CAAC,EAAC;cAAiD;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9D;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC,eAGT/C,OAAA;YACEoE,OAAO,EAAEA,CAAA,KAAM/C,WAAW,CAACD,QAAQ,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI,CAAE;YAC5DoC,KAAK,EAAElE,MAAM,CAACmG,cAAe;YAC7BV,KAAK,EAAEzD,CAAC,CAAC,iBAAiB,CAAE;YAAAsC,QAAA,EAE3BtC,CAAC,CAAC,kBAAkB;UAAC;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,EAGR5B,WAAW,gBACVnB,OAAA,CAACR,WAAW;YAACa,QAAQ,EAAEA;UAAS;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEnC/C,OAAA;YACEoE,OAAO,EAAEA,CAAA,KAAMlD,YAAY,CAAC,IAAI,CAAE;YAClCsC,KAAK,EAAElE,MAAM,CAACoG,WAAY;YAAA9B,QAAA,gBAE1B5D,OAAA;cAAKsE,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC,GAAG;cAACC,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAAAjB,QAAA,gBAC5I5D,OAAA;gBAAM8E,CAAC,EAAC;cAA2C;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3D/C,OAAA;gBAAQkF,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,GAAG;gBAACC,CAAC,EAAC;cAAG;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,EACLzB,CAAC,CAAC,cAAc,CAAC;UAAA;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN/C,OAAA;QAAKwD,KAAK,EAAElE,MAAM,CAACqG,IAAK;QAAA/B,QAAA,gBACtB5D,OAAA;UACEoE,OAAO,EAAEA,CAAA,KAAMhE,YAAY,CAAC,QAAQ,CAAE;UACtCoD,KAAK,EAAE;YACL,GAAGlE,MAAM,CAACsG,SAAS;YACnBC,YAAY,EAAE1F,SAAS,KAAK,QAAQ,GAAG,aAAaE,QAAQ,GAAG,SAAS,GAAG,SAAS,EAAE,GAAG,MAAM;YAC/FsD,KAAK,EAAExD,SAAS,KAAK,QAAQ,GAAIE,QAAQ,GAAG,SAAS,GAAG,SAAS,GAAKA,QAAQ,GAAG,SAAS,GAAG;UAC/F,CAAE;UAAAuD,QAAA,gBAEF5D,OAAA;YAAKsE,KAAK,EAAC,IAAI;YAACC,MAAM,EAAC,IAAI;YAACC,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,WAAW,EAAC,GAAG;YAACC,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC,OAAO;YAAAjB,QAAA,eAC5I5D,OAAA;cAAM8E,CAAC,EAAC;YAAqU;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7U,CAAC,EACLzB,CAAC,CAAC,YAAY,CAAC;QAAA;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAET/C,OAAA;UACEoE,OAAO,EAAEA,CAAA,KAAMhE,YAAY,CAAC,WAAW,CAAE;UACzCoD,KAAK,EAAE;YACL,GAAGlE,MAAM,CAACsG,SAAS;YACnBC,YAAY,EAAE1F,SAAS,KAAK,WAAW,GAAG,aAAaE,QAAQ,GAAG,SAAS,GAAG,SAAS,EAAE,GAAG,MAAM;YAClGsD,KAAK,EAAExD,SAAS,KAAK,WAAW,GAAIE,QAAQ,GAAG,SAAS,GAAG,SAAS,GAAKA,QAAQ,GAAG,SAAS,GAAG;UAClG,CAAE;UAAAuD,QAAA,gBAEF5D,OAAA;YAAKsE,KAAK,EAAC,IAAI;YAACC,MAAM,EAAC,IAAI;YAACC,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,WAAW,EAAC,GAAG;YAACC,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC,OAAO;YAAAjB,QAAA,eAC5I5D,OAAA;cAAM8E,CAAC,EAAC;YAA0K;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClL,CAAC,EACLzB,CAAC,CAAC,eAAe,CAAC;QAAA;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,eAET/C,OAAA;UACEoE,OAAO,EAAEA,CAAA,KAAMhE,YAAY,CAAC,MAAM,CAAE;UACpCoD,KAAK,EAAE;YACL,GAAGlE,MAAM,CAACsG,SAAS;YACnBC,YAAY,EAAE1F,SAAS,KAAK,MAAM,GAAG,aAAaE,QAAQ,GAAG,SAAS,GAAG,SAAS,EAAE,GAAG,MAAM;YAC7FsD,KAAK,EAAExD,SAAS,KAAK,MAAM,GAAIE,QAAQ,GAAG,SAAS,GAAG,SAAS,GAAKA,QAAQ,GAAG,SAAS,GAAG;UAC7F,CAAE;UAAAuD,QAAA,gBAEF5D,OAAA;YAAKsE,KAAK,EAAC,IAAI;YAACC,MAAM,EAAC,IAAI;YAACC,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,WAAW,EAAC,GAAG;YAACC,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC,OAAO;YAAAjB,QAAA,eAC5I5D,OAAA;cAAM8E,CAAC,EAAC;YAA8Q;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtR,CAAC,EACLzB,CAAC,CAAC,UAAU,CAAC;QAAA;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAET/C,OAAA;UACEoE,OAAO,EAAEA,CAAA,KAAMhE,YAAY,CAAC,KAAK,CAAE;UACnCoD,KAAK,EAAE;YACL,GAAGlE,MAAM,CAACsG,SAAS;YACnBC,YAAY,EAAE1F,SAAS,KAAK,KAAK,GAAG,aAAaE,QAAQ,GAAG,SAAS,GAAG,SAAS,EAAE,GAAG,MAAM;YAC5FsD,KAAK,EAAExD,SAAS,KAAK,KAAK,GAAIE,QAAQ,GAAG,SAAS,GAAG,SAAS,GAAKA,QAAQ,GAAG,SAAS,GAAG;UAC5F,CAAE;UAAAuD,QAAA,gBAEF5D,OAAA;YAAKsE,KAAK,EAAC,IAAI;YAACC,MAAM,EAAC,IAAI;YAACC,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,WAAW,EAAC,GAAG;YAACC,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC,OAAO;YAAAjB,QAAA,eAC5I5D,OAAA;cAAM8E,CAAC,EAAC;YAAge;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxe,CAAC,EACLzB,CAAC,CAAC,mBAAmB,CAAC;QAAA;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEN/C,OAAA;QAAKwD,KAAK,EAAElE,MAAM,CAACwG,cAAe;QAAAlC,QAAA,gBAChC5D,OAAA;UAAKwD,KAAK,EAAElE,MAAM,CAACyG,aAAc;UAAAnC,QAAA,gBAC/B5D,OAAA;YAAIwD,KAAK,EAAElE,MAAM,CAAC0G,YAAa;YAAApC,QAAA,GAC5BzD,SAAS,KAAK,QAAQ,IAAImB,CAAC,CAAC,sBAAsB,CAAC,EACnDnB,SAAS,KAAK,WAAW,IAAImB,CAAC,CAAC,yBAAyB,CAAC,EACzDnB,SAAS,KAAK,MAAM,IAAImB,CAAC,CAAC,oBAAoB,CAAC,EAC/CnB,SAAS,KAAK,KAAK,IAAImB,CAAC,CAAC,6BAA6B,CAAC;UAAA;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eACL/C,OAAA;YAAGwD,KAAK,EAAElE,MAAM,CAAC2G,eAAgB;YAAArC,QAAA,GAC9BzD,SAAS,KAAK,QAAQ,IAAImB,CAAC,CAAC,yBAAyB,CAAC,EACtDnB,SAAS,KAAK,WAAW,IAAImB,CAAC,CAAC,4BAA4B,CAAC,EAC5DnB,SAAS,KAAK,MAAM,IAAImB,CAAC,CAAC,uBAAuB,CAAC,EAClDnB,SAAS,KAAK,KAAK,IAAImB,CAAC,CAAC,gCAAgC,CAAC;UAAA;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAEN/C,OAAA;UAAKwD,KAAK,EAAElE,MAAM,CAAC4G,cAAe;UAAAtC,QAAA,EAC/BlB,aAAa,CAAC;QAAC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN/C,OAAA;QAAKwD,KAAK,EAAElE,MAAM,CAAC6G,MAAO;QAAAvC,QAAA,eACxB5D,OAAA;UAAA4D,QAAA,EAAItC,CAAC,CAAC,aAAa;QAAC;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAA7C,EAAA,CAlRMD,UAAoB;EAAA,QAUAP,OAAO,EACMG,WAAW;AAAA;AAAAuG,EAAA,GAX5CnG,UAAoB;AAmR1B,MAAMoG,GAAa,GAAGA,CAAA,KAAM;EAC1B,oBACErG,OAAA,CAACJ,gBAAgB;IAAAgE,QAAA,eACf5D,OAAA,CAACP,YAAY;MAAAmE,QAAA,eACX5D,OAAA,CAACL,eAAe;QAAAiE,QAAA,eACd5D,OAAA,CAACC,UAAU;UAAA2C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEvB,CAAC;AAACuD,GAAA,GAVID,GAAa;AAYnB,eAAeA,GAAG;AAAC,IAAAD,EAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAH,EAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}