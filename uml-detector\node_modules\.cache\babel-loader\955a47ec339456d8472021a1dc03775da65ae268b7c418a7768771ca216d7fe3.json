{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\FixTorchUMLDGM\\\\uml-detector\\\\src\\\\components\\\\Documentation.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { useLanguage } from \"../context/LanguageContext\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Documentation = ({\n  darkMode\n}) => {\n  _s();\n  const {\n    t\n  } = useLanguage();\n  const [activeSection, setActiveSection] = useState(\"overview\");\n  const styles = {\n    container: {\n      maxWidth: \"1200px\",\n      margin: \"0 auto\",\n      padding: \"20px\",\n      color: darkMode ? \"#e0e0e0\" : \"#333\"\n    },\n    header: {\n      textAlign: \"center\",\n      marginBottom: \"40px\",\n      borderBottom: `2px solid ${darkMode ? \"#4f46e5\" : \"#2563eb\"}`,\n      paddingBottom: \"30px\"\n    },\n    title: {\n      fontSize: \"2.5rem\",\n      fontWeight: \"700\",\n      color: darkMode ? \"#4f46e5\" : \"#2563eb\",\n      marginBottom: \"10px\"\n    },\n    subtitle: {\n      fontSize: \"1.2rem\",\n      opacity: 0.8,\n      marginBottom: \"20px\"\n    },\n    version: {\n      display: \"inline-block\",\n      backgroundColor: darkMode ? \"#4f46e5\" : \"#2563eb\",\n      color: \"white\",\n      padding: \"6px 12px\",\n      borderRadius: \"20px\",\n      fontSize: \"0.9rem\",\n      fontWeight: \"500\"\n    },\n    navigation: {\n      display: \"flex\",\n      justifyContent: \"center\",\n      flexWrap: \"wrap\",\n      gap: \"10px\",\n      marginBottom: \"40px\",\n      padding: \"20px\",\n      backgroundColor: darkMode ? \"#1e293b\" : \"#f8fafc\",\n      borderRadius: \"12px\",\n      border: `1px solid ${darkMode ? \"#334155\" : \"#e2e8f0\"}`\n    },\n    navButton: isActive => ({\n      padding: \"10px 20px\",\n      borderRadius: \"8px\",\n      border: \"none\",\n      cursor: \"pointer\",\n      fontSize: \"14px\",\n      fontWeight: \"500\",\n      transition: \"all 0.2s ease\",\n      backgroundColor: isActive ? darkMode ? \"#4f46e5\" : \"#2563eb\" : darkMode ? \"#374151\" : \"#e5e7eb\",\n      color: isActive ? \"white\" : darkMode ? \"#d1d5db\" : \"#374151\",\n      transform: isActive ? \"translateY(-2px)\" : \"none\",\n      boxShadow: isActive ? \"0 4px 12px rgba(79, 70, 229, 0.3)\" : \"none\"\n    }),\n    section: {\n      backgroundColor: darkMode ? \"#1e293b\" : \"#ffffff\",\n      borderRadius: \"12px\",\n      padding: \"30px\",\n      marginBottom: \"30px\",\n      boxShadow: darkMode ? \"0 4px 20px rgba(0,0,0,0.3)\" : \"0 4px 20px rgba(0,0,0,0.08)\",\n      border: `1px solid ${darkMode ? \"#334155\" : \"#e2e8f0\"}`\n    },\n    sectionTitle: {\n      color: darkMode ? \"#4f46e5\" : \"#2563eb\",\n      marginTop: \"0\",\n      marginBottom: \"25px\",\n      display: \"flex\",\n      alignItems: \"center\",\n      gap: \"12px\",\n      fontSize: \"1.8rem\",\n      fontWeight: \"600\"\n    },\n    featureGrid: {\n      display: \"grid\",\n      gridTemplateColumns: \"repeat(auto-fit, minmax(300px, 1fr))\",\n      gap: \"20px\",\n      marginBottom: \"30px\"\n    },\n    featureCard: {\n      backgroundColor: darkMode ? \"#2d3748\" : \"#f7fafc\",\n      borderRadius: \"10px\",\n      padding: \"20px\",\n      border: `1px solid ${darkMode ? \"#4a5568\" : \"#e2e8f0\"}`,\n      transition: \"all 0.2s ease\",\n      cursor: \"pointer\"\n    },\n    featureIcon: {\n      width: \"24px\",\n      height: \"24px\",\n      color: darkMode ? \"#4f46e5\" : \"#2563eb\",\n      marginBottom: \"15px\"\n    },\n    featureTitle: {\n      fontSize: \"1.1rem\",\n      fontWeight: \"600\",\n      marginBottom: \"10px\",\n      color: darkMode ? \"#e2e8f0\" : \"#1a202c\"\n    },\n    featureDescription: {\n      fontSize: \"0.95rem\",\n      lineHeight: \"1.6\",\n      opacity: 0.8\n    },\n    techStack: {\n      display: \"grid\",\n      gridTemplateColumns: \"repeat(auto-fit, minmax(200px, 1fr))\",\n      gap: \"15px\",\n      marginTop: \"20px\"\n    },\n    techItem: {\n      display: \"flex\",\n      alignItems: \"center\",\n      gap: \"10px\",\n      padding: \"12px\",\n      backgroundColor: darkMode ? \"#2d3748\" : \"#f7fafc\",\n      borderRadius: \"8px\",\n      border: `1px solid ${darkMode ? \"#4a5568\" : \"#e2e8f0\"}`\n    },\n    icon: {\n      width: \"20px\",\n      height: \"20px\",\n      flexShrink: 0\n    }\n  };\n  const navigationItems = [{\n    id: \"overview\",\n    label: t('doc.overview'),\n    icon: \"🏠\"\n  }, {\n    id: \"features\",\n    label: t('doc.features'),\n    icon: \"⚡\"\n  }, {\n    id: \"guide\",\n    label: t('doc.guide'),\n    icon: \"📖\"\n  }, {\n    id: \"tech\",\n    label: t('doc.technologies'),\n    icon: \"🔧\"\n  }, {\n    id: \"api\",\n    label: t('doc.api'),\n    icon: \"🔌\"\n  }, {\n    id: \"faq\",\n    label: t('doc.faq'),\n    icon: \"❓\"\n  }];\n  const renderOverview = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    style: styles.section,\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      style: styles.sectionTitle,\n      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n        style: styles.icon,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        children: [/*#__PURE__*/_jsxDEV(\"path\", {\n          d: \"M3 7V17C3 18.1046 3.89543 19 5 19H19C20.1046 19 21 18.1046 21 17V7C21 5.89543 20.1046 5 19 5H5C3.89543 5 3 5.89543 3 7Z\",\n          strokeWidth: \"2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n          d: \"M8 9L12 13L16 9\",\n          strokeWidth: \"2\",\n          strokeLinecap: \"round\",\n          strokeLinejoin: \"round\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this), t('doc.projectOverview')]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: \"30px\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          color: darkMode ? \"#e2e8f0\" : \"#1a202c\",\n          marginBottom: \"15px\"\n        },\n        children: t('doc.appTitle')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          fontSize: \"1.1rem\",\n          lineHeight: \"1.7\",\n          marginBottom: \"20px\"\n        },\n        children: t('doc.description')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.featureGrid,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.featureCard,\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            style: styles.featureIcon,\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z\",\n              strokeWidth: \"2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.featureTitle,\n            children: t('doc.features.aiDetection.title')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.featureDescription,\n            children: t('doc.features.aiDetection.description')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.featureCard,\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            style: styles.featureIcon,\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M13 2L3 14H12L11 22L21 10H12L13 2Z\",\n              strokeWidth: \"2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.featureTitle,\n            children: t('doc.features.fastProcessing.title')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.featureDescription,\n            children: t('doc.features.fastProcessing.description')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.featureCard,\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            style: styles.featureIcon,\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            children: [/*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M12 2L2 7L12 12L22 7L12 2Z\",\n              strokeWidth: \"2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M2 17L12 22L22 17\",\n              strokeWidth: \"2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M2 12L12 17L22 12\",\n              strokeWidth: \"2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.featureTitle,\n            children: t('doc.features.multiFormat.title')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.featureDescription,\n            children: t('doc.features.multiFormat.description')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 165,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 156,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: styles.container,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.header,\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        style: styles.title,\n        children: t('doc.title')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: styles.subtitle,\n        children: t('doc.subtitle')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        style: styles.version,\n        children: t('doc.version')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 213,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.navigation,\n      children: navigationItems.map(item => /*#__PURE__*/_jsxDEV(\"button\", {\n        style: styles.navButton(activeSection === item.id),\n        onClick: () => setActiveSection(item.id),\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            marginRight: \"8px\"\n          },\n          children: item.icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 13\n        }, this), item.label]\n      }, item.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 222,\n      columnNumber: 7\n    }, this), activeSection === \"overview\" && renderOverview(), activeSection === \"features\" && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.section,\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: styles.sectionTitle,\n        children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n          style: styles.icon,\n          viewBox: \"0 0 24 24\",\n          fill: \"none\",\n          stroke: \"currentColor\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M13 2L3 14H12L11 22L21 10H12L13 2Z\",\n            strokeWidth: \"2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 13\n        }, this), t('doc.featuresTitle')]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.featureGrid,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.featureCard,\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            style: styles.featureIcon,\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M4 16L8.586 11.414C8.961 11.039 9.47 10.828 10 10.828C10.53 10.828 11.039 11.039 11.414 11.414L16 16M14 14L15.586 12.414C15.961 12.039 16.47 11.828 17 11.828C17.53 11.828 18.039 12.039 18.414 12.414L20 14M14 8H14.01M6 20H18C19.105 20 20 19.105 20 18V6C20 4.895 19.105 4 18 4H6C4.895 4 4 4.895 4 6V18C4 19.105 4.895 20 6 20Z\",\n              strokeWidth: \"2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.featureTitle,\n            children: t('doc.features.uploadAnalyze.title')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.featureDescription,\n            children: t('doc.features.uploadAnalyze.description')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.featureCard,\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            style: styles.featureIcon,\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M9 3V5M15 3V5M9 19V21M15 19V21M5 9H3M5 15H3M21 9H19M21 15H19M7 19H17C18.105 19 19 18.105 19 17V7C19 5.895 18.105 5 17 5H7C5.895 5 5 5.895 5 7V17C5 18.105 5.895 19 7 19Z\",\n              strokeWidth: \"2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.featureTitle,\n            children: t('doc.features.relationDetection.title')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.featureDescription,\n            children: t('doc.features.relationDetection.description')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.featureCard,\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            style: styles.featureIcon,\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            children: [/*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M12 3H5C4.46957 3 3.96086 3.21071 3.58579 3.58579C3.21071 3.96086 3 4.46957 3 5V19C3 19.5304 3.21071 20.0391 3.58579 20.4142C3.96086 20.7893 4.46957 21 5 21H19C19.5304 21 20.0391 20.7893 20.4142 20.4142C20.7893 20.0391 21 19.5304 21 19V12\",\n              strokeWidth: \"2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M16 3L21 8L8 21L3 21L3 16L16 3Z\",\n              strokeWidth: \"2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.featureTitle,\n            children: t('doc.features.textExtraction.title')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.featureDescription,\n            children: t('doc.features.textExtraction.description')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.featureCard,\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            style: styles.featureIcon,\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            children: [/*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z\",\n              strokeWidth: \"2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M14 2V8H20\",\n              strokeWidth: \"2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M16 13H8\",\n              strokeWidth: \"2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M16 17H8\",\n              strokeWidth: \"2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M10 9H8\",\n              strokeWidth: \"2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.featureTitle,\n            children: t('doc.features.codeGeneration.title')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.featureDescription,\n            children: t('doc.features.codeGeneration.description')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.featureCard,\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            style: styles.featureIcon,\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M16 4H18C18.5304 4 19.0391 4.21071 19.4142 4.58579C19.7893 4.96086 20 5.46957 20 6V18C20 18.5304 19.7893 19.0391 19.4142 19.4142C19.0391 19.7893 18.5304 20 18 20H6C5.46957 20 4.96086 19.7893 4.58579 19.4142C4.21071 19.0391 4 18.5304 4 18V6C4 5.46957 4.21071 4.96086 4.58579 4.58579C4.96086 4.21071 5.46957 4 6 4H8M16 4V2M8 4V2M8 4V6M16 4V6\",\n              strokeWidth: \"2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.featureTitle,\n            children: t('doc.features.historyManagement.title')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.featureDescription,\n            children: t('doc.features.historyManagement.description')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.featureCard,\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            style: styles.featureIcon,\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            children: [/*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z\",\n              strokeWidth: \"2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M8 12L11 15L16 10\",\n              strokeWidth: \"2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.featureTitle,\n            children: t('doc.features.modernInterface.title')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.featureDescription,\n            children: t('doc.features.modernInterface.description')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 9\n    }, this), activeSection === \"guide\" && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.section,\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: styles.sectionTitle,\n        children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n          style: styles.icon,\n          viewBox: \"0 0 24 24\",\n          fill: \"none\",\n          stroke: \"currentColor\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M12 6.253V16.747M7 10L12 5L17 10M7 14L12 19L17 14\",\n            strokeWidth: \"2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 13\n        }, this), t('doc.guideTitle'), \" \\xE9tape par \\xE9tape\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 319,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: \"30px\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            ...styles.featureCard,\n            marginBottom: \"20px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: \"flex\",\n              alignItems: \"center\",\n              gap: \"15px\",\n              marginBottom: \"15px\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                backgroundColor: darkMode ? \"#4f46e5\" : \"#2563eb\",\n                color: \"white\",\n                borderRadius: \"50%\",\n                width: \"30px\",\n                height: \"30px\",\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"center\",\n                fontWeight: \"bold\"\n              },\n              children: \"1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              style: styles.featureTitle,\n              children: t('doc.guide.step1.title')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: styles.featureDescription,\n            children: t('doc.guide.step1.description')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            ...styles.featureCard,\n            marginBottom: \"20px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: \"flex\",\n              alignItems: \"center\",\n              gap: \"15px\",\n              marginBottom: \"15px\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                backgroundColor: darkMode ? \"#4f46e5\" : \"#2563eb\",\n                color: \"white\",\n                borderRadius: \"50%\",\n                width: \"30px\",\n                height: \"30px\",\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"center\",\n                fontWeight: \"bold\"\n              },\n              children: \"2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              style: styles.featureTitle,\n              children: t('doc.guide.step2.title')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: styles.featureDescription,\n            children: t('doc.guide.step2.description')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 347,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            ...styles.featureCard,\n            marginBottom: \"20px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: \"flex\",\n              alignItems: \"center\",\n              gap: \"15px\",\n              marginBottom: \"15px\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                backgroundColor: darkMode ? \"#4f46e5\" : \"#2563eb\",\n                color: \"white\",\n                borderRadius: \"50%\",\n                width: \"30px\",\n                height: \"30px\",\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"center\",\n                fontWeight: \"bold\"\n              },\n              children: \"3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 369,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              style: styles.featureTitle,\n              children: t('doc.guide.step3.title')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: styles.featureDescription,\n            children: t('doc.guide.step3.description')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 382,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            ...styles.featureCard,\n            marginBottom: \"20px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: \"flex\",\n              alignItems: \"center\",\n              gap: \"15px\",\n              marginBottom: \"15px\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                backgroundColor: darkMode ? \"#4f46e5\" : \"#2563eb\",\n                color: \"white\",\n                borderRadius: \"50%\",\n                width: \"30px\",\n                height: \"30px\",\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"center\",\n                fontWeight: \"bold\"\n              },\n              children: \"4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              style: styles.featureTitle,\n              children: t('doc.guide.step4.title')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: styles.featureDescription,\n            children: t('doc.guide.step4.description')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 387,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 326,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 318,\n      columnNumber: 9\n    }, this), activeSection === \"tech\" && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.section,\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: styles.sectionTitle,\n        children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n          style: styles.icon,\n          viewBox: \"0 0 24 24\",\n          fill: \"none\",\n          stroke: \"currentColor\",\n          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M12 2L2 7L12 12L22 7L12 2Z\",\n            strokeWidth: \"2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 414,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M2 17L12 22L22 17\",\n            strokeWidth: \"2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M2 12L12 17L22 12\",\n            strokeWidth: \"2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 416,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 413,\n          columnNumber: 13\n        }, this), t('doc.techTitle')]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 412,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.techStack,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.techItem,\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            style: styles.icon,\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M12 2L2 7L12 12L22 7L12 2Z\",\n              strokeWidth: \"2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 424,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: t('doc.tech.frontend.title')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                margin: \"5px 0 0 0\",\n                fontSize: \"0.9rem\",\n                opacity: 0.8\n              },\n              children: t('doc.tech.frontend.description')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 426,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 422,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.techItem,\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            style: styles.icon,\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M21 16V8C20.9996 7.64927 20.9071 7.30481 20.7315 7.00116C20.556 6.69751 20.3037 6.44536 20 6.27L13 2.27C12.696 2.09446 12.3511 2.00205 12 2.00205C11.6489 2.00205 11.304 2.09446 11 2.27L4 6.27C3.69626 6.44536 3.44398 6.69751 3.26846 7.00116C3.09294 7.30481 3.00036 7.64927 3 8V16C3.00036 16.3507 3.09294 16.6952 3.26846 16.9988C3.44398 17.3025 3.69626 17.5546 4 17.73L11 21.73C11.304 21.9055 11.6489 21.9979 12 21.9979C12.3511 21.9979 12.696 21.9055 13 21.73L20 17.73C20.3037 17.5546 20.556 17.3025 20.7315 16.9988C20.9071 16.6952 20.9996 16.3507 21 16Z\",\n              strokeWidth: \"2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 436,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 435,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: t('doc.tech.backend.title')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 439,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                margin: \"5px 0 0 0\",\n                fontSize: \"0.9rem\",\n                opacity: 0.8\n              },\n              children: t('doc.tech.backend.description')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 440,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 438,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 434,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.techItem,\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            style: styles.icon,\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z\",\n              strokeWidth: \"2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 448,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 447,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: t('doc.tech.ai.title')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 451,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                margin: \"5px 0 0 0\",\n                fontSize: \"0.9rem\",\n                opacity: 0.8\n              },\n              children: t('doc.tech.ai.description')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 452,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 446,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.techItem,\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            style: styles.icon,\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M16 4H18C18.5304 4 19.0391 4.21071 19.4142 4.58579C19.7893 4.96086 20 5.46957 20 6V18C20 18.5304 19.7893 19.0391 19.4142 19.4142C19.0391 19.7893 18.5304 20 18 20H6C5.46957 20 4.96086 19.7893 4.58579 19.4142C4.21071 19.0391 4 18.5304 4 18V6C4 5.46957 4.21071 4.96086 4.58579 4.58579C4.96086 4.21071 5.46957 4 6 4H8\",\n              strokeWidth: \"2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 460,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 459,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: t('doc.tech.database.title')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 463,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                margin: \"5px 0 0 0\",\n                fontSize: \"0.9rem\",\n                opacity: 0.8\n              },\n              children: t('doc.tech.database.description')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 464,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 458,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.techItem,\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            style: styles.icon,\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z\",\n              strokeWidth: \"2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 472,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 471,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: t('doc.tech.visualization.title')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 475,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                margin: \"5px 0 0 0\",\n                fontSize: \"0.9rem\",\n                opacity: 0.8\n              },\n              children: t('doc.tech.visualization.description')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 476,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 474,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 470,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 421,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 411,\n      columnNumber: 9\n    }, this), activeSection === \"api\" && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.section,\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: styles.sectionTitle,\n        children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n          style: styles.icon,\n          viewBox: \"0 0 24 24\",\n          fill: \"none\",\n          stroke: \"currentColor\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M8 9L12 5L16 9M8 15L12 19L16 15\",\n            strokeWidth: \"2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 489,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 488,\n          columnNumber: 13\n        }, this), t('doc.apiTitle')]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 487,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: \"30px\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            color: darkMode ? \"#e2e8f0\" : \"#1a202c\",\n            marginBottom: \"15px\"\n          },\n          children: t('doc.api.endpointsTitle')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 495,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            ...styles.featureCard,\n            marginBottom: \"20px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: \"flex\",\n              alignItems: \"center\",\n              gap: \"10px\",\n              marginBottom: \"10px\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                backgroundColor: \"#10b981\",\n                color: \"white\",\n                padding: \"4px 8px\",\n                borderRadius: \"4px\",\n                fontSize: \"0.8rem\",\n                fontWeight: \"bold\"\n              },\n              children: \"POST\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 501,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"code\", {\n              style: {\n                backgroundColor: darkMode ? \"#374151\" : \"#f3f4f6\",\n                padding: \"4px 8px\",\n                borderRadius: \"4px\",\n                fontFamily: \"monospace\"\n              },\n              children: \"/detect/\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 509,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 500,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: styles.featureDescription,\n            children: t('doc.api.detect.description')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 516,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 499,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            ...styles.featureCard,\n            marginBottom: \"20px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: \"flex\",\n              alignItems: \"center\",\n              gap: \"10px\",\n              marginBottom: \"10px\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                backgroundColor: \"#3b82f6\",\n                color: \"white\",\n                padding: \"4px 8px\",\n                borderRadius: \"4px\",\n                fontSize: \"0.8rem\",\n                fontWeight: \"bold\"\n              },\n              children: \"GET\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 523,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"code\", {\n              style: {\n                backgroundColor: darkMode ? \"#374151\" : \"#f3f4f6\",\n                padding: \"4px 8px\",\n                borderRadius: \"4px\",\n                fontFamily: \"monospace\"\n              },\n              children: \"/annotated_image.jpg\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 531,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 522,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: styles.featureDescription,\n            children: t('doc.api.annotated.description')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 538,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 521,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            ...styles.featureCard,\n            marginBottom: \"20px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: \"flex\",\n              alignItems: \"center\",\n              gap: \"10px\",\n              marginBottom: \"10px\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                backgroundColor: \"#3b82f6\",\n                color: \"white\",\n                padding: \"4px 8px\",\n                borderRadius: \"4px\",\n                fontSize: \"0.8rem\",\n                fontWeight: \"bold\"\n              },\n              children: \"GET\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 545,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"code\", {\n              style: {\n                backgroundColor: darkMode ? \"#374151\" : \"#f3f4f6\",\n                padding: \"4px 8px\",\n                borderRadius: \"4px\",\n                fontFamily: \"monospace\"\n              },\n              children: \"/resultats_classes.txt\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 553,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 544,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: styles.featureDescription,\n            children: t('doc.api.results.description')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 560,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 543,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 494,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 486,\n      columnNumber: 9\n    }, this), activeSection === \"faq\" && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.section,\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: styles.sectionTitle,\n        children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n          style: styles.icon,\n          viewBox: \"0 0 24 24\",\n          fill: \"none\",\n          stroke: \"currentColor\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M8.228 9C8.777 7.835 9.758 6.945 10.99 6.537C12.221 6.13 13.61 6.242 14.753 6.846C15.896 7.45 16.683 8.488 16.926 9.685C17.17 10.883 16.85 12.115 16.053 13.019C15.256 13.923 14.07 14.401 12.828 14.319C11.585 14.237 10.469 13.603 9.8 12.65M12 17V17.01M12 3C16.418 3 20 6.582 20 11C20 15.418 16.418 19 12 19C7.582 19 4 15.418 4 11C4 6.582 7.582 3 12 3Z\",\n            strokeWidth: \"2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 572,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 571,\n          columnNumber: 13\n        }, this), t('doc.faqTitle')]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 570,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: \"30px\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            ...styles.featureCard,\n            marginBottom: \"20px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              ...styles.featureTitle,\n              marginBottom: \"10px\"\n            },\n            children: t('doc.faq.formats.question')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 579,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: styles.featureDescription,\n            children: t('doc.faq.formats.answer')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 582,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 578,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            ...styles.featureCard,\n            marginBottom: \"20px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              ...styles.featureTitle,\n              marginBottom: \"10px\"\n            },\n            children: t('doc.faq.offline.question')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 588,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: styles.featureDescription,\n            children: t('doc.faq.offline.answer')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 591,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 587,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            ...styles.featureCard,\n            marginBottom: \"20px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              ...styles.featureTitle,\n              marginBottom: \"10px\"\n            },\n            children: t('doc.faq.codeGeneration.question')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 597,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: styles.featureDescription,\n            children: t('doc.faq.codeGeneration.answer')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 600,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 596,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            ...styles.featureCard,\n            marginBottom: \"20px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              ...styles.featureTitle,\n              marginBottom: \"10px\"\n            },\n            children: t('doc.faq.accuracy.question')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 606,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: styles.featureDescription,\n            children: t('doc.faq.accuracy.answer')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 609,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 605,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            ...styles.featureCard,\n            marginBottom: \"20px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              ...styles.featureTitle,\n              marginBottom: \"10px\"\n            },\n            children: t('doc.faq.security.question')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 615,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: styles.featureDescription,\n            children: t('doc.faq.security.answer')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 618,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 614,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            ...styles.featureCard,\n            marginBottom: \"20px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              ...styles.featureTitle,\n              marginBottom: \"10px\"\n            },\n            children: t('doc.faq.speed.question')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 624,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: styles.featureDescription,\n            children: t('doc.faq.speed.answer')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 627,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 623,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 577,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 569,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: \"center\",\n        margin: \"40px 0\",\n        backgroundColor: darkMode ? \"rgba(79, 70, 229, 0.2)\" : \"rgba(37, 99, 235, 0.1)\",\n        borderRadius: \"16px\",\n        padding: \"40px 30px\",\n        border: darkMode ? \"1px solid rgba(79, 70, 229, 0.3)\" : \"1px solid rgba(37, 99, 235, 0.2)\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          marginBottom: \"20px\",\n          display: \"flex\",\n          alignItems: \"center\",\n          justifyContent: \"center\",\n          gap: \"12px\",\n          color: darkMode ? \"#e2e8f0\" : \"#1e40af\",\n          fontSize: \"1.5rem\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n          style: {\n            width: \"24px\",\n            height: \"24px\",\n            stroke: darkMode ? \"#e2e8f0\" : \"#1e40af\"\n          },\n          viewBox: \"0 0 24 24\",\n          fill: \"none\",\n          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M18 8C18 4.686 15.314 2 12 2C8.686 2 6 4.686 6 8C6 11.313 8.686 14 12 14C15.314 14 18 11.313 18 8Z\",\n            strokeWidth: \"2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 654,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M12 18C7.582 18 4 15.313 4 11.999V16C4 17.105 4.895 18 6 18H18C19.105 18 20 17.105 20 16V12C20 15.313 16.418 18 12 18Z\",\n            strokeWidth: \"2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 655,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 653,\n          columnNumber: 11\n        }, this), t('doc.contact.title')]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 644,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          marginBottom: \"25px\",\n          color: darkMode ? \"#cbd5e1\" : \"#475569\",\n          fontSize: \"1.1rem\",\n          lineHeight: \"1.6\"\n        },\n        children: t('doc.contact.description')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 659,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 636,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 211,\n    columnNumber: 5\n  }, this);\n};\n_s(Documentation, \"021GIHjjS7dii666cekxR+CX2rI=\", false, function () {\n  return [useLanguage];\n});\n_c = Documentation;\nexport default Documentation;\nvar _c;\n$RefreshReg$(_c, \"Documentation\");", "map": {"version": 3, "names": ["React", "useState", "useLanguage", "jsxDEV", "_jsxDEV", "Documentation", "darkMode", "_s", "t", "activeSection", "setActiveSection", "styles", "container", "max<PERSON><PERSON><PERSON>", "margin", "padding", "color", "header", "textAlign", "marginBottom", "borderBottom", "paddingBottom", "title", "fontSize", "fontWeight", "subtitle", "opacity", "version", "display", "backgroundColor", "borderRadius", "navigation", "justifyContent", "flexWrap", "gap", "border", "navButton", "isActive", "cursor", "transition", "transform", "boxShadow", "section", "sectionTitle", "marginTop", "alignItems", "featureGrid", "gridTemplateColumns", "featureCard", "featureIcon", "width", "height", "featureTitle", "featureDescription", "lineHeight", "techStack", "techItem", "icon", "flexShrink", "navigationItems", "id", "label", "renderOverview", "style", "children", "viewBox", "fill", "stroke", "d", "strokeWidth", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "strokeLinecap", "strokeLinejoin", "map", "item", "onClick", "marginRight", "fontFamily", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/FixTorchUMLDGM/uml-detector/src/components/Documentation.tsx"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport { useLanguage } from \"../context/LanguageContext\";\r\n\r\ninterface DocumentationProps {\r\n  darkMode: boolean;\r\n}\r\n\r\nconst Documentation: React.FC<DocumentationProps> = ({ darkMode }) => {\r\n  const { t } = useLanguage();\r\n  const [activeSection, setActiveSection] = useState<string>(\"overview\");\r\n\r\n  const styles = {\r\n    container: {\r\n      maxWidth: \"1200px\",\r\n      margin: \"0 auto\",\r\n      padding: \"20px\",\r\n      color: darkMode ? \"#e0e0e0\" : \"#333\"\r\n    },\r\n    header: {\r\n      textAlign: \"center\" as const,\r\n      marginBottom: \"40px\",\r\n      borderBottom: `2px solid ${darkMode ? \"#4f46e5\" : \"#2563eb\"}`,\r\n      paddingBottom: \"30px\"\r\n    },\r\n    title: {\r\n      fontSize: \"2.5rem\",\r\n      fontWeight: \"700\",\r\n      color: darkMode ? \"#4f46e5\" : \"#2563eb\",\r\n      marginBottom: \"10px\"\r\n    },\r\n    subtitle: {\r\n      fontSize: \"1.2rem\",\r\n      opacity: 0.8,\r\n      marginBottom: \"20px\"\r\n    },\r\n    version: {\r\n      display: \"inline-block\",\r\n      backgroundColor: darkMode ? \"#4f46e5\" : \"#2563eb\",\r\n      color: \"white\",\r\n      padding: \"6px 12px\",\r\n      borderRadius: \"20px\",\r\n      fontSize: \"0.9rem\",\r\n      fontWeight: \"500\"\r\n    },\r\n    navigation: {\r\n      display: \"flex\",\r\n      justifyContent: \"center\",\r\n      flexWrap: \"wrap\" as const,\r\n      gap: \"10px\",\r\n      marginBottom: \"40px\",\r\n      padding: \"20px\",\r\n      backgroundColor: darkMode ? \"#1e293b\" : \"#f8fafc\",\r\n      borderRadius: \"12px\",\r\n      border: `1px solid ${darkMode ? \"#334155\" : \"#e2e8f0\"}`\r\n    },\r\n    navButton: (isActive: boolean) => ({\r\n      padding: \"10px 20px\",\r\n      borderRadius: \"8px\",\r\n      border: \"none\",\r\n      cursor: \"pointer\",\r\n      fontSize: \"14px\",\r\n      fontWeight: \"500\",\r\n      transition: \"all 0.2s ease\",\r\n      backgroundColor: isActive\r\n        ? (darkMode ? \"#4f46e5\" : \"#2563eb\")\r\n        : (darkMode ? \"#374151\" : \"#e5e7eb\"),\r\n      color: isActive\r\n        ? \"white\"\r\n        : (darkMode ? \"#d1d5db\" : \"#374151\"),\r\n      transform: isActive ? \"translateY(-2px)\" : \"none\",\r\n      boxShadow: isActive ? \"0 4px 12px rgba(79, 70, 229, 0.3)\" : \"none\"\r\n    }),\r\n    section: {\r\n      backgroundColor: darkMode ? \"#1e293b\" : \"#ffffff\",\r\n      borderRadius: \"12px\",\r\n      padding: \"30px\",\r\n      marginBottom: \"30px\",\r\n      boxShadow: darkMode\r\n        ? \"0 4px 20px rgba(0,0,0,0.3)\"\r\n        : \"0 4px 20px rgba(0,0,0,0.08)\",\r\n      border: `1px solid ${darkMode ? \"#334155\" : \"#e2e8f0\"}`\r\n    },\r\n    sectionTitle: {\r\n      color: darkMode ? \"#4f46e5\" : \"#2563eb\",\r\n      marginTop: \"0\",\r\n      marginBottom: \"25px\",\r\n      display: \"flex\",\r\n      alignItems: \"center\",\r\n      gap: \"12px\",\r\n      fontSize: \"1.8rem\",\r\n      fontWeight: \"600\"\r\n    },\r\n    featureGrid: {\r\n      display: \"grid\",\r\n      gridTemplateColumns: \"repeat(auto-fit, minmax(300px, 1fr))\",\r\n      gap: \"20px\",\r\n      marginBottom: \"30px\"\r\n    },\r\n    featureCard: {\r\n      backgroundColor: darkMode ? \"#2d3748\" : \"#f7fafc\",\r\n      borderRadius: \"10px\",\r\n      padding: \"20px\",\r\n      border: `1px solid ${darkMode ? \"#4a5568\" : \"#e2e8f0\"}`,\r\n      transition: \"all 0.2s ease\",\r\n      cursor: \"pointer\"\r\n    },\r\n    featureIcon: {\r\n      width: \"24px\",\r\n      height: \"24px\",\r\n      color: darkMode ? \"#4f46e5\" : \"#2563eb\",\r\n      marginBottom: \"15px\"\r\n    },\r\n    featureTitle: {\r\n      fontSize: \"1.1rem\",\r\n      fontWeight: \"600\",\r\n      marginBottom: \"10px\",\r\n      color: darkMode ? \"#e2e8f0\" : \"#1a202c\"\r\n    },\r\n    featureDescription: {\r\n      fontSize: \"0.95rem\",\r\n      lineHeight: \"1.6\",\r\n      opacity: 0.8\r\n    },\r\n    techStack: {\r\n      display: \"grid\",\r\n      gridTemplateColumns: \"repeat(auto-fit, minmax(200px, 1fr))\",\r\n      gap: \"15px\",\r\n      marginTop: \"20px\"\r\n    },\r\n    techItem: {\r\n      display: \"flex\",\r\n      alignItems: \"center\",\r\n      gap: \"10px\",\r\n      padding: \"12px\",\r\n      backgroundColor: darkMode ? \"#2d3748\" : \"#f7fafc\",\r\n      borderRadius: \"8px\",\r\n      border: `1px solid ${darkMode ? \"#4a5568\" : \"#e2e8f0\"}`\r\n    },\r\n    icon: {\r\n      width: \"20px\",\r\n      height: \"20px\",\r\n      flexShrink: 0\r\n    }\r\n  };\r\n\r\n  const navigationItems = [\r\n    { id: \"overview\", label: t('doc.overview'), icon: \"🏠\" },\r\n    { id: \"features\", label: t('doc.features'), icon: \"⚡\" },\r\n    { id: \"guide\", label: t('doc.guide'), icon: \"📖\" },\r\n    { id: \"tech\", label: t('doc.technologies'), icon: \"🔧\" },\r\n    { id: \"api\", label: t('doc.api'), icon: \"🔌\" },\r\n    { id: \"faq\", label: t('doc.faq'), icon: \"❓\" }\r\n  ];\r\n\r\n  const renderOverview = () => (\r\n    <div style={styles.section}>\r\n      <h2 style={styles.sectionTitle}>\r\n        <svg style={styles.icon} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\r\n          <path d=\"M3 7V17C3 18.1046 3.89543 19 5 19H19C20.1046 19 21 18.1046 21 17V7C21 5.89543 20.1046 5 19 5H5C3.89543 5 3 5.89543 3 7Z\" strokeWidth=\"2\"/>\r\n          <path d=\"M8 9L12 13L16 9\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n        </svg>\r\n{t('doc.projectOverview')}\r\n      </h2>\r\n\r\n      <div style={{ marginBottom: \"30px\" }}>\r\n        <h3 style={{ color: darkMode ? \"#e2e8f0\" : \"#1a202c\", marginBottom: \"15px\" }}>\r\n{t('doc.appTitle')}\r\n        </h3>\r\n        <p style={{ fontSize: \"1.1rem\", lineHeight: \"1.7\", marginBottom: \"20px\" }}>\r\n          {t('doc.description')}\r\n        </p>\r\n\r\n        <div style={styles.featureGrid}>\r\n          <div style={styles.featureCard}>\r\n            <svg style={styles.featureIcon} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\r\n              <path d=\"M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z\" strokeWidth=\"2\"/>\r\n            </svg>\r\n            <div style={styles.featureTitle}>{t('doc.features.aiDetection.title')}</div>\r\n            <div style={styles.featureDescription}>\r\n              {t('doc.features.aiDetection.description')}\r\n            </div>\r\n          </div>\r\n\r\n          <div style={styles.featureCard}>\r\n            <svg style={styles.featureIcon} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\r\n              <path d=\"M13 2L3 14H12L11 22L21 10H12L13 2Z\" strokeWidth=\"2\"/>\r\n            </svg>\r\n            <div style={styles.featureTitle}>{t('doc.features.fastProcessing.title')}</div>\r\n            <div style={styles.featureDescription}>\r\n              {t('doc.features.fastProcessing.description')}\r\n            </div>\r\n          </div>\r\n\r\n          <div style={styles.featureCard}>\r\n            <svg style={styles.featureIcon} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\r\n              <path d=\"M12 2L2 7L12 12L22 7L12 2Z\" strokeWidth=\"2\"/>\r\n              <path d=\"M2 17L12 22L22 17\" strokeWidth=\"2\"/>\r\n              <path d=\"M2 12L12 17L22 12\" strokeWidth=\"2\"/>\r\n            </svg>\r\n            <div style={styles.featureTitle}>{t('doc.features.multiFormat.title')}</div>\r\n            <div style={styles.featureDescription}>\r\n              {t('doc.features.multiFormat.description')}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  return (\r\n    <div style={styles.container}>\r\n      {/* En-tête principal */}\r\n      <div style={styles.header}>\r\n        <h1 style={styles.title}>{t('doc.title')}</h1>\r\n        <p style={styles.subtitle}>\r\n          {t('doc.subtitle')}\r\n        </p>\r\n        <span style={styles.version}>{t('doc.version')}</span>\r\n      </div>\r\n\r\n      {/* Navigation */}\r\n      <div style={styles.navigation}>\r\n        {navigationItems.map((item) => (\r\n          <button\r\n            key={item.id}\r\n            style={styles.navButton(activeSection === item.id)}\r\n            onClick={() => setActiveSection(item.id)}\r\n          >\r\n            <span style={{ marginRight: \"8px\" }}>{item.icon}</span>\r\n            {item.label}\r\n          </button>\r\n        ))}\r\n      </div>\r\n\r\n      {/* Contenu dynamique */}\r\n      {activeSection === \"overview\" && renderOverview()}\r\n\r\n      {activeSection === \"features\" && (\r\n        <div style={styles.section}>\r\n          <h2 style={styles.sectionTitle}>\r\n            <svg style={styles.icon} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\r\n              <path d=\"M13 2L3 14H12L11 22L21 10H12L13 2Z\" strokeWidth=\"2\"/>\r\n            </svg>\r\n{t('doc.featuresTitle')}\r\n          </h2>\r\n\r\n          <div style={styles.featureGrid}>\r\n            <div style={styles.featureCard}>\r\n              <svg style={styles.featureIcon} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\r\n                <path d=\"M4 16L8.586 11.414C8.961 11.039 9.47 10.828 10 10.828C10.53 10.828 11.039 11.039 11.414 11.414L16 16M14 14L15.586 12.414C15.961 12.039 16.47 11.828 17 11.828C17.53 11.828 18.039 12.039 18.414 12.414L20 14M14 8H14.01M6 20H18C19.105 20 20 19.105 20 18V6C20 4.895 19.105 4 18 4H6C4.895 4 4 4.895 4 6V18C4 19.105 4.895 20 6 20Z\" strokeWidth=\"2\"/>\r\n              </svg>\r\n              <div style={styles.featureTitle}>{t('doc.features.uploadAnalyze.title')}</div>\r\n              <div style={styles.featureDescription}>\r\n                {t('doc.features.uploadAnalyze.description')}\r\n              </div>\r\n            </div>\r\n\r\n            <div style={styles.featureCard}>\r\n              <svg style={styles.featureIcon} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\r\n                <path d=\"M9 3V5M15 3V5M9 19V21M15 19V21M5 9H3M5 15H3M21 9H19M21 15H19M7 19H17C18.105 19 19 18.105 19 17V7C19 5.895 18.105 5 17 5H7C5.895 5 5 5.895 5 7V17C5 18.105 5.895 19 7 19Z\" strokeWidth=\"2\"/>\r\n              </svg>\r\n              <div style={styles.featureTitle}>{t('doc.features.relationDetection.title')}</div>\r\n              <div style={styles.featureDescription}>\r\n                {t('doc.features.relationDetection.description')}\r\n              </div>\r\n            </div>\r\n\r\n            <div style={styles.featureCard}>\r\n              <svg style={styles.featureIcon} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\r\n                <path d=\"M12 3H5C4.46957 3 3.96086 3.21071 3.58579 3.58579C3.21071 3.96086 3 4.46957 3 5V19C3 19.5304 3.21071 20.0391 3.58579 20.4142C3.96086 20.7893 4.46957 21 5 21H19C19.5304 21 20.0391 20.7893 20.4142 20.4142C20.7893 20.0391 21 19.5304 21 19V12\" strokeWidth=\"2\"/>\r\n                <path d=\"M16 3L21 8L8 21L3 21L3 16L16 3Z\" strokeWidth=\"2\"/>\r\n              </svg>\r\n              <div style={styles.featureTitle}>{t('doc.features.textExtraction.title')}</div>\r\n              <div style={styles.featureDescription}>\r\n                {t('doc.features.textExtraction.description')}\r\n              </div>\r\n            </div>\r\n\r\n            <div style={styles.featureCard}>\r\n              <svg style={styles.featureIcon} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\r\n                <path d=\"M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z\" strokeWidth=\"2\"/>\r\n                <path d=\"M14 2V8H20\" strokeWidth=\"2\"/>\r\n                <path d=\"M16 13H8\" strokeWidth=\"2\"/>\r\n                <path d=\"M16 17H8\" strokeWidth=\"2\"/>\r\n                <path d=\"M10 9H8\" strokeWidth=\"2\"/>\r\n              </svg>\r\n              <div style={styles.featureTitle}>{t('doc.features.codeGeneration.title')}</div>\r\n              <div style={styles.featureDescription}>\r\n                {t('doc.features.codeGeneration.description')}\r\n              </div>\r\n            </div>\r\n\r\n            <div style={styles.featureCard}>\r\n              <svg style={styles.featureIcon} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\r\n                <path d=\"M16 4H18C18.5304 4 19.0391 4.21071 19.4142 4.58579C19.7893 4.96086 20 5.46957 20 6V18C20 18.5304 19.7893 19.0391 19.4142 19.4142C19.0391 19.7893 18.5304 20 18 20H6C5.46957 20 4.96086 19.7893 4.58579 19.4142C4.21071 19.0391 4 18.5304 4 18V6C4 5.46957 4.21071 4.96086 4.58579 4.58579C4.96086 4.21071 5.46957 4 6 4H8M16 4V2M8 4V2M8 4V6M16 4V6\" strokeWidth=\"2\"/>\r\n              </svg>\r\n              <div style={styles.featureTitle}>{t('doc.features.historyManagement.title')}</div>\r\n              <div style={styles.featureDescription}>\r\n                {t('doc.features.historyManagement.description')}\r\n              </div>\r\n            </div>\r\n\r\n            <div style={styles.featureCard}>\r\n              <svg style={styles.featureIcon} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\r\n                <path d=\"M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z\" strokeWidth=\"2\"/>\r\n                <path d=\"M8 12L11 15L16 10\" strokeWidth=\"2\"/>\r\n              </svg>\r\n              <div style={styles.featureTitle}>{t('doc.features.modernInterface.title')}</div>\r\n              <div style={styles.featureDescription}>\r\n                {t('doc.features.modernInterface.description')}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {activeSection === \"guide\" && (\r\n        <div style={styles.section}>\r\n          <h2 style={styles.sectionTitle}>\r\n            <svg style={styles.icon} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\r\n              <path d=\"M12 6.253V16.747M7 10L12 5L17 10M7 14L12 19L17 14\" strokeWidth=\"2\"/>\r\n            </svg>\r\n{t('doc.guideTitle')} étape par étape\r\n          </h2>\r\n\r\n          <div style={{ marginBottom: \"30px\" }}>\r\n            <div style={{ ...styles.featureCard, marginBottom: \"20px\" }}>\r\n              <div style={{ display: \"flex\", alignItems: \"center\", gap: \"15px\", marginBottom: \"15px\" }}>\r\n                <div style={{\r\n                  backgroundColor: darkMode ? \"#4f46e5\" : \"#2563eb\",\r\n                  color: \"white\",\r\n                  borderRadius: \"50%\",\r\n                  width: \"30px\",\r\n                  height: \"30px\",\r\n                  display: \"flex\",\r\n                  alignItems: \"center\",\r\n                  justifyContent: \"center\",\r\n                  fontWeight: \"bold\"\r\n                }}>1</div>\r\n                <h3 style={styles.featureTitle}>{t('doc.guide.step1.title')}</h3>\r\n              </div>\r\n              <p style={styles.featureDescription}>\r\n                {t('doc.guide.step1.description')}\r\n              </p>\r\n            </div>\r\n\r\n            <div style={{ ...styles.featureCard, marginBottom: \"20px\" }}>\r\n              <div style={{ display: \"flex\", alignItems: \"center\", gap: \"15px\", marginBottom: \"15px\" }}>\r\n                <div style={{\r\n                  backgroundColor: darkMode ? \"#4f46e5\" : \"#2563eb\",\r\n                  color: \"white\",\r\n                  borderRadius: \"50%\",\r\n                  width: \"30px\",\r\n                  height: \"30px\",\r\n                  display: \"flex\",\r\n                  alignItems: \"center\",\r\n                  justifyContent: \"center\",\r\n                  fontWeight: \"bold\"\r\n                }}>2</div>\r\n                <h3 style={styles.featureTitle}>{t('doc.guide.step2.title')}</h3>\r\n              </div>\r\n              <p style={styles.featureDescription}>\r\n                {t('doc.guide.step2.description')}\r\n              </p>\r\n            </div>\r\n\r\n            <div style={{ ...styles.featureCard, marginBottom: \"20px\" }}>\r\n              <div style={{ display: \"flex\", alignItems: \"center\", gap: \"15px\", marginBottom: \"15px\" }}>\r\n                <div style={{\r\n                  backgroundColor: darkMode ? \"#4f46e5\" : \"#2563eb\",\r\n                  color: \"white\",\r\n                  borderRadius: \"50%\",\r\n                  width: \"30px\",\r\n                  height: \"30px\",\r\n                  display: \"flex\",\r\n                  alignItems: \"center\",\r\n                  justifyContent: \"center\",\r\n                  fontWeight: \"bold\"\r\n                }}>3</div>\r\n                <h3 style={styles.featureTitle}>{t('doc.guide.step3.title')}</h3>\r\n              </div>\r\n              <p style={styles.featureDescription}>\r\n                {t('doc.guide.step3.description')}\r\n              </p>\r\n            </div>\r\n\r\n            <div style={{ ...styles.featureCard, marginBottom: \"20px\" }}>\r\n              <div style={{ display: \"flex\", alignItems: \"center\", gap: \"15px\", marginBottom: \"15px\" }}>\r\n                <div style={{\r\n                  backgroundColor: darkMode ? \"#4f46e5\" : \"#2563eb\",\r\n                  color: \"white\",\r\n                  borderRadius: \"50%\",\r\n                  width: \"30px\",\r\n                  height: \"30px\",\r\n                  display: \"flex\",\r\n                  alignItems: \"center\",\r\n                  justifyContent: \"center\",\r\n                  fontWeight: \"bold\"\r\n                }}>4</div>\r\n                <h3 style={styles.featureTitle}>{t('doc.guide.step4.title')}</h3>\r\n              </div>\r\n              <p style={styles.featureDescription}>\r\n                {t('doc.guide.step4.description')}\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {activeSection === \"tech\" && (\r\n        <div style={styles.section}>\r\n          <h2 style={styles.sectionTitle}>\r\n            <svg style={styles.icon} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\r\n              <path d=\"M12 2L2 7L12 12L22 7L12 2Z\" strokeWidth=\"2\"/>\r\n              <path d=\"M2 17L12 22L22 17\" strokeWidth=\"2\"/>\r\n              <path d=\"M2 12L12 17L22 12\" strokeWidth=\"2\"/>\r\n            </svg>\r\n{t('doc.techTitle')}\r\n          </h2>\r\n\r\n          <div style={styles.techStack}>\r\n            <div style={styles.techItem}>\r\n              <svg style={styles.icon} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\r\n                <path d=\"M12 2L2 7L12 12L22 7L12 2Z\" strokeWidth=\"2\"/>\r\n              </svg>\r\n              <div>\r\n                <strong>{t('doc.tech.frontend.title')}</strong>\r\n                <p style={{ margin: \"5px 0 0 0\", fontSize: \"0.9rem\", opacity: 0.8 }}>\r\n                  {t('doc.tech.frontend.description')}\r\n                </p>\r\n              </div>\r\n            </div>\r\n\r\n            <div style={styles.techItem}>\r\n              <svg style={styles.icon} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\r\n                <path d=\"M21 16V8C20.9996 7.64927 20.9071 7.30481 20.7315 7.00116C20.556 6.69751 20.3037 6.44536 20 6.27L13 2.27C12.696 2.09446 12.3511 2.00205 12 2.00205C11.6489 2.00205 11.304 2.09446 11 2.27L4 6.27C3.69626 6.44536 3.44398 6.69751 3.26846 7.00116C3.09294 7.30481 3.00036 7.64927 3 8V16C3.00036 16.3507 3.09294 16.6952 3.26846 16.9988C3.44398 17.3025 3.69626 17.5546 4 17.73L11 21.73C11.304 21.9055 11.6489 21.9979 12 21.9979C12.3511 21.9979 12.696 21.9055 13 21.73L20 17.73C20.3037 17.5546 20.556 17.3025 20.7315 16.9988C20.9071 16.6952 20.9996 16.3507 21 16Z\" strokeWidth=\"2\"/>\r\n              </svg>\r\n              <div>\r\n                <strong>{t('doc.tech.backend.title')}</strong>\r\n                <p style={{ margin: \"5px 0 0 0\", fontSize: \"0.9rem\", opacity: 0.8 }}>\r\n                  {t('doc.tech.backend.description')}\r\n                </p>\r\n              </div>\r\n            </div>\r\n\r\n            <div style={styles.techItem}>\r\n              <svg style={styles.icon} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\r\n                <path d=\"M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z\" strokeWidth=\"2\"/>\r\n              </svg>\r\n              <div>\r\n                <strong>{t('doc.tech.ai.title')}</strong>\r\n                <p style={{ margin: \"5px 0 0 0\", fontSize: \"0.9rem\", opacity: 0.8 }}>\r\n                  {t('doc.tech.ai.description')}\r\n                </p>\r\n              </div>\r\n            </div>\r\n\r\n            <div style={styles.techItem}>\r\n              <svg style={styles.icon} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\r\n                <path d=\"M16 4H18C18.5304 4 19.0391 4.21071 19.4142 4.58579C19.7893 4.96086 20 5.46957 20 6V18C20 18.5304 19.7893 19.0391 19.4142 19.4142C19.0391 19.7893 18.5304 20 18 20H6C5.46957 20 4.96086 19.7893 4.58579 19.4142C4.21071 19.0391 4 18.5304 4 18V6C4 5.46957 4.21071 4.96086 4.58579 4.58579C4.96086 4.21071 5.46957 4 6 4H8\" strokeWidth=\"2\"/>\r\n              </svg>\r\n              <div>\r\n                <strong>{t('doc.tech.database.title')}</strong>\r\n                <p style={{ margin: \"5px 0 0 0\", fontSize: \"0.9rem\", opacity: 0.8 }}>\r\n                  {t('doc.tech.database.description')}\r\n                </p>\r\n              </div>\r\n            </div>\r\n\r\n            <div style={styles.techItem}>\r\n              <svg style={styles.icon} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\r\n                <path d=\"M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z\" strokeWidth=\"2\"/>\r\n              </svg>\r\n              <div>\r\n                <strong>{t('doc.tech.visualization.title')}</strong>\r\n                <p style={{ margin: \"5px 0 0 0\", fontSize: \"0.9rem\", opacity: 0.8 }}>\r\n                  {t('doc.tech.visualization.description')}\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {activeSection === \"api\" && (\r\n        <div style={styles.section}>\r\n          <h2 style={styles.sectionTitle}>\r\n            <svg style={styles.icon} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\r\n              <path d=\"M8 9L12 5L16 9M8 15L12 19L16 15\" strokeWidth=\"2\"/>\r\n            </svg>\r\n{t('doc.apiTitle')}\r\n          </h2>\r\n\r\n          <div style={{ marginBottom: \"30px\" }}>\r\n            <h3 style={{ color: darkMode ? \"#e2e8f0\" : \"#1a202c\", marginBottom: \"15px\" }}>\r\n{t('doc.api.endpointsTitle')}\r\n            </h3>\r\n\r\n            <div style={{ ...styles.featureCard, marginBottom: \"20px\" }}>\r\n              <div style={{ display: \"flex\", alignItems: \"center\", gap: \"10px\", marginBottom: \"10px\" }}>\r\n                <span style={{\r\n                  backgroundColor: \"#10b981\",\r\n                  color: \"white\",\r\n                  padding: \"4px 8px\",\r\n                  borderRadius: \"4px\",\r\n                  fontSize: \"0.8rem\",\r\n                  fontWeight: \"bold\"\r\n                }}>POST</span>\r\n                <code style={{\r\n                  backgroundColor: darkMode ? \"#374151\" : \"#f3f4f6\",\r\n                  padding: \"4px 8px\",\r\n                  borderRadius: \"4px\",\r\n                  fontFamily: \"monospace\"\r\n                }}>/detect/</code>\r\n              </div>\r\n              <p style={styles.featureDescription}>\r\n                {t('doc.api.detect.description')}\r\n              </p>\r\n            </div>\r\n\r\n            <div style={{ ...styles.featureCard, marginBottom: \"20px\" }}>\r\n              <div style={{ display: \"flex\", alignItems: \"center\", gap: \"10px\", marginBottom: \"10px\" }}>\r\n                <span style={{\r\n                  backgroundColor: \"#3b82f6\",\r\n                  color: \"white\",\r\n                  padding: \"4px 8px\",\r\n                  borderRadius: \"4px\",\r\n                  fontSize: \"0.8rem\",\r\n                  fontWeight: \"bold\"\r\n                }}>GET</span>\r\n                <code style={{\r\n                  backgroundColor: darkMode ? \"#374151\" : \"#f3f4f6\",\r\n                  padding: \"4px 8px\",\r\n                  borderRadius: \"4px\",\r\n                  fontFamily: \"monospace\"\r\n                }}>/annotated_image.jpg</code>\r\n              </div>\r\n              <p style={styles.featureDescription}>\r\n                {t('doc.api.annotated.description')}\r\n              </p>\r\n            </div>\r\n\r\n            <div style={{ ...styles.featureCard, marginBottom: \"20px\" }}>\r\n              <div style={{ display: \"flex\", alignItems: \"center\", gap: \"10px\", marginBottom: \"10px\" }}>\r\n                <span style={{\r\n                  backgroundColor: \"#3b82f6\",\r\n                  color: \"white\",\r\n                  padding: \"4px 8px\",\r\n                  borderRadius: \"4px\",\r\n                  fontSize: \"0.8rem\",\r\n                  fontWeight: \"bold\"\r\n                }}>GET</span>\r\n                <code style={{\r\n                  backgroundColor: darkMode ? \"#374151\" : \"#f3f4f6\",\r\n                  padding: \"4px 8px\",\r\n                  borderRadius: \"4px\",\r\n                  fontFamily: \"monospace\"\r\n                }}>/resultats_classes.txt</code>\r\n              </div>\r\n              <p style={styles.featureDescription}>\r\n                {t('doc.api.results.description')}\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {activeSection === \"faq\" && (\r\n        <div style={styles.section}>\r\n          <h2 style={styles.sectionTitle}>\r\n            <svg style={styles.icon} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\r\n              <path d=\"M8.228 9C8.777 7.835 9.758 6.945 10.99 6.537C12.221 6.13 13.61 6.242 14.753 6.846C15.896 7.45 16.683 8.488 16.926 9.685C17.17 10.883 16.85 12.115 16.053 13.019C15.256 13.923 14.07 14.401 12.828 14.319C11.585 14.237 10.469 13.603 9.8 12.65M12 17V17.01M12 3C16.418 3 20 6.582 20 11C20 15.418 16.418 19 12 19C7.582 19 4 15.418 4 11C4 6.582 7.582 3 12 3Z\" strokeWidth=\"2\"/>\r\n            </svg>\r\n{t('doc.faqTitle')}\r\n          </h2>\r\n\r\n          <div style={{ marginBottom: \"30px\" }}>\r\n            <div style={{ ...styles.featureCard, marginBottom: \"20px\" }}>\r\n              <h3 style={{ ...styles.featureTitle, marginBottom: \"10px\" }}>\r\n                {t('doc.faq.formats.question')}\r\n              </h3>\r\n              <p style={styles.featureDescription}>\r\n                {t('doc.faq.formats.answer')}\r\n              </p>\r\n            </div>\r\n\r\n            <div style={{ ...styles.featureCard, marginBottom: \"20px\" }}>\r\n              <h3 style={{ ...styles.featureTitle, marginBottom: \"10px\" }}>\r\n                {t('doc.faq.offline.question')}\r\n              </h3>\r\n              <p style={styles.featureDescription}>\r\n                {t('doc.faq.offline.answer')}\r\n              </p>\r\n            </div>\r\n\r\n            <div style={{ ...styles.featureCard, marginBottom: \"20px\" }}>\r\n              <h3 style={{ ...styles.featureTitle, marginBottom: \"10px\" }}>\r\n                {t('doc.faq.codeGeneration.question')}\r\n              </h3>\r\n              <p style={styles.featureDescription}>\r\n                {t('doc.faq.codeGeneration.answer')}\r\n              </p>\r\n            </div>\r\n\r\n            <div style={{ ...styles.featureCard, marginBottom: \"20px\" }}>\r\n              <h3 style={{ ...styles.featureTitle, marginBottom: \"10px\" }}>\r\n                {t('doc.faq.accuracy.question')}\r\n              </h3>\r\n              <p style={styles.featureDescription}>\r\n                {t('doc.faq.accuracy.answer')}\r\n              </p>\r\n            </div>\r\n\r\n            <div style={{ ...styles.featureCard, marginBottom: \"20px\" }}>\r\n              <h3 style={{ ...styles.featureTitle, marginBottom: \"10px\" }}>\r\n                {t('doc.faq.security.question')}\r\n              </h3>\r\n              <p style={styles.featureDescription}>\r\n                {t('doc.faq.security.answer')}\r\n              </p>\r\n            </div>\r\n\r\n            <div style={{ ...styles.featureCard, marginBottom: \"20px\" }}>\r\n              <h3 style={{ ...styles.featureTitle, marginBottom: \"10px\" }}>\r\n                {t('doc.faq.speed.question')}\r\n              </h3>\r\n              <p style={styles.featureDescription}>\r\n                {t('doc.faq.speed.answer')}\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Section Contact */}\r\n      <div style={{\r\n        textAlign: \"center\",\r\n        margin: \"40px 0\",\r\n        backgroundColor: darkMode ? \"rgba(79, 70, 229, 0.2)\" : \"rgba(37, 99, 235, 0.1)\",\r\n        borderRadius: \"16px\",\r\n        padding: \"40px 30px\",\r\n        border: darkMode ? \"1px solid rgba(79, 70, 229, 0.3)\" : \"1px solid rgba(37, 99, 235, 0.2)\"\r\n      }}>\r\n        <h3 style={{\r\n          marginBottom: \"20px\",\r\n          display: \"flex\",\r\n          alignItems: \"center\",\r\n          justifyContent: \"center\",\r\n          gap: \"12px\",\r\n          color: darkMode ? \"#e2e8f0\" : \"#1e40af\",\r\n          fontSize: \"1.5rem\"\r\n        }}>\r\n          <svg style={{ width: \"24px\", height: \"24px\", stroke: darkMode ? \"#e2e8f0\" : \"#1e40af\" }} viewBox=\"0 0 24 24\" fill=\"none\">\r\n            <path d=\"M18 8C18 4.686 15.314 2 12 2C8.686 2 6 4.686 6 8C6 11.313 8.686 14 12 14C15.314 14 18 11.313 18 8Z\" strokeWidth=\"2\"/>\r\n            <path d=\"M12 18C7.582 18 4 15.313 4 11.999V16C4 17.105 4.895 18 6 18H18C19.105 18 20 17.105 20 16V12C20 15.313 16.418 18 12 18Z\" strokeWidth=\"2\"/>\r\n          </svg>\r\n{t('doc.contact.title')}\r\n        </h3>\r\n        <p style={{\r\n          marginBottom: \"25px\",\r\n          color: darkMode ? \"#cbd5e1\" : \"#475569\",\r\n          fontSize: \"1.1rem\",\r\n          lineHeight: \"1.6\"\r\n        }}>\r\n{t('doc.contact.description')}\r\n        </p>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Documentation;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAMzD,MAAMC,aAA2C,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACpE,MAAM;IAAEC;EAAE,CAAC,GAAGN,WAAW,CAAC,CAAC;EAC3B,MAAM,CAACO,aAAa,EAAEC,gBAAgB,CAAC,GAAGT,QAAQ,CAAS,UAAU,CAAC;EAEtE,MAAMU,MAAM,GAAG;IACbC,SAAS,EAAE;MACTC,QAAQ,EAAE,QAAQ;MAClBC,MAAM,EAAE,QAAQ;MAChBC,OAAO,EAAE,MAAM;MACfC,KAAK,EAAEV,QAAQ,GAAG,SAAS,GAAG;IAChC,CAAC;IACDW,MAAM,EAAE;MACNC,SAAS,EAAE,QAAiB;MAC5BC,YAAY,EAAE,MAAM;MACpBC,YAAY,EAAE,aAAad,QAAQ,GAAG,SAAS,GAAG,SAAS,EAAE;MAC7De,aAAa,EAAE;IACjB,CAAC;IACDC,KAAK,EAAE;MACLC,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAE,KAAK;MACjBR,KAAK,EAAEV,QAAQ,GAAG,SAAS,GAAG,SAAS;MACvCa,YAAY,EAAE;IAChB,CAAC;IACDM,QAAQ,EAAE;MACRF,QAAQ,EAAE,QAAQ;MAClBG,OAAO,EAAE,GAAG;MACZP,YAAY,EAAE;IAChB,CAAC;IACDQ,OAAO,EAAE;MACPC,OAAO,EAAE,cAAc;MACvBC,eAAe,EAAEvB,QAAQ,GAAG,SAAS,GAAG,SAAS;MACjDU,KAAK,EAAE,OAAO;MACdD,OAAO,EAAE,UAAU;MACnBe,YAAY,EAAE,MAAM;MACpBP,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAE;IACd,CAAC;IACDO,UAAU,EAAE;MACVH,OAAO,EAAE,MAAM;MACfI,cAAc,EAAE,QAAQ;MACxBC,QAAQ,EAAE,MAAe;MACzBC,GAAG,EAAE,MAAM;MACXf,YAAY,EAAE,MAAM;MACpBJ,OAAO,EAAE,MAAM;MACfc,eAAe,EAAEvB,QAAQ,GAAG,SAAS,GAAG,SAAS;MACjDwB,YAAY,EAAE,MAAM;MACpBK,MAAM,EAAE,aAAa7B,QAAQ,GAAG,SAAS,GAAG,SAAS;IACvD,CAAC;IACD8B,SAAS,EAAGC,QAAiB,KAAM;MACjCtB,OAAO,EAAE,WAAW;MACpBe,YAAY,EAAE,KAAK;MACnBK,MAAM,EAAE,MAAM;MACdG,MAAM,EAAE,SAAS;MACjBf,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,KAAK;MACjBe,UAAU,EAAE,eAAe;MAC3BV,eAAe,EAAEQ,QAAQ,GACpB/B,QAAQ,GAAG,SAAS,GAAG,SAAS,GAChCA,QAAQ,GAAG,SAAS,GAAG,SAAU;MACtCU,KAAK,EAAEqB,QAAQ,GACX,OAAO,GACN/B,QAAQ,GAAG,SAAS,GAAG,SAAU;MACtCkC,SAAS,EAAEH,QAAQ,GAAG,kBAAkB,GAAG,MAAM;MACjDI,SAAS,EAAEJ,QAAQ,GAAG,mCAAmC,GAAG;IAC9D,CAAC,CAAC;IACFK,OAAO,EAAE;MACPb,eAAe,EAAEvB,QAAQ,GAAG,SAAS,GAAG,SAAS;MACjDwB,YAAY,EAAE,MAAM;MACpBf,OAAO,EAAE,MAAM;MACfI,YAAY,EAAE,MAAM;MACpBsB,SAAS,EAAEnC,QAAQ,GACf,4BAA4B,GAC5B,6BAA6B;MACjC6B,MAAM,EAAE,aAAa7B,QAAQ,GAAG,SAAS,GAAG,SAAS;IACvD,CAAC;IACDqC,YAAY,EAAE;MACZ3B,KAAK,EAAEV,QAAQ,GAAG,SAAS,GAAG,SAAS;MACvCsC,SAAS,EAAE,GAAG;MACdzB,YAAY,EAAE,MAAM;MACpBS,OAAO,EAAE,MAAM;MACfiB,UAAU,EAAE,QAAQ;MACpBX,GAAG,EAAE,MAAM;MACXX,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAE;IACd,CAAC;IACDsB,WAAW,EAAE;MACXlB,OAAO,EAAE,MAAM;MACfmB,mBAAmB,EAAE,sCAAsC;MAC3Db,GAAG,EAAE,MAAM;MACXf,YAAY,EAAE;IAChB,CAAC;IACD6B,WAAW,EAAE;MACXnB,eAAe,EAAEvB,QAAQ,GAAG,SAAS,GAAG,SAAS;MACjDwB,YAAY,EAAE,MAAM;MACpBf,OAAO,EAAE,MAAM;MACfoB,MAAM,EAAE,aAAa7B,QAAQ,GAAG,SAAS,GAAG,SAAS,EAAE;MACvDiC,UAAU,EAAE,eAAe;MAC3BD,MAAM,EAAE;IACV,CAAC;IACDW,WAAW,EAAE;MACXC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,MAAM;MACdnC,KAAK,EAAEV,QAAQ,GAAG,SAAS,GAAG,SAAS;MACvCa,YAAY,EAAE;IAChB,CAAC;IACDiC,YAAY,EAAE;MACZ7B,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAE,KAAK;MACjBL,YAAY,EAAE,MAAM;MACpBH,KAAK,EAAEV,QAAQ,GAAG,SAAS,GAAG;IAChC,CAAC;IACD+C,kBAAkB,EAAE;MAClB9B,QAAQ,EAAE,SAAS;MACnB+B,UAAU,EAAE,KAAK;MACjB5B,OAAO,EAAE;IACX,CAAC;IACD6B,SAAS,EAAE;MACT3B,OAAO,EAAE,MAAM;MACfmB,mBAAmB,EAAE,sCAAsC;MAC3Db,GAAG,EAAE,MAAM;MACXU,SAAS,EAAE;IACb,CAAC;IACDY,QAAQ,EAAE;MACR5B,OAAO,EAAE,MAAM;MACfiB,UAAU,EAAE,QAAQ;MACpBX,GAAG,EAAE,MAAM;MACXnB,OAAO,EAAE,MAAM;MACfc,eAAe,EAAEvB,QAAQ,GAAG,SAAS,GAAG,SAAS;MACjDwB,YAAY,EAAE,KAAK;MACnBK,MAAM,EAAE,aAAa7B,QAAQ,GAAG,SAAS,GAAG,SAAS;IACvD,CAAC;IACDmD,IAAI,EAAE;MACJP,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,MAAM;MACdO,UAAU,EAAE;IACd;EACF,CAAC;EAED,MAAMC,eAAe,GAAG,CACtB;IAAEC,EAAE,EAAE,UAAU;IAAEC,KAAK,EAAErD,CAAC,CAAC,cAAc,CAAC;IAAEiD,IAAI,EAAE;EAAK,CAAC,EACxD;IAAEG,EAAE,EAAE,UAAU;IAAEC,KAAK,EAAErD,CAAC,CAAC,cAAc,CAAC;IAAEiD,IAAI,EAAE;EAAI,CAAC,EACvD;IAAEG,EAAE,EAAE,OAAO;IAAEC,KAAK,EAAErD,CAAC,CAAC,WAAW,CAAC;IAAEiD,IAAI,EAAE;EAAK,CAAC,EAClD;IAAEG,EAAE,EAAE,MAAM;IAAEC,KAAK,EAAErD,CAAC,CAAC,kBAAkB,CAAC;IAAEiD,IAAI,EAAE;EAAK,CAAC,EACxD;IAAEG,EAAE,EAAE,KAAK;IAAEC,KAAK,EAAErD,CAAC,CAAC,SAAS,CAAC;IAAEiD,IAAI,EAAE;EAAK,CAAC,EAC9C;IAAEG,EAAE,EAAE,KAAK;IAAEC,KAAK,EAAErD,CAAC,CAAC,SAAS,CAAC;IAAEiD,IAAI,EAAE;EAAI,CAAC,CAC9C;EAED,MAAMK,cAAc,GAAGA,CAAA,kBACrB1D,OAAA;IAAK2D,KAAK,EAAEpD,MAAM,CAAC+B,OAAQ;IAAAsB,QAAA,gBACzB5D,OAAA;MAAI2D,KAAK,EAAEpD,MAAM,CAACgC,YAAa;MAAAqB,QAAA,gBAC7B5D,OAAA;QAAK2D,KAAK,EAAEpD,MAAM,CAAC8C,IAAK;QAACQ,OAAO,EAAC,WAAW;QAACC,IAAI,EAAC,MAAM;QAACC,MAAM,EAAC,cAAc;QAAAH,QAAA,gBAC5E5D,OAAA;UAAMgE,CAAC,EAAC,yHAAyH;UAACC,WAAW,EAAC;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC,CAAC,eACnJrE,OAAA;UAAMgE,CAAC,EAAC,iBAAiB;UAACC,WAAW,EAAC,GAAG;UAACK,aAAa,EAAC,OAAO;UAACC,cAAc,EAAC;QAAO;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrF,CAAC,EACbjE,CAAC,CAAC,qBAAqB,CAAC;IAAA;MAAA8D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC,eAELrE,OAAA;MAAK2D,KAAK,EAAE;QAAE5C,YAAY,EAAE;MAAO,CAAE;MAAA6C,QAAA,gBACnC5D,OAAA;QAAI2D,KAAK,EAAE;UAAE/C,KAAK,EAAEV,QAAQ,GAAG,SAAS,GAAG,SAAS;UAAEa,YAAY,EAAE;QAAO,CAAE;QAAA6C,QAAA,EACpFxD,CAAC,CAAC,cAAc;MAAC;QAAA8D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACLrE,OAAA;QAAG2D,KAAK,EAAE;UAAExC,QAAQ,EAAE,QAAQ;UAAE+B,UAAU,EAAE,KAAK;UAAEnC,YAAY,EAAE;QAAO,CAAE;QAAA6C,QAAA,EACvExD,CAAC,CAAC,iBAAiB;MAAC;QAAA8D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC,eAEJrE,OAAA;QAAK2D,KAAK,EAAEpD,MAAM,CAACmC,WAAY;QAAAkB,QAAA,gBAC7B5D,OAAA;UAAK2D,KAAK,EAAEpD,MAAM,CAACqC,WAAY;UAAAgB,QAAA,gBAC7B5D,OAAA;YAAK2D,KAAK,EAAEpD,MAAM,CAACsC,WAAY;YAACgB,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAAAH,QAAA,eACnF5D,OAAA;cAAMgE,CAAC,EAAC,oIAAoI;cAACC,WAAW,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3J,CAAC,eACNrE,OAAA;YAAK2D,KAAK,EAAEpD,MAAM,CAACyC,YAAa;YAAAY,QAAA,EAAExD,CAAC,CAAC,gCAAgC;UAAC;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC5ErE,OAAA;YAAK2D,KAAK,EAAEpD,MAAM,CAAC0C,kBAAmB;YAAAW,QAAA,EACnCxD,CAAC,CAAC,sCAAsC;UAAC;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENrE,OAAA;UAAK2D,KAAK,EAAEpD,MAAM,CAACqC,WAAY;UAAAgB,QAAA,gBAC7B5D,OAAA;YAAK2D,KAAK,EAAEpD,MAAM,CAACsC,WAAY;YAACgB,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAAAH,QAAA,eACnF5D,OAAA;cAAMgE,CAAC,EAAC,oCAAoC;cAACC,WAAW,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eACNrE,OAAA;YAAK2D,KAAK,EAAEpD,MAAM,CAACyC,YAAa;YAAAY,QAAA,EAAExD,CAAC,CAAC,mCAAmC;UAAC;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/ErE,OAAA;YAAK2D,KAAK,EAAEpD,MAAM,CAAC0C,kBAAmB;YAAAW,QAAA,EACnCxD,CAAC,CAAC,yCAAyC;UAAC;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENrE,OAAA;UAAK2D,KAAK,EAAEpD,MAAM,CAACqC,WAAY;UAAAgB,QAAA,gBAC7B5D,OAAA;YAAK2D,KAAK,EAAEpD,MAAM,CAACsC,WAAY;YAACgB,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAAAH,QAAA,gBACnF5D,OAAA;cAAMgE,CAAC,EAAC,4BAA4B;cAACC,WAAW,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC,eACtDrE,OAAA;cAAMgE,CAAC,EAAC,mBAAmB;cAACC,WAAW,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC,eAC7CrE,OAAA;cAAMgE,CAAC,EAAC,mBAAmB;cAACC,WAAW,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eACNrE,OAAA;YAAK2D,KAAK,EAAEpD,MAAM,CAACyC,YAAa;YAAAY,QAAA,EAAExD,CAAC,CAAC,gCAAgC;UAAC;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC5ErE,OAAA;YAAK2D,KAAK,EAAEpD,MAAM,CAAC0C,kBAAmB;YAAAW,QAAA,EACnCxD,CAAC,CAAC,sCAAsC;UAAC;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,oBACErE,OAAA;IAAK2D,KAAK,EAAEpD,MAAM,CAACC,SAAU;IAAAoD,QAAA,gBAE3B5D,OAAA;MAAK2D,KAAK,EAAEpD,MAAM,CAACM,MAAO;MAAA+C,QAAA,gBACxB5D,OAAA;QAAI2D,KAAK,EAAEpD,MAAM,CAACW,KAAM;QAAA0C,QAAA,EAAExD,CAAC,CAAC,WAAW;MAAC;QAAA8D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC9CrE,OAAA;QAAG2D,KAAK,EAAEpD,MAAM,CAACc,QAAS;QAAAuC,QAAA,EACvBxD,CAAC,CAAC,cAAc;MAAC;QAAA8D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,eACJrE,OAAA;QAAM2D,KAAK,EAAEpD,MAAM,CAACgB,OAAQ;QAAAqC,QAAA,EAAExD,CAAC,CAAC,aAAa;MAAC;QAAA8D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnD,CAAC,eAGNrE,OAAA;MAAK2D,KAAK,EAAEpD,MAAM,CAACoB,UAAW;MAAAiC,QAAA,EAC3BL,eAAe,CAACiB,GAAG,CAAEC,IAAI,iBACxBzE,OAAA;QAEE2D,KAAK,EAAEpD,MAAM,CAACyB,SAAS,CAAC3B,aAAa,KAAKoE,IAAI,CAACjB,EAAE,CAAE;QACnDkB,OAAO,EAAEA,CAAA,KAAMpE,gBAAgB,CAACmE,IAAI,CAACjB,EAAE,CAAE;QAAAI,QAAA,gBAEzC5D,OAAA;UAAM2D,KAAK,EAAE;YAAEgB,WAAW,EAAE;UAAM,CAAE;UAAAf,QAAA,EAAEa,IAAI,CAACpB;QAAI;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACtDI,IAAI,CAAChB,KAAK;MAAA,GALNgB,IAAI,CAACjB,EAAE;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAMN,CACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAGLhE,aAAa,KAAK,UAAU,IAAIqD,cAAc,CAAC,CAAC,EAEhDrD,aAAa,KAAK,UAAU,iBAC3BL,OAAA;MAAK2D,KAAK,EAAEpD,MAAM,CAAC+B,OAAQ;MAAAsB,QAAA,gBACzB5D,OAAA;QAAI2D,KAAK,EAAEpD,MAAM,CAACgC,YAAa;QAAAqB,QAAA,gBAC7B5D,OAAA;UAAK2D,KAAK,EAAEpD,MAAM,CAAC8C,IAAK;UAACQ,OAAO,EAAC,WAAW;UAACC,IAAI,EAAC,MAAM;UAACC,MAAM,EAAC,cAAc;UAAAH,QAAA,eAC5E5D,OAAA;YAAMgE,CAAC,EAAC,oCAAoC;YAACC,WAAW,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC,EACjBjE,CAAC,CAAC,mBAAmB,CAAC;MAAA;QAAA8D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eAELrE,OAAA;QAAK2D,KAAK,EAAEpD,MAAM,CAACmC,WAAY;QAAAkB,QAAA,gBAC7B5D,OAAA;UAAK2D,KAAK,EAAEpD,MAAM,CAACqC,WAAY;UAAAgB,QAAA,gBAC7B5D,OAAA;YAAK2D,KAAK,EAAEpD,MAAM,CAACsC,WAAY;YAACgB,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAAAH,QAAA,eACnF5D,OAAA;cAAMgE,CAAC,EAAC,qUAAqU;cAACC,WAAW,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5V,CAAC,eACNrE,OAAA;YAAK2D,KAAK,EAAEpD,MAAM,CAACyC,YAAa;YAAAY,QAAA,EAAExD,CAAC,CAAC,kCAAkC;UAAC;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC9ErE,OAAA;YAAK2D,KAAK,EAAEpD,MAAM,CAAC0C,kBAAmB;YAAAW,QAAA,EACnCxD,CAAC,CAAC,wCAAwC;UAAC;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENrE,OAAA;UAAK2D,KAAK,EAAEpD,MAAM,CAACqC,WAAY;UAAAgB,QAAA,gBAC7B5D,OAAA;YAAK2D,KAAK,EAAEpD,MAAM,CAACsC,WAAY;YAACgB,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAAAH,QAAA,eACnF5D,OAAA;cAAMgE,CAAC,EAAC,0KAA0K;cAACC,WAAW,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjM,CAAC,eACNrE,OAAA;YAAK2D,KAAK,EAAEpD,MAAM,CAACyC,YAAa;YAAAY,QAAA,EAAExD,CAAC,CAAC,sCAAsC;UAAC;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClFrE,OAAA;YAAK2D,KAAK,EAAEpD,MAAM,CAAC0C,kBAAmB;YAAAW,QAAA,EACnCxD,CAAC,CAAC,4CAA4C;UAAC;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENrE,OAAA;UAAK2D,KAAK,EAAEpD,MAAM,CAACqC,WAAY;UAAAgB,QAAA,gBAC7B5D,OAAA;YAAK2D,KAAK,EAAEpD,MAAM,CAACsC,WAAY;YAACgB,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAAAH,QAAA,gBACnF5D,OAAA;cAAMgE,CAAC,EAAC,gPAAgP;cAACC,WAAW,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC,eAC1QrE,OAAA;cAAMgE,CAAC,EAAC,iCAAiC;cAACC,WAAW,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC,eACNrE,OAAA;YAAK2D,KAAK,EAAEpD,MAAM,CAACyC,YAAa;YAAAY,QAAA,EAAExD,CAAC,CAAC,mCAAmC;UAAC;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/ErE,OAAA;YAAK2D,KAAK,EAAEpD,MAAM,CAAC0C,kBAAmB;YAAAW,QAAA,EACnCxD,CAAC,CAAC,yCAAyC;UAAC;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENrE,OAAA;UAAK2D,KAAK,EAAEpD,MAAM,CAACqC,WAAY;UAAAgB,QAAA,gBAC7B5D,OAAA;YAAK2D,KAAK,EAAEpD,MAAM,CAACsC,WAAY;YAACgB,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAAAH,QAAA,gBACnF5D,OAAA;cAAMgE,CAAC,EAAC,qPAAqP;cAACC,WAAW,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC,eAC/QrE,OAAA;cAAMgE,CAAC,EAAC,YAAY;cAACC,WAAW,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC,eACtCrE,OAAA;cAAMgE,CAAC,EAAC,UAAU;cAACC,WAAW,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC,eACpCrE,OAAA;cAAMgE,CAAC,EAAC,UAAU;cAACC,WAAW,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC,eACpCrE,OAAA;cAAMgE,CAAC,EAAC,SAAS;cAACC,WAAW,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eACNrE,OAAA;YAAK2D,KAAK,EAAEpD,MAAM,CAACyC,YAAa;YAAAY,QAAA,EAAExD,CAAC,CAAC,mCAAmC;UAAC;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/ErE,OAAA;YAAK2D,KAAK,EAAEpD,MAAM,CAAC0C,kBAAmB;YAAAW,QAAA,EACnCxD,CAAC,CAAC,yCAAyC;UAAC;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENrE,OAAA;UAAK2D,KAAK,EAAEpD,MAAM,CAACqC,WAAY;UAAAgB,QAAA,gBAC7B5D,OAAA;YAAK2D,KAAK,EAAEpD,MAAM,CAACsC,WAAY;YAACgB,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAAAH,QAAA,eACnF5D,OAAA;cAAMgE,CAAC,EAAC,qVAAqV;cAACC,WAAW,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5W,CAAC,eACNrE,OAAA;YAAK2D,KAAK,EAAEpD,MAAM,CAACyC,YAAa;YAAAY,QAAA,EAAExD,CAAC,CAAC,sCAAsC;UAAC;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClFrE,OAAA;YAAK2D,KAAK,EAAEpD,MAAM,CAAC0C,kBAAmB;YAAAW,QAAA,EACnCxD,CAAC,CAAC,4CAA4C;UAAC;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENrE,OAAA;UAAK2D,KAAK,EAAEpD,MAAM,CAACqC,WAAY;UAAAgB,QAAA,gBAC7B5D,OAAA;YAAK2D,KAAK,EAAEpD,MAAM,CAACsC,WAAY;YAACgB,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAAAH,QAAA,gBACnF5D,OAAA;cAAMgE,CAAC,EAAC,mHAAmH;cAACC,WAAW,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC,eAC7IrE,OAAA;cAAMgE,CAAC,EAAC,mBAAmB;cAACC,WAAW,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eACNrE,OAAA;YAAK2D,KAAK,EAAEpD,MAAM,CAACyC,YAAa;YAAAY,QAAA,EAAExD,CAAC,CAAC,oCAAoC;UAAC;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAChFrE,OAAA;YAAK2D,KAAK,EAAEpD,MAAM,CAAC0C,kBAAmB;YAAAW,QAAA,EACnCxD,CAAC,CAAC,0CAA0C;UAAC;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAEAhE,aAAa,KAAK,OAAO,iBACxBL,OAAA;MAAK2D,KAAK,EAAEpD,MAAM,CAAC+B,OAAQ;MAAAsB,QAAA,gBACzB5D,OAAA;QAAI2D,KAAK,EAAEpD,MAAM,CAACgC,YAAa;QAAAqB,QAAA,gBAC7B5D,OAAA;UAAK2D,KAAK,EAAEpD,MAAM,CAAC8C,IAAK;UAACQ,OAAO,EAAC,WAAW;UAACC,IAAI,EAAC,MAAM;UAACC,MAAM,EAAC,cAAc;UAAAH,QAAA,eAC5E5D,OAAA;YAAMgE,CAAC,EAAC,mDAAmD;YAACC,WAAW,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E,CAAC,EACjBjE,CAAC,CAAC,gBAAgB,CAAC,EAAC,wBACX;MAAA;QAAA8D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELrE,OAAA;QAAK2D,KAAK,EAAE;UAAE5C,YAAY,EAAE;QAAO,CAAE;QAAA6C,QAAA,gBACnC5D,OAAA;UAAK2D,KAAK,EAAE;YAAE,GAAGpD,MAAM,CAACqC,WAAW;YAAE7B,YAAY,EAAE;UAAO,CAAE;UAAA6C,QAAA,gBAC1D5D,OAAA;YAAK2D,KAAK,EAAE;cAAEnC,OAAO,EAAE,MAAM;cAAEiB,UAAU,EAAE,QAAQ;cAAEX,GAAG,EAAE,MAAM;cAAEf,YAAY,EAAE;YAAO,CAAE;YAAA6C,QAAA,gBACvF5D,OAAA;cAAK2D,KAAK,EAAE;gBACVlC,eAAe,EAAEvB,QAAQ,GAAG,SAAS,GAAG,SAAS;gBACjDU,KAAK,EAAE,OAAO;gBACdc,YAAY,EAAE,KAAK;gBACnBoB,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,MAAM;gBACdvB,OAAO,EAAE,MAAM;gBACfiB,UAAU,EAAE,QAAQ;gBACpBb,cAAc,EAAE,QAAQ;gBACxBR,UAAU,EAAE;cACd,CAAE;cAAAwC,QAAA,EAAC;YAAC;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACVrE,OAAA;cAAI2D,KAAK,EAAEpD,MAAM,CAACyC,YAAa;cAAAY,QAAA,EAAExD,CAAC,CAAC,uBAAuB;YAAC;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC,eACNrE,OAAA;YAAG2D,KAAK,EAAEpD,MAAM,CAAC0C,kBAAmB;YAAAW,QAAA,EACjCxD,CAAC,CAAC,6BAA6B;UAAC;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENrE,OAAA;UAAK2D,KAAK,EAAE;YAAE,GAAGpD,MAAM,CAACqC,WAAW;YAAE7B,YAAY,EAAE;UAAO,CAAE;UAAA6C,QAAA,gBAC1D5D,OAAA;YAAK2D,KAAK,EAAE;cAAEnC,OAAO,EAAE,MAAM;cAAEiB,UAAU,EAAE,QAAQ;cAAEX,GAAG,EAAE,MAAM;cAAEf,YAAY,EAAE;YAAO,CAAE;YAAA6C,QAAA,gBACvF5D,OAAA;cAAK2D,KAAK,EAAE;gBACVlC,eAAe,EAAEvB,QAAQ,GAAG,SAAS,GAAG,SAAS;gBACjDU,KAAK,EAAE,OAAO;gBACdc,YAAY,EAAE,KAAK;gBACnBoB,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,MAAM;gBACdvB,OAAO,EAAE,MAAM;gBACfiB,UAAU,EAAE,QAAQ;gBACpBb,cAAc,EAAE,QAAQ;gBACxBR,UAAU,EAAE;cACd,CAAE;cAAAwC,QAAA,EAAC;YAAC;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACVrE,OAAA;cAAI2D,KAAK,EAAEpD,MAAM,CAACyC,YAAa;cAAAY,QAAA,EAAExD,CAAC,CAAC,uBAAuB;YAAC;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC,eACNrE,OAAA;YAAG2D,KAAK,EAAEpD,MAAM,CAAC0C,kBAAmB;YAAAW,QAAA,EACjCxD,CAAC,CAAC,6BAA6B;UAAC;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENrE,OAAA;UAAK2D,KAAK,EAAE;YAAE,GAAGpD,MAAM,CAACqC,WAAW;YAAE7B,YAAY,EAAE;UAAO,CAAE;UAAA6C,QAAA,gBAC1D5D,OAAA;YAAK2D,KAAK,EAAE;cAAEnC,OAAO,EAAE,MAAM;cAAEiB,UAAU,EAAE,QAAQ;cAAEX,GAAG,EAAE,MAAM;cAAEf,YAAY,EAAE;YAAO,CAAE;YAAA6C,QAAA,gBACvF5D,OAAA;cAAK2D,KAAK,EAAE;gBACVlC,eAAe,EAAEvB,QAAQ,GAAG,SAAS,GAAG,SAAS;gBACjDU,KAAK,EAAE,OAAO;gBACdc,YAAY,EAAE,KAAK;gBACnBoB,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,MAAM;gBACdvB,OAAO,EAAE,MAAM;gBACfiB,UAAU,EAAE,QAAQ;gBACpBb,cAAc,EAAE,QAAQ;gBACxBR,UAAU,EAAE;cACd,CAAE;cAAAwC,QAAA,EAAC;YAAC;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACVrE,OAAA;cAAI2D,KAAK,EAAEpD,MAAM,CAACyC,YAAa;cAAAY,QAAA,EAAExD,CAAC,CAAC,uBAAuB;YAAC;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC,eACNrE,OAAA;YAAG2D,KAAK,EAAEpD,MAAM,CAAC0C,kBAAmB;YAAAW,QAAA,EACjCxD,CAAC,CAAC,6BAA6B;UAAC;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENrE,OAAA;UAAK2D,KAAK,EAAE;YAAE,GAAGpD,MAAM,CAACqC,WAAW;YAAE7B,YAAY,EAAE;UAAO,CAAE;UAAA6C,QAAA,gBAC1D5D,OAAA;YAAK2D,KAAK,EAAE;cAAEnC,OAAO,EAAE,MAAM;cAAEiB,UAAU,EAAE,QAAQ;cAAEX,GAAG,EAAE,MAAM;cAAEf,YAAY,EAAE;YAAO,CAAE;YAAA6C,QAAA,gBACvF5D,OAAA;cAAK2D,KAAK,EAAE;gBACVlC,eAAe,EAAEvB,QAAQ,GAAG,SAAS,GAAG,SAAS;gBACjDU,KAAK,EAAE,OAAO;gBACdc,YAAY,EAAE,KAAK;gBACnBoB,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,MAAM;gBACdvB,OAAO,EAAE,MAAM;gBACfiB,UAAU,EAAE,QAAQ;gBACpBb,cAAc,EAAE,QAAQ;gBACxBR,UAAU,EAAE;cACd,CAAE;cAAAwC,QAAA,EAAC;YAAC;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACVrE,OAAA;cAAI2D,KAAK,EAAEpD,MAAM,CAACyC,YAAa;cAAAY,QAAA,EAAExD,CAAC,CAAC,uBAAuB;YAAC;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC,eACNrE,OAAA;YAAG2D,KAAK,EAAEpD,MAAM,CAAC0C,kBAAmB;YAAAW,QAAA,EACjCxD,CAAC,CAAC,6BAA6B;UAAC;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAEAhE,aAAa,KAAK,MAAM,iBACvBL,OAAA;MAAK2D,KAAK,EAAEpD,MAAM,CAAC+B,OAAQ;MAAAsB,QAAA,gBACzB5D,OAAA;QAAI2D,KAAK,EAAEpD,MAAM,CAACgC,YAAa;QAAAqB,QAAA,gBAC7B5D,OAAA;UAAK2D,KAAK,EAAEpD,MAAM,CAAC8C,IAAK;UAACQ,OAAO,EAAC,WAAW;UAACC,IAAI,EAAC,MAAM;UAACC,MAAM,EAAC,cAAc;UAAAH,QAAA,gBAC5E5D,OAAA;YAAMgE,CAAC,EAAC,4BAA4B;YAACC,WAAW,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC,eACtDrE,OAAA;YAAMgE,CAAC,EAAC,mBAAmB;YAACC,WAAW,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC,eAC7CrE,OAAA;YAAMgE,CAAC,EAAC,mBAAmB;YAACC,WAAW,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,EACjBjE,CAAC,CAAC,eAAe,CAAC;MAAA;QAAA8D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAELrE,OAAA;QAAK2D,KAAK,EAAEpD,MAAM,CAAC4C,SAAU;QAAAS,QAAA,gBAC3B5D,OAAA;UAAK2D,KAAK,EAAEpD,MAAM,CAAC6C,QAAS;UAAAQ,QAAA,gBAC1B5D,OAAA;YAAK2D,KAAK,EAAEpD,MAAM,CAAC8C,IAAK;YAACQ,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAAAH,QAAA,eAC5E5D,OAAA;cAAMgE,CAAC,EAAC,4BAA4B;cAACC,WAAW,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eACNrE,OAAA;YAAA4D,QAAA,gBACE5D,OAAA;cAAA4D,QAAA,EAASxD,CAAC,CAAC,yBAAyB;YAAC;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,eAC/CrE,OAAA;cAAG2D,KAAK,EAAE;gBAAEjD,MAAM,EAAE,WAAW;gBAAES,QAAQ,EAAE,QAAQ;gBAAEG,OAAO,EAAE;cAAI,CAAE;cAAAsC,QAAA,EACjExD,CAAC,CAAC,+BAA+B;YAAC;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENrE,OAAA;UAAK2D,KAAK,EAAEpD,MAAM,CAAC6C,QAAS;UAAAQ,QAAA,gBAC1B5D,OAAA;YAAK2D,KAAK,EAAEpD,MAAM,CAAC8C,IAAK;YAACQ,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAAAH,QAAA,eAC5E5D,OAAA;cAAMgE,CAAC,EAAC,0iBAA0iB;cAACC,WAAW,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjkB,CAAC,eACNrE,OAAA;YAAA4D,QAAA,gBACE5D,OAAA;cAAA4D,QAAA,EAASxD,CAAC,CAAC,wBAAwB;YAAC;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,eAC9CrE,OAAA;cAAG2D,KAAK,EAAE;gBAAEjD,MAAM,EAAE,WAAW;gBAAES,QAAQ,EAAE,QAAQ;gBAAEG,OAAO,EAAE;cAAI,CAAE;cAAAsC,QAAA,EACjExD,CAAC,CAAC,8BAA8B;YAAC;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENrE,OAAA;UAAK2D,KAAK,EAAEpD,MAAM,CAAC6C,QAAS;UAAAQ,QAAA,gBAC1B5D,OAAA;YAAK2D,KAAK,EAAEpD,MAAM,CAAC8C,IAAK;YAACQ,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAAAH,QAAA,eAC5E5D,OAAA;cAAMgE,CAAC,EAAC,oIAAoI;cAACC,WAAW,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3J,CAAC,eACNrE,OAAA;YAAA4D,QAAA,gBACE5D,OAAA;cAAA4D,QAAA,EAASxD,CAAC,CAAC,mBAAmB;YAAC;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,eACzCrE,OAAA;cAAG2D,KAAK,EAAE;gBAAEjD,MAAM,EAAE,WAAW;gBAAES,QAAQ,EAAE,QAAQ;gBAAEG,OAAO,EAAE;cAAI,CAAE;cAAAsC,QAAA,EACjExD,CAAC,CAAC,yBAAyB;YAAC;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENrE,OAAA;UAAK2D,KAAK,EAAEpD,MAAM,CAAC6C,QAAS;UAAAQ,QAAA,gBAC1B5D,OAAA;YAAK2D,KAAK,EAAEpD,MAAM,CAAC8C,IAAK;YAACQ,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAAAH,QAAA,eAC5E5D,OAAA;cAAMgE,CAAC,EAAC,2TAA2T;cAACC,WAAW,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClV,CAAC,eACNrE,OAAA;YAAA4D,QAAA,gBACE5D,OAAA;cAAA4D,QAAA,EAASxD,CAAC,CAAC,yBAAyB;YAAC;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,eAC/CrE,OAAA;cAAG2D,KAAK,EAAE;gBAAEjD,MAAM,EAAE,WAAW;gBAAES,QAAQ,EAAE,QAAQ;gBAAEG,OAAO,EAAE;cAAI,CAAE;cAAAsC,QAAA,EACjExD,CAAC,CAAC,+BAA+B;YAAC;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENrE,OAAA;UAAK2D,KAAK,EAAEpD,MAAM,CAAC6C,QAAS;UAAAQ,QAAA,gBAC1B5D,OAAA;YAAK2D,KAAK,EAAEpD,MAAM,CAAC8C,IAAK;YAACQ,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAAAH,QAAA,eAC5E5D,OAAA;cAAMgE,CAAC,EAAC,qPAAqP;cAACC,WAAW,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5Q,CAAC,eACNrE,OAAA;YAAA4D,QAAA,gBACE5D,OAAA;cAAA4D,QAAA,EAASxD,CAAC,CAAC,8BAA8B;YAAC;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,eACpDrE,OAAA;cAAG2D,KAAK,EAAE;gBAAEjD,MAAM,EAAE,WAAW;gBAAES,QAAQ,EAAE,QAAQ;gBAAEG,OAAO,EAAE;cAAI,CAAE;cAAAsC,QAAA,EACjExD,CAAC,CAAC,oCAAoC;YAAC;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAEAhE,aAAa,KAAK,KAAK,iBACtBL,OAAA;MAAK2D,KAAK,EAAEpD,MAAM,CAAC+B,OAAQ;MAAAsB,QAAA,gBACzB5D,OAAA;QAAI2D,KAAK,EAAEpD,MAAM,CAACgC,YAAa;QAAAqB,QAAA,gBAC7B5D,OAAA;UAAK2D,KAAK,EAAEpD,MAAM,CAAC8C,IAAK;UAACQ,OAAO,EAAC,WAAW;UAACC,IAAI,EAAC,MAAM;UAACC,MAAM,EAAC,cAAc;UAAAH,QAAA,eAC5E5D,OAAA;YAAMgE,CAAC,EAAC,iCAAiC;YAACC,WAAW,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC,EACjBjE,CAAC,CAAC,cAAc,CAAC;MAAA;QAAA8D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAELrE,OAAA;QAAK2D,KAAK,EAAE;UAAE5C,YAAY,EAAE;QAAO,CAAE;QAAA6C,QAAA,gBACnC5D,OAAA;UAAI2D,KAAK,EAAE;YAAE/C,KAAK,EAAEV,QAAQ,GAAG,SAAS,GAAG,SAAS;YAAEa,YAAY,EAAE;UAAO,CAAE;UAAA6C,QAAA,EACxFxD,CAAC,CAAC,wBAAwB;QAAC;UAAA8D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eAELrE,OAAA;UAAK2D,KAAK,EAAE;YAAE,GAAGpD,MAAM,CAACqC,WAAW;YAAE7B,YAAY,EAAE;UAAO,CAAE;UAAA6C,QAAA,gBAC1D5D,OAAA;YAAK2D,KAAK,EAAE;cAAEnC,OAAO,EAAE,MAAM;cAAEiB,UAAU,EAAE,QAAQ;cAAEX,GAAG,EAAE,MAAM;cAAEf,YAAY,EAAE;YAAO,CAAE;YAAA6C,QAAA,gBACvF5D,OAAA;cAAM2D,KAAK,EAAE;gBACXlC,eAAe,EAAE,SAAS;gBAC1Bb,KAAK,EAAE,OAAO;gBACdD,OAAO,EAAE,SAAS;gBAClBe,YAAY,EAAE,KAAK;gBACnBP,QAAQ,EAAE,QAAQ;gBAClBC,UAAU,EAAE;cACd,CAAE;cAAAwC,QAAA,EAAC;YAAI;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACdrE,OAAA;cAAM2D,KAAK,EAAE;gBACXlC,eAAe,EAAEvB,QAAQ,GAAG,SAAS,GAAG,SAAS;gBACjDS,OAAO,EAAE,SAAS;gBAClBe,YAAY,EAAE,KAAK;gBACnBkD,UAAU,EAAE;cACd,CAAE;cAAAhB,QAAA,EAAC;YAAQ;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eACNrE,OAAA;YAAG2D,KAAK,EAAEpD,MAAM,CAAC0C,kBAAmB;YAAAW,QAAA,EACjCxD,CAAC,CAAC,4BAA4B;UAAC;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENrE,OAAA;UAAK2D,KAAK,EAAE;YAAE,GAAGpD,MAAM,CAACqC,WAAW;YAAE7B,YAAY,EAAE;UAAO,CAAE;UAAA6C,QAAA,gBAC1D5D,OAAA;YAAK2D,KAAK,EAAE;cAAEnC,OAAO,EAAE,MAAM;cAAEiB,UAAU,EAAE,QAAQ;cAAEX,GAAG,EAAE,MAAM;cAAEf,YAAY,EAAE;YAAO,CAAE;YAAA6C,QAAA,gBACvF5D,OAAA;cAAM2D,KAAK,EAAE;gBACXlC,eAAe,EAAE,SAAS;gBAC1Bb,KAAK,EAAE,OAAO;gBACdD,OAAO,EAAE,SAAS;gBAClBe,YAAY,EAAE,KAAK;gBACnBP,QAAQ,EAAE,QAAQ;gBAClBC,UAAU,EAAE;cACd,CAAE;cAAAwC,QAAA,EAAC;YAAG;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACbrE,OAAA;cAAM2D,KAAK,EAAE;gBACXlC,eAAe,EAAEvB,QAAQ,GAAG,SAAS,GAAG,SAAS;gBACjDS,OAAO,EAAE,SAAS;gBAClBe,YAAY,EAAE,KAAK;gBACnBkD,UAAU,EAAE;cACd,CAAE;cAAAhB,QAAA,EAAC;YAAoB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eACNrE,OAAA;YAAG2D,KAAK,EAAEpD,MAAM,CAAC0C,kBAAmB;YAAAW,QAAA,EACjCxD,CAAC,CAAC,+BAA+B;UAAC;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENrE,OAAA;UAAK2D,KAAK,EAAE;YAAE,GAAGpD,MAAM,CAACqC,WAAW;YAAE7B,YAAY,EAAE;UAAO,CAAE;UAAA6C,QAAA,gBAC1D5D,OAAA;YAAK2D,KAAK,EAAE;cAAEnC,OAAO,EAAE,MAAM;cAAEiB,UAAU,EAAE,QAAQ;cAAEX,GAAG,EAAE,MAAM;cAAEf,YAAY,EAAE;YAAO,CAAE;YAAA6C,QAAA,gBACvF5D,OAAA;cAAM2D,KAAK,EAAE;gBACXlC,eAAe,EAAE,SAAS;gBAC1Bb,KAAK,EAAE,OAAO;gBACdD,OAAO,EAAE,SAAS;gBAClBe,YAAY,EAAE,KAAK;gBACnBP,QAAQ,EAAE,QAAQ;gBAClBC,UAAU,EAAE;cACd,CAAE;cAAAwC,QAAA,EAAC;YAAG;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACbrE,OAAA;cAAM2D,KAAK,EAAE;gBACXlC,eAAe,EAAEvB,QAAQ,GAAG,SAAS,GAAG,SAAS;gBACjDS,OAAO,EAAE,SAAS;gBAClBe,YAAY,EAAE,KAAK;gBACnBkD,UAAU,EAAE;cACd,CAAE;cAAAhB,QAAA,EAAC;YAAsB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACNrE,OAAA;YAAG2D,KAAK,EAAEpD,MAAM,CAAC0C,kBAAmB;YAAAW,QAAA,EACjCxD,CAAC,CAAC,6BAA6B;UAAC;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAEAhE,aAAa,KAAK,KAAK,iBACtBL,OAAA;MAAK2D,KAAK,EAAEpD,MAAM,CAAC+B,OAAQ;MAAAsB,QAAA,gBACzB5D,OAAA;QAAI2D,KAAK,EAAEpD,MAAM,CAACgC,YAAa;QAAAqB,QAAA,gBAC7B5D,OAAA;UAAK2D,KAAK,EAAEpD,MAAM,CAAC8C,IAAK;UAACQ,OAAO,EAAC,WAAW;UAACC,IAAI,EAAC,MAAM;UAACC,MAAM,EAAC,cAAc;UAAAH,QAAA,eAC5E5D,OAAA;YAAMgE,CAAC,EAAC,gWAAgW;YAACC,WAAW,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvX,CAAC,EACjBjE,CAAC,CAAC,cAAc,CAAC;MAAA;QAAA8D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAELrE,OAAA;QAAK2D,KAAK,EAAE;UAAE5C,YAAY,EAAE;QAAO,CAAE;QAAA6C,QAAA,gBACnC5D,OAAA;UAAK2D,KAAK,EAAE;YAAE,GAAGpD,MAAM,CAACqC,WAAW;YAAE7B,YAAY,EAAE;UAAO,CAAE;UAAA6C,QAAA,gBAC1D5D,OAAA;YAAI2D,KAAK,EAAE;cAAE,GAAGpD,MAAM,CAACyC,YAAY;cAAEjC,YAAY,EAAE;YAAO,CAAE;YAAA6C,QAAA,EACzDxD,CAAC,CAAC,0BAA0B;UAAC;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACLrE,OAAA;YAAG2D,KAAK,EAAEpD,MAAM,CAAC0C,kBAAmB;YAAAW,QAAA,EACjCxD,CAAC,CAAC,wBAAwB;UAAC;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENrE,OAAA;UAAK2D,KAAK,EAAE;YAAE,GAAGpD,MAAM,CAACqC,WAAW;YAAE7B,YAAY,EAAE;UAAO,CAAE;UAAA6C,QAAA,gBAC1D5D,OAAA;YAAI2D,KAAK,EAAE;cAAE,GAAGpD,MAAM,CAACyC,YAAY;cAAEjC,YAAY,EAAE;YAAO,CAAE;YAAA6C,QAAA,EACzDxD,CAAC,CAAC,0BAA0B;UAAC;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACLrE,OAAA;YAAG2D,KAAK,EAAEpD,MAAM,CAAC0C,kBAAmB;YAAAW,QAAA,EACjCxD,CAAC,CAAC,wBAAwB;UAAC;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENrE,OAAA;UAAK2D,KAAK,EAAE;YAAE,GAAGpD,MAAM,CAACqC,WAAW;YAAE7B,YAAY,EAAE;UAAO,CAAE;UAAA6C,QAAA,gBAC1D5D,OAAA;YAAI2D,KAAK,EAAE;cAAE,GAAGpD,MAAM,CAACyC,YAAY;cAAEjC,YAAY,EAAE;YAAO,CAAE;YAAA6C,QAAA,EACzDxD,CAAC,CAAC,iCAAiC;UAAC;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eACLrE,OAAA;YAAG2D,KAAK,EAAEpD,MAAM,CAAC0C,kBAAmB;YAAAW,QAAA,EACjCxD,CAAC,CAAC,+BAA+B;UAAC;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENrE,OAAA;UAAK2D,KAAK,EAAE;YAAE,GAAGpD,MAAM,CAACqC,WAAW;YAAE7B,YAAY,EAAE;UAAO,CAAE;UAAA6C,QAAA,gBAC1D5D,OAAA;YAAI2D,KAAK,EAAE;cAAE,GAAGpD,MAAM,CAACyC,YAAY;cAAEjC,YAAY,EAAE;YAAO,CAAE;YAAA6C,QAAA,EACzDxD,CAAC,CAAC,2BAA2B;UAAC;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACLrE,OAAA;YAAG2D,KAAK,EAAEpD,MAAM,CAAC0C,kBAAmB;YAAAW,QAAA,EACjCxD,CAAC,CAAC,yBAAyB;UAAC;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENrE,OAAA;UAAK2D,KAAK,EAAE;YAAE,GAAGpD,MAAM,CAACqC,WAAW;YAAE7B,YAAY,EAAE;UAAO,CAAE;UAAA6C,QAAA,gBAC1D5D,OAAA;YAAI2D,KAAK,EAAE;cAAE,GAAGpD,MAAM,CAACyC,YAAY;cAAEjC,YAAY,EAAE;YAAO,CAAE;YAAA6C,QAAA,EACzDxD,CAAC,CAAC,2BAA2B;UAAC;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACLrE,OAAA;YAAG2D,KAAK,EAAEpD,MAAM,CAAC0C,kBAAmB;YAAAW,QAAA,EACjCxD,CAAC,CAAC,yBAAyB;UAAC;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENrE,OAAA;UAAK2D,KAAK,EAAE;YAAE,GAAGpD,MAAM,CAACqC,WAAW;YAAE7B,YAAY,EAAE;UAAO,CAAE;UAAA6C,QAAA,gBAC1D5D,OAAA;YAAI2D,KAAK,EAAE;cAAE,GAAGpD,MAAM,CAACyC,YAAY;cAAEjC,YAAY,EAAE;YAAO,CAAE;YAAA6C,QAAA,EACzDxD,CAAC,CAAC,wBAAwB;UAAC;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eACLrE,OAAA;YAAG2D,KAAK,EAAEpD,MAAM,CAAC0C,kBAAmB;YAAAW,QAAA,EACjCxD,CAAC,CAAC,sBAAsB;UAAC;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDrE,OAAA;MAAK2D,KAAK,EAAE;QACV7C,SAAS,EAAE,QAAQ;QACnBJ,MAAM,EAAE,QAAQ;QAChBe,eAAe,EAAEvB,QAAQ,GAAG,wBAAwB,GAAG,wBAAwB;QAC/EwB,YAAY,EAAE,MAAM;QACpBf,OAAO,EAAE,WAAW;QACpBoB,MAAM,EAAE7B,QAAQ,GAAG,kCAAkC,GAAG;MAC1D,CAAE;MAAA0D,QAAA,gBACA5D,OAAA;QAAI2D,KAAK,EAAE;UACT5C,YAAY,EAAE,MAAM;UACpBS,OAAO,EAAE,MAAM;UACfiB,UAAU,EAAE,QAAQ;UACpBb,cAAc,EAAE,QAAQ;UACxBE,GAAG,EAAE,MAAM;UACXlB,KAAK,EAAEV,QAAQ,GAAG,SAAS,GAAG,SAAS;UACvCiB,QAAQ,EAAE;QACZ,CAAE;QAAAyC,QAAA,gBACA5D,OAAA;UAAK2D,KAAK,EAAE;YAAEb,KAAK,EAAE,MAAM;YAAEC,MAAM,EAAE,MAAM;YAAEgB,MAAM,EAAE7D,QAAQ,GAAG,SAAS,GAAG;UAAU,CAAE;UAAC2D,OAAO,EAAC,WAAW;UAACC,IAAI,EAAC,MAAM;UAAAF,QAAA,gBACtH5D,OAAA;YAAMgE,CAAC,EAAC,oGAAoG;YAACC,WAAW,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC,eAC9HrE,OAAA;YAAMgE,CAAC,EAAC,wHAAwH;YAACC,WAAW,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/I,CAAC,EACfjE,CAAC,CAAC,mBAAmB,CAAC;MAAA;QAAA8D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC,eACLrE,OAAA;QAAG2D,KAAK,EAAE;UACR5C,YAAY,EAAE,MAAM;UACpBH,KAAK,EAAEV,QAAQ,GAAG,SAAS,GAAG,SAAS;UACvCiB,QAAQ,EAAE,QAAQ;UAClB+B,UAAU,EAAE;QACd,CAAE;QAAAU,QAAA,EACTxD,CAAC,CAAC,yBAAyB;MAAC;QAAA8D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClE,EAAA,CAtpBIF,aAA2C;EAAA,QACjCH,WAAW;AAAA;AAAA+E,EAAA,GADrB5E,aAA2C;AAwpBjD,eAAeA,aAAa;AAAC,IAAA4E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}