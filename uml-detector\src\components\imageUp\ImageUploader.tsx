import React, { useState, useEffect, useRef } from "react";
import Loading<PERSON>pinner from "./LoadingSpinner";
import AnalysisResults from "./AnalysisResults";
import { useHistory } from '../../context/HistoryContext';
import { useLanguage } from '../../context/LanguageContext';

// Utilitaires intégrés pour la gestion des fichiers
const fileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = () => {
      if (typeof reader.result === 'string') {
        resolve(reader.result);
      } else {
        reject(new Error('Erreur lors de la conversion en Base64'));
      }
    };
    
    reader.onerror = () => {
      reject(new Error('Erreur lors de la lecture du fichier'));
    };
    
    reader.readAsDataURL(file);
  });
};

// Fonction pour redimensionner une image et créer une miniature
const createThumbnail = (base64Url: string, maxWidth = 200, maxHeight = 200, quality = 0.7): Promise<string> => {
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = () => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d')!;
      
      // Calculer les nouvelles dimensions en conservant le ratio
      let { width, height } = img;
      if (width > height) {
        if (width > maxWidth) {
          height = (height * maxWidth) / width;
          width = maxWidth;
        }
      } else {
        if (height > maxHeight) {
          width = (width * maxHeight) / height;
          height = maxHeight;
        }
      }
      
      canvas.width = width;
      canvas.height = height;
      
      // Dessiner l'image redimensionnée
      ctx.drawImage(img, 0, 0, width, height);
      
      // Convertir en Base64 avec compression
      const thumbnailUrl = canvas.toDataURL('image/jpeg', quality);
      resolve(thumbnailUrl);
    };
    img.src = base64Url;
  });
};

const isBase64Url = (url: string): boolean => {
  return url.startsWith('data:');
};

interface ImageUploaderProps {
  onImageUpload?: (file: File) => void;
  onAnalysisComplete?: (imageUrl: string, text: string, textFileUrl: string | null) => void;
  darkMode: boolean;
  savedImageUrl?: string | null;
  savedExtractedText?: string;
  savedTextUrl?: string | null;
  onViewAnnotatedImage?: () => void;
  onNavigateToUMLExtractor?: () => void;
  onUpdateExtractedText?: (newText: string) => void;
}

const ImageUploader: React.FC<ImageUploaderProps> = ({ 
  darkMode, 
  onAnalysisComplete, 
  savedImageUrl,
  savedExtractedText,
  savedTextUrl,
  onViewAnnotatedImage,
  onNavigateToUMLExtractor,
  onUpdateExtractedText
}) => {
  const [imageUrl, setImageUrl] = useState<string | null>(savedImageUrl || null);
  const [textUrl, setTextUrl] = useState<string | null>(savedTextUrl || null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [extractedText, setExtractedText] = useState<string>(savedExtractedText || "");
  const [analysisProgress, setAnalysisProgress] = useState<string>("");
  const [fileName, setFileName] = useState<string>("");
  const [isConverting, setIsConverting] = useState(false);

  const { t } = useLanguage();

  const { addHistoryItem } = useHistory();

  // Initialiser le texte de progression avec la traduction
  useEffect(() => {
    if (!analysisProgress) {
      setAnalysisProgress(t('imageUploader.progress.waiting'));
    }
  }, [t, analysisProgress]);
  
  // Référence pour éviter les ajouts multiples à l'historique
  const historyAddedRef = useRef<boolean>(false);
  const currentAnalysisId = useRef<string>("");
  
  // Stocker l'image originale et la miniature séparément
  const currentImageBase64 = useRef<string | null>(null);
  const currentThumbnail = useRef<string | null>(null);

  // Réinitialiser l'état d'historique lorsqu'une nouvelle analyse commence
  const startNewAnalysis = () => {
    historyAddedRef.current = false;
    currentAnalysisId.current = Date.now().toString();
    console.log(`🔄 Nouvelle analyse démarrée: ${currentAnalysisId.current}`);
  };

  // Mettre à jour les états locaux lorsque les props changent
  useEffect(() => {
    if (savedImageUrl !== undefined) {
      setImageUrl(savedImageUrl);
      if (savedImageUrl && isBase64Url(savedImageUrl)) {
        currentImageBase64.current = savedImageUrl;
      }
    }
    if (savedExtractedText !== undefined) {
      setExtractedText(savedExtractedText);
    }
    if (savedTextUrl !== undefined) {
      setTextUrl(savedTextUrl);
    }
  }, [savedImageUrl, savedExtractedText, savedTextUrl]);

  // Fonction pour traiter un fichier : conversion en Base64 + création de miniature
  const processFileToBase64 = async (file: File): Promise<{ fullImage: string; thumbnail: string }> => {
    setIsConverting(true);
    setAnalysisProgress(t('imageUploader.progress.converting'));

    try {
      const base64Url = await fileToBase64(file);
      const thumbnail = await createThumbnail(base64Url);
      
      currentImageBase64.current = base64Url;
      currentThumbnail.current = thumbnail;
      
      console.log(`📊 Taille image originale: ${Math.round(base64Url.length / 1024)} KB`);
      console.log(`🖼️ Taille miniature: ${Math.round(thumbnail.length / 1024)} KB`);
      
      return { fullImage: base64Url, thumbnail };
    } catch (error) {
      console.error("Erreur lors de la conversion en Base64:", error);
      throw error;
    } finally {
      setIsConverting(false);
    }
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      await processImage(file);
    }
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      processImage(e.dataTransfer.files[0]);
    }
  };

  const processImage = async (file: File) => {
    // Commencer une nouvelle analyse
    startNewAnalysis();
    
    setFileName(file.name);
    setExtractedText("");
    setTextUrl(null);
    
    try {
      // Convertir et créer la miniature
      const { fullImage } = await processFileToBase64(file);
      setImageUrl(fullImage);
      
      // Analyser l'image
      analyzeImage(file);
    } catch (error) {
      console.error("Erreur lors du traitement du fichier:", error);
      alert(t('imageUploader.error.processing'));
    }
  };

  // Fonction pour ajouter à l'historique une seule fois avec gestion optimisée
  const addToHistoryOnce = async (imageUrl: string, text: string, textUrl: string) => {
    if (historyAddedRef.current) {
      console.log(`⚠️ Tentative d'ajout multiple évitée pour l'analyse: ${currentAnalysisId.current}`);
      return;
    }

    try {
      // Marquer immédiatement pour éviter les doublons
      historyAddedRef.current = true;
      
      // Utiliser la miniature pour l'historique (beaucoup plus légère)
      const thumbnail = currentThumbnail.current;
      if (!thumbnail) {
        throw new Error(t('imageUploader.error.thumbnail'));
      }
      
      const title = fileName 
        ? `${fileName} - ${new Date().toLocaleDateString('fr-FR')}` 
        : `Diagramme UML - ${new Date().toLocaleDateString('fr-FR')}`;
        
      const annotatedImageUrl = fileName
        ? `http://127.0.0.1:8000/annotated_image_${encodeURIComponent(fileName)}`
        : "http://127.0.0.1:8000/annotated_image.jpg";
      
      console.log(`💾 Ajout à l'historique pour l'analyse: ${currentAnalysisId.current}`);
        
      await addHistoryItem({
        title: title,
        originalImageUrl: annotatedImageUrl, // URL du serveur au lieu de Base64
        annotatedImageUrl: annotatedImageUrl,
        extractedText: text,
        thumbnail: thumbnail, // Miniature optimisée
        userId: ''
      });
      
      console.log(`✅ Ajouté à l'historique avec succès: ${currentAnalysisId.current}`);
    } catch (error) {
      console.error("Erreur lors de l'ajout à l'historique:", error);
      // Réinitialiser en cas d'erreur pour permettre une nouvelle tentative
      historyAddedRef.current = false;
    }
  };

  // Fonction pour récupérer le texte extrait
  useEffect(() => {
    if (!textUrl || !imageUrl || historyAddedRef.current) return;

    let isMounted = true;

    const fetchResults = async () => {
      setAnalysisProgress(t('imageUploader.progress.fetching'));

      try {
        const response = await fetch(textUrl);
        if (!response.ok) {
          throw new Error(t('imageUploader.error.fetchText'));
        }
        
        const text = await response.text();
        
        if (!isMounted) return;
        
        setExtractedText(text);
        setAnalysisProgress(t('imageUploader.progress.completed'));

        if (onAnalysisComplete) {
          onAnalysisComplete(imageUrl, text, textUrl);
        }
        
        // Ajouter à l'historique une seule fois
        await addToHistoryOnce(imageUrl, text, textUrl);
      } catch (error) {
        if (isMounted) {
          console.error("Erreur lors de la récupération du texte :", error);
          setAnalysisProgress(t('imageUploader.progress.fetchError'));
        }
      }
    };

    fetchResults();

    return () => {
      isMounted = false;
    };
  }, [textUrl, imageUrl, onAnalysisComplete]);

  const analyzeImage = async (file: File) => {
    setIsAnalyzing(true);
    setAnalysisProgress(t('imageUploader.progress.detecting'));

    const formData = new FormData();
    formData.append("file", file);

    try {
      const timeoutId = setTimeout(() => {
        if (isAnalyzing) {
          setAnalysisProgress(t('imageUploader.progress.analyzing'));
        }
      }, 3000);
      
      const response = await fetch("http://127.0.0.1:8000/detect/", {
        method: "POST",
        body: formData,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        try {
          const errorData = await response.json();
          throw new Error(`Erreur serveur: ${errorData.detail || response.status}`);
        } catch (jsonError) {
          throw new Error(`Erreur HTTP: ${response.status}`);
        }
      }

      const textFileUrl = "http://127.0.0.1:8000/resultats_classes.txt";
      setTextUrl(textFileUrl);
      
    } catch (error) {
      console.error("Erreur de connexion ou analyse :", error);
      setAnalysisProgress(t('imageUploader.progress.error'));
      alert(`${t('imageUploader.error.connection')}: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const resetAnalysis = () => {
    // Réinitialiser tous les états
    setImageUrl(null);
    setTextUrl(null);
    setExtractedText("");
    setFileName("");
    setAnalysisProgress(t('imageUploader.progress.waiting'));

    // Réinitialiser l'état d'historique et les références
    historyAddedRef.current = false;
    currentAnalysisId.current = "";
    currentImageBase64.current = null;
    currentThumbnail.current = null;
    
    console.log("🔄 Analyse réinitialisée");
    
    if (onAnalysisComplete) {
      onAnalysisComplete("", "", null);
    }
  };

  const handleUpdateExtractedText = (newText: string) => {
    console.log("Mise à jour du texte extrait dans ImageUploader.tsx");
    console.log("Nouveau texte:", newText.substring(0, 100) + "...");
    
    // Mettre à jour l'état local
    setExtractedText(newText);
    
    // Propager la mise à jour au parent
    if (onUpdateExtractedText) {
      onUpdateExtractedText(newText);
    }
    
    // Forcer un re-rendu si nécessaire
    setTimeout(() => {
      console.log("Forcer un re-rendu après mise à jour dans ImageUploader");
      setExtractedText(prevText => {
        if (prevText === newText) {
          // Si le texte n'a pas changé, forcer un re-rendu en créant une nouvelle référence
          return newText + " "; // Ajouter un espace pour forcer un changement
        }
        return newText;
      });
    }, 100);
  };

  const getStyles = () => ({
    container: {
      maxWidth: "800px",
      margin: "0 auto",
      padding: "20px",
      color: darkMode ? "#e0e0e0" : "#333"
    },
    dropZone: {
      border: `2px dashed ${darkMode ? '#4f46e5' : '#2563eb'}`,
      borderRadius: '8px',
      padding: '40px',
      textAlign: 'center' as const,
      backgroundColor: isDragging 
        ? (darkMode ? 'rgba(79, 70, 229, 0.2)' : 'rgba(37, 99, 235, 0.1)')
        : (darkMode ? '#2d2d2d' : '#f5f5f5'),
      margin: '20px 0',
      cursor: 'pointer',
      transition: 'all 0.3s ease'
    },
    fileInput: {
      display: 'none'
    },
    selectButton: {
      backgroundColor: darkMode ? '#4f46e5' : '#2563eb',
      color: 'white',
      padding: '10px 20px',
      borderRadius: '5px',
      border: 'none',
      cursor: 'pointer',
      marginTop: '10px',
      fontSize: '16px'
    },
    resultsContainer: {
      backgroundColor: darkMode ? '#333' : '#fff',
      borderRadius: '8px',
      padding: '20px',
      marginTop: '20px',
      boxShadow: darkMode ? '0 2px 10px rgba(0,0,0,0.5)' : '0 2px 10px rgba(0,0,0,0.1)'
    }
  });

  const styles = getStyles();

  return (
    <div style={styles.container}>
      {!imageUrl ? (
        <div
          onDragEnter={() => setIsDragging(true)}
          onDragOver={(e) => e.preventDefault()}
          onDragLeave={() => setIsDragging(false)}
          onDrop={handleDrop}
          onClick={() => document.getElementById('file-input')?.click()}
          style={styles.dropZone}
        >
          <p>{t('imageUploader.dropZone')}</p>
          <input
            id="file-input"
            type="file"
            accept="image/*"
            onChange={handleFileChange}
            style={styles.fileInput}
          />
          <button style={styles.selectButton}>{t('imageUploader.selectFile')}</button>
        </div>
      ) : (
        <div style={styles.resultsContainer}>
          {(isAnalyzing || isConverting) ? (
            <LoadingSpinner darkMode={darkMode} text={analysisProgress} />
          ) : (
            <AnalysisResults 
              darkMode={darkMode}
              imageUrl={imageUrl}
              extractedText={extractedText}
              onViewAnnotatedImage={onViewAnnotatedImage}
              onNavigateToUMLExtractor={onNavigateToUMLExtractor}
              resetAnalysis={resetAnalysis}
              onUpdateExtractedText={handleUpdateExtractedText}
            />
          )}
        </div>
      )}
    </div>
  );
};

export default ImageUploader;
