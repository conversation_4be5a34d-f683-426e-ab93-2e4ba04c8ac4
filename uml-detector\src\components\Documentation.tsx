import React, { useState } from "react";
import { useLanguage } from "../context/LanguageContext";

interface DocumentationProps {
  darkMode: boolean;
}

const Documentation: React.FC<DocumentationProps> = ({ darkMode }) => {
  const { t } = useLanguage();
  const [activeSection, setActiveSection] = useState<string>("overview");

  const styles = {
    container: {
      maxWidth: "1200px",
      margin: "0 auto",
      padding: "20px",
      color: darkMode ? "#e0e0e0" : "#333"
    },
    header: {
      textAlign: "center" as const,
      marginBottom: "40px",
      borderBottom: `2px solid ${darkMode ? "#4f46e5" : "#2563eb"}`,
      paddingBottom: "30px"
    },
    title: {
      fontSize: "2.5rem",
      fontWeight: "700",
      color: darkMode ? "#4f46e5" : "#2563eb",
      marginBottom: "10px"
    },
    subtitle: {
      fontSize: "1.2rem",
      opacity: 0.8,
      marginBottom: "20px"
    },
    version: {
      display: "inline-block",
      backgroundColor: darkMode ? "#4f46e5" : "#2563eb",
      color: "white",
      padding: "6px 12px",
      borderRadius: "20px",
      fontSize: "0.9rem",
      fontWeight: "500"
    },
    navigation: {
      display: "flex",
      justifyContent: "center",
      flexWrap: "wrap" as const,
      gap: "10px",
      marginBottom: "40px",
      padding: "20px",
      backgroundColor: darkMode ? "#1e293b" : "#f8fafc",
      borderRadius: "12px",
      border: `1px solid ${darkMode ? "#334155" : "#e2e8f0"}`
    },
    navButton: (isActive: boolean) => ({
      padding: "10px 20px",
      borderRadius: "8px",
      border: "none",
      cursor: "pointer",
      fontSize: "14px",
      fontWeight: "500",
      transition: "all 0.2s ease",
      backgroundColor: isActive
        ? (darkMode ? "#4f46e5" : "#2563eb")
        : (darkMode ? "#374151" : "#e5e7eb"),
      color: isActive
        ? "white"
        : (darkMode ? "#d1d5db" : "#374151"),
      transform: isActive ? "translateY(-2px)" : "none",
      boxShadow: isActive ? "0 4px 12px rgba(79, 70, 229, 0.3)" : "none"
    }),
    section: {
      backgroundColor: darkMode ? "#1e293b" : "#ffffff",
      borderRadius: "12px",
      padding: "30px",
      marginBottom: "30px",
      boxShadow: darkMode
        ? "0 4px 20px rgba(0,0,0,0.3)"
        : "0 4px 20px rgba(0,0,0,0.08)",
      border: `1px solid ${darkMode ? "#334155" : "#e2e8f0"}`
    },
    sectionTitle: {
      color: darkMode ? "#4f46e5" : "#2563eb",
      marginTop: "0",
      marginBottom: "25px",
      display: "flex",
      alignItems: "center",
      gap: "12px",
      fontSize: "1.8rem",
      fontWeight: "600"
    },
    featureGrid: {
      display: "grid",
      gridTemplateColumns: "repeat(auto-fit, minmax(300px, 1fr))",
      gap: "20px",
      marginBottom: "30px"
    },
    featureCard: {
      backgroundColor: darkMode ? "#2d3748" : "#f7fafc",
      borderRadius: "10px",
      padding: "20px",
      border: `1px solid ${darkMode ? "#4a5568" : "#e2e8f0"}`,
      transition: "all 0.2s ease",
      cursor: "pointer"
    },
    featureIcon: {
      width: "24px",
      height: "24px",
      color: darkMode ? "#4f46e5" : "#2563eb",
      marginBottom: "15px"
    },
    featureTitle: {
      fontSize: "1.1rem",
      fontWeight: "600",
      marginBottom: "10px",
      color: darkMode ? "#e2e8f0" : "#1a202c"
    },
    featureDescription: {
      fontSize: "0.95rem",
      lineHeight: "1.6",
      opacity: 0.8
    },
    techStack: {
      display: "grid",
      gridTemplateColumns: "repeat(auto-fit, minmax(200px, 1fr))",
      gap: "15px",
      marginTop: "20px"
    },
    techItem: {
      display: "flex",
      alignItems: "center",
      gap: "10px",
      padding: "12px",
      backgroundColor: darkMode ? "#2d3748" : "#f7fafc",
      borderRadius: "8px",
      border: `1px solid ${darkMode ? "#4a5568" : "#e2e8f0"}`
    },
    icon: {
      width: "20px",
      height: "20px",
      flexShrink: 0
    }
  };

  const navigationItems = [
    { id: "overview", label: t('doc.overview'), icon: "🏠" },
    { id: "features", label: t('doc.features'), icon: "⚡" },
    { id: "guide", label: t('doc.guide'), icon: "📖" },
    { id: "tech", label: t('doc.technologies'), icon: "🔧" },
    { id: "api", label: t('doc.api'), icon: "🔌" },
    { id: "faq", label: t('doc.faq'), icon: "❓" }
  ];

  const renderOverview = () => (
    <div style={styles.section}>
      <h2 style={styles.sectionTitle}>
        <svg style={styles.icon} viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <path d="M3 7V17C3 18.1046 3.89543 19 5 19H19C20.1046 19 21 18.1046 21 17V7C21 5.89543 20.1046 5 19 5H5C3.89543 5 3 5.89543 3 7Z" strokeWidth="2"/>
          <path d="M8 9L12 13L16 9" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
{t('doc.projectOverview')}
      </h2>

      <div style={{ marginBottom: "30px" }}>
        <h3 style={{ color: darkMode ? "#e2e8f0" : "#1a202c", marginBottom: "15px" }}>
{t('doc.appTitle')}
        </h3>
        <p style={{ fontSize: "1.1rem", lineHeight: "1.7", marginBottom: "20px" }}>
          UML Class Analyzer est une application web avancée qui utilise l'intelligence artificielle
          pour analyser automatiquement les diagrammes de classes UML. Notre solution combine la
          puissance des modèles pour offrir une précision exceptionnelle dans la détection et l'extraction d'éléments UML.
        </p>

        <div style={styles.featureGrid}>
          <div style={styles.featureCard}>
            <svg style={styles.featureIcon} viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" strokeWidth="2"/>
            </svg>
            <div style={styles.featureTitle}>Détection IA Avancée</div>
            <div style={styles.featureDescription}>
              Utilise des modèles entraînés spécifiquement pour reconnaître les éléments UML avec une précision de plus de 95%.
            </div>
          </div>

          <div style={styles.featureCard}>
            <svg style={styles.featureIcon} viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M13 2L3 14H12L11 22L21 10H12L13 2Z" strokeWidth="2"/>
            </svg>
            <div style={styles.featureTitle}>Traitement Rapide</div>
            <div style={styles.featureDescription}>
              Analyse complète d'un diagramme UML en moins de 30 secondes grâce à notre infrastructure optimisée.
            </div>
          </div>

          <div style={styles.featureCard}>
            <svg style={styles.featureIcon} viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M12 2L2 7L12 12L22 7L12 2Z" strokeWidth="2"/>
              <path d="M2 17L12 22L22 17" strokeWidth="2"/>
              <path d="M2 12L12 17L22 12" strokeWidth="2"/>
            </svg>
            <div style={styles.featureTitle}>Multi-format</div>
            <div style={styles.featureDescription}>
              Support complet des formats PNG, JPG, JPEG et PDF avec export pour le moment vers Java et Mermaid.
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div style={styles.container}>
      {/* En-tête principal */}
      <div style={styles.header}>
        <h1 style={styles.title}>{t('doc.title')}</h1>
        <p style={styles.subtitle}>
          {t('doc.subtitle')}
        </p>
        <span style={styles.version}>{t('doc.version')}</span>
      </div>

      {/* Navigation */}
      <div style={styles.navigation}>
        {navigationItems.map((item) => (
          <button
            key={item.id}
            style={styles.navButton(activeSection === item.id)}
            onClick={() => setActiveSection(item.id)}
          >
            <span style={{ marginRight: "8px" }}>{item.icon}</span>
            {item.label}
          </button>
        ))}
      </div>

      {/* Contenu dynamique */}
      {activeSection === "overview" && renderOverview()}

      {activeSection === "features" && (
        <div style={styles.section}>
          <h2 style={styles.sectionTitle}>
            <svg style={styles.icon} viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M13 2L3 14H12L11 22L21 10H12L13 2Z" strokeWidth="2"/>
            </svg>
            Fonctionnalités principales
          </h2>

          <div style={styles.featureGrid}>
            <div style={styles.featureCard}>
              <svg style={styles.featureIcon} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M4 16L8.586 11.414C8.961 11.039 9.47 10.828 10 10.828C10.53 10.828 11.039 11.039 11.414 11.414L16 16M14 14L15.586 12.414C15.961 12.039 16.47 11.828 17 11.828C17.53 11.828 18.039 12.039 18.414 12.414L20 14M14 8H14.01M6 20H18C19.105 20 20 19.105 20 18V6C20 4.895 19.105 4 18 4H6C4.895 4 4 4.895 4 6V18C4 19.105 4.895 20 6 20Z" strokeWidth="2"/>
              </svg>
              <div style={styles.featureTitle}>Upload & Analyse</div>
              <div style={styles.featureDescription}>
                Téléchargez vos diagrammes UML (PNG, JPG, PDF) et obtenez une analyse complète en quelques secondes avec notre IA spécialisée.
              </div>
            </div>

            <div style={styles.featureCard}>
              <svg style={styles.featureIcon} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M9 3V5M15 3V5M9 19V21M15 19V21M5 9H3M5 15H3M21 9H19M21 15H19M7 19H17C18.105 19 19 18.105 19 17V7C19 5.895 18.105 5 17 5H7C5.895 5 5 5.895 5 7V17C5 18.105 5.895 19 7 19Z" strokeWidth="2"/>
              </svg>
              <div style={styles.featureTitle}>Détection de Relations</div>
              <div style={styles.featureDescription}>
                Identification automatique des relations UML : héritage, composition, agrégation,association avec visualisation annotée.
              </div>
            </div>

            <div style={styles.featureCard}>
              <svg style={styles.featureIcon} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M12 3H5C4.46957 3 3.96086 3.21071 3.58579 3.58579C3.21071 3.96086 3 4.46957 3 5V19C3 19.5304 3.21071 20.0391 3.58579 20.4142C3.96086 20.7893 4.46957 21 5 21H19C19.5304 21 20.0391 20.7893 20.4142 20.4142C20.7893 20.0391 21 19.5304 21 19V12" strokeWidth="2"/>
                <path d="M16 3L21 8L8 21L3 21L3 16L16 3Z" strokeWidth="2"/>
              </svg>
              <div style={styles.featureTitle}>Extraction de Texte</div>
              <div style={styles.featureDescription}>
                Extraction intelligente des classes, attributs et méthodes avec organisation
                automatique et export multi-format.
              </div>
            </div>

            <div style={styles.featureCard}>
              <svg style={styles.featureIcon} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z" strokeWidth="2"/>
                <path d="M14 2V8H20" strokeWidth="2"/>
                <path d="M16 13H8" strokeWidth="2"/>
                <path d="M16 17H8" strokeWidth="2"/>
                <path d="M10 9H8" strokeWidth="2"/>
              </svg>
              <div style={styles.featureTitle}>Génération de Code</div>
              <div style={styles.featureDescription}>
                Conversion automatique vers Java et diagrammes Mermaid
                avec structure de classes complète.
              </div>
            </div>

            <div style={styles.featureCard}>
              <svg style={styles.featureIcon} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M16 4H18C18.5304 4 19.0391 4.21071 19.4142 4.58579C19.7893 4.96086 20 5.46957 20 6V18C20 18.5304 19.7893 19.0391 19.4142 19.4142C19.0391 19.7893 18.5304 20 18 20H6C5.46957 20 4.96086 19.7893 4.58579 19.4142C4.21071 19.0391 4 18.5304 4 18V6C4 5.46957 4.21071 4.96086 4.58579 4.58579C4.96086 4.21071 5.46957 4 6 4H8M16 4V2M8 4V2M8 4V6M16 4V6" strokeWidth="2"/>
              </svg>
              <div style={styles.featureTitle}>Historique & Gestion</div>
              <div style={styles.featureDescription}>
                Sauvegarde automatique des analyses, historique complet avec authentification
                Firebase et gestion des projets.
              </div>
            </div>

            <div style={styles.featureCard}>
              <svg style={styles.featureIcon} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" strokeWidth="2"/>
                <path d="M8 12L11 15L16 10" strokeWidth="2"/>
              </svg>
              <div style={styles.featureTitle}>Interface Moderne</div>
              <div style={styles.featureDescription}>
                Interface utilisateur intuitive avec mode sombre/clair, design responsive
                et expérience utilisateur optimisée.
              </div>
            </div>
          </div>
        </div>
      )}

      {activeSection === "guide" && (
        <div style={styles.section}>
          <h2 style={styles.sectionTitle}>
            <svg style={styles.icon} viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M12 6.253V16.747M7 10L12 5L17 10M7 14L12 19L17 14" strokeWidth="2"/>
            </svg>
            Guide d'utilisation étape par étape
          </h2>

          <div style={{ marginBottom: "30px" }}>
            <div style={{ ...styles.featureCard, marginBottom: "20px" }}>
              <div style={{ display: "flex", alignItems: "center", gap: "15px", marginBottom: "15px" }}>
                <div style={{
                  backgroundColor: darkMode ? "#4f46e5" : "#2563eb",
                  color: "white",
                  borderRadius: "50%",
                  width: "30px",
                  height: "30px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  fontWeight: "bold"
                }}>1</div>
                <h3 style={styles.featureTitle}>Téléchargement du diagramme</h3>
              </div>
              <p style={styles.featureDescription}>
                Cliquez sur "Sélectionner un fichier" ou glissez-déposez votre diagramme UML.
                Formats supportés : PNG, JPG, JPEG, PDF. Taille maximale : 50MB.
              </p>
            </div>

            <div style={{ ...styles.featureCard, marginBottom: "20px" }}>
              <div style={{ display: "flex", alignItems: "center", gap: "15px", marginBottom: "15px" }}>
                <div style={{
                  backgroundColor: darkMode ? "#4f46e5" : "#2563eb",
                  color: "white",
                  borderRadius: "50%",
                  width: "30px",
                  height: "30px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  fontWeight: "bold"
                }}>2</div>
                <h3 style={styles.featureTitle}>Analyse automatique</h3>
              </div>
              <p style={styles.featureDescription}>
                Notre IA analyse votre diagramme en utilisant des plusieurs modèles pour une bonne resultats.
                Le processus prend généralement 15-30 secondes selon la complexité.
              </p>
            </div>

            <div style={{ ...styles.featureCard, marginBottom: "20px" }}>
              <div style={{ display: "flex", alignItems: "center", gap: "15px", marginBottom: "15px" }}>
                <div style={{
                  backgroundColor: darkMode ? "#4f46e5" : "#2563eb",
                  color: "white",
                  borderRadius: "50%",
                  width: "30px",
                  height: "30px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  fontWeight: "bold"
                }}>3</div>
                <h3 style={styles.featureTitle}>Visualisation des relations</h3>
              </div>
              <p style={styles.featureDescription}>
                Consultez l'onglet "Analyse de relations" pour voir votre diagramme annoté avec
                toutes les relations détectées (héritage, composition, agrégation, etc.).
              </p>
            </div>

            <div style={{ ...styles.featureCard, marginBottom: "20px" }}>
              <div style={{ display: "flex", alignItems: "center", gap: "15px", marginBottom: "15px" }}>
                <div style={{
                  backgroundColor: darkMode ? "#4f46e5" : "#2563eb",
                  color: "white",
                  borderRadius: "50%",
                  width: "30px",
                  height: "30px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  fontWeight: "bold"
                }}>4</div>
                <h3 style={styles.featureTitle}>Export et génération de code</h3>
              </div>
              <p style={styles.featureDescription}>
                Dans l'onglet "Extraction de diagramme", exportez vos résultats en Java ou Mermaid. Personnalisez le code généré selon vos besoins.
              </p>
            </div>
          </div>
        </div>
      )}

      {activeSection === "tech" && (
        <div style={styles.section}>
          <h2 style={styles.sectionTitle}>
            <svg style={styles.icon} viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M12 2L2 7L12 12L22 7L12 2Z" strokeWidth="2"/>
              <path d="M2 17L12 22L22 17" strokeWidth="2"/>
              <path d="M2 12L12 17L22 12" strokeWidth="2"/>
            </svg>
            Stack technologique
          </h2>

          <div style={styles.techStack}>
            <div style={styles.techItem}>
              <svg style={styles.icon} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M12 2L2 7L12 12L22 7L12 2Z" strokeWidth="2"/>
              </svg>
              <div>
                <strong>Frontend</strong>
                <p style={{ margin: "5px 0 0 0", fontSize: "0.9rem", opacity: 0.8 }}>
                  React 18, TypeScript, CSS-in-JS
                </p>
              </div>
            </div>

            <div style={styles.techItem}>
              <svg style={styles.icon} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M21 16V8C20.9996 7.64927 20.9071 7.30481 20.7315 7.00116C20.556 6.69751 20.3037 6.44536 20 6.27L13 2.27C12.696 2.09446 12.3511 2.00205 12 2.00205C11.6489 2.00205 11.304 2.09446 11 2.27L4 6.27C3.69626 6.44536 3.44398 6.69751 3.26846 7.00116C3.09294 7.30481 3.00036 7.64927 3 8V16C3.00036 16.3507 3.09294 16.6952 3.26846 16.9988C3.44398 17.3025 3.69626 17.5546 4 17.73L11 21.73C11.304 21.9055 11.6489 21.9979 12 21.9979C12.3511 21.9979 12.696 21.9055 13 21.73L20 17.73C20.3037 17.5546 20.556 17.3025 20.7315 16.9988C20.9071 16.6952 20.9996 16.3507 21 16Z" strokeWidth="2"/>
              </svg>
              <div>
                <strong>Backend</strong>
                <p style={{ margin: "5px 0 0 0", fontSize: "0.9rem", opacity: 0.8 }}>
                  FastAPI, Python, Uvicorn
                </p>
              </div>
            </div>

            <div style={styles.techItem}>
              <svg style={styles.icon} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" strokeWidth="2"/>
              </svg>
              <div>
                <strong>IA & Vision</strong>
                <p style={{ margin: "5px 0 0 0", fontSize: "0.9rem", opacity: 0.8 }}>
                  Confidentiel
                </p>
              </div>
            </div>

            <div style={styles.techItem}>
              <svg style={styles.icon} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M16 4H18C18.5304 4 19.0391 4.21071 19.4142 4.58579C19.7893 4.96086 20 5.46957 20 6V18C20 18.5304 19.7893 19.0391 19.4142 19.4142C19.0391 19.7893 18.5304 20 18 20H6C5.46957 20 4.96086 19.7893 4.58579 19.4142C4.21071 19.0391 4 18.5304 4 18V6C4 5.46957 4.21071 4.96086 4.58579 4.58579C4.96086 4.21071 5.46957 4 6 4H8" strokeWidth="2"/>
              </svg>
              <div>
                <strong>Base de données</strong>
                <p style={{ margin: "5px 0 0 0", fontSize: "0.9rem", opacity: 0.8 }}>
                  Firebase Firestore, Authentication
                </p>
              </div>
            </div>

            <div style={styles.techItem}>
              <svg style={styles.icon} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z" strokeWidth="2"/>
              </svg>
              <div>
                <strong>Visualisation</strong>
                <p style={{ margin: "5px 0 0 0", fontSize: "0.9rem", opacity: 0.8 }}>
                  Mermaid.js, SVG, Canvas API
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {activeSection === "api" && (
        <div style={styles.section}>
          <h2 style={styles.sectionTitle}>
            <svg style={styles.icon} viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M8 9L12 5L16 9M8 15L12 19L16 15" strokeWidth="2"/>
            </svg>
            Documentation API
          </h2>

          <div style={{ marginBottom: "30px" }}>
            <h3 style={{ color: darkMode ? "#e2e8f0" : "#1a202c", marginBottom: "15px" }}>
              Endpoints principaux
            </h3>

            <div style={{ ...styles.featureCard, marginBottom: "20px" }}>
              <div style={{ display: "flex", alignItems: "center", gap: "10px", marginBottom: "10px" }}>
                <span style={{
                  backgroundColor: "#10b981",
                  color: "white",
                  padding: "4px 8px",
                  borderRadius: "4px",
                  fontSize: "0.8rem",
                  fontWeight: "bold"
                }}>POST</span>
                <code style={{
                  backgroundColor: darkMode ? "#374151" : "#f3f4f6",
                  padding: "4px 8px",
                  borderRadius: "4px",
                  fontFamily: "monospace"
                }}>/detect/</code>
              </div>
              <p style={styles.featureDescription}>
                Endpoint principal pour l'analyse de diagrammes UML. Accepte les fichiers image
                et retourne l'analyse complète avec détection de relations.
              </p>
            </div>

            <div style={{ ...styles.featureCard, marginBottom: "20px" }}>
              <div style={{ display: "flex", alignItems: "center", gap: "10px", marginBottom: "10px" }}>
                <span style={{
                  backgroundColor: "#3b82f6",
                  color: "white",
                  padding: "4px 8px",
                  borderRadius: "4px",
                  fontSize: "0.8rem",
                  fontWeight: "bold"
                }}>GET</span>
                <code style={{
                  backgroundColor: darkMode ? "#374151" : "#f3f4f6",
                  padding: "4px 8px",
                  borderRadius: "4px",
                  fontFamily: "monospace"
                }}>/annotated_image.jpg</code>
              </div>
              <p style={styles.featureDescription}>
                Récupère l'image annotée avec les relations détectées visualisées.
              </p>
            </div>

            <div style={{ ...styles.featureCard, marginBottom: "20px" }}>
              <div style={{ display: "flex", alignItems: "center", gap: "10px", marginBottom: "10px" }}>
                <span style={{
                  backgroundColor: "#3b82f6",
                  color: "white",
                  padding: "4px 8px",
                  borderRadius: "4px",
                  fontSize: "0.8rem",
                  fontWeight: "bold"
                }}>GET</span>
                <code style={{
                  backgroundColor: darkMode ? "#374151" : "#f3f4f6",
                  padding: "4px 8px",
                  borderRadius: "4px",
                  fontFamily: "monospace"
                }}>/resultats_classes.txt</code>
              </div>
              <p style={styles.featureDescription}>
                Télécharge le fichier texte contenant l'extraction complète des classes,
                attributs et méthodes.
              </p>
            </div>
          </div>
        </div>
      )}

      {activeSection === "faq" && (
        <div style={styles.section}>
          <h2 style={styles.sectionTitle}>
            <svg style={styles.icon} viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M8.228 9C8.777 7.835 9.758 6.945 10.99 6.537C12.221 6.13 13.61 6.242 14.753 6.846C15.896 7.45 16.683 8.488 16.926 9.685C17.17 10.883 16.85 12.115 16.053 13.019C15.256 13.923 14.07 14.401 12.828 14.319C11.585 14.237 10.469 13.603 9.8 12.65M12 17V17.01M12 3C16.418 3 20 6.582 20 11C20 15.418 16.418 19 12 19C7.582 19 4 15.418 4 11C4 6.582 7.582 3 12 3Z" strokeWidth="2"/>
            </svg>
            Questions fréquemment posées
          </h2>

          <div style={{ marginBottom: "30px" }}>
            <div style={{ ...styles.featureCard, marginBottom: "20px" }}>
              <h3 style={{ ...styles.featureTitle, marginBottom: "10px" }}>
                🖼️ Quels formats d'image sont supportés ?
              </h3>
              <p style={styles.featureDescription}>
                Notre application supporte les formats PNG, JPG, JPEG et PDF. Pour de meilleurs résultats,
                utilisez des images de haute résolution (minimum 800x600) et bien éclairées.
                La taille maximale autorisée est de 50MB.
              </p>
            </div>

            <div style={{ ...styles.featureCard, marginBottom: "20px" }}>
              <h3 style={{ ...styles.featureTitle, marginBottom: "10px" }}>
                🌐 L'application fonctionne-t-elle hors ligne ?
              </h3>
              <p style={styles.featureDescription}>
                Non, notre application nécessite une connexion Internet active car elle utilise des modèles d'IA
                hébergés sur nos serveurs pour analyser vos diagrammes. L'interface peut être mise en cache
                mais l'analyse nécessite une connexion.
              </p>
            </div>

            <div style={{ ...styles.featureCard, marginBottom: "20px" }}>
              <h3 style={{ ...styles.featureTitle, marginBottom: "10px" }}>
                💻 Puis-je générer du code à partir de mon diagramme UML ?
              </h3>
              <p style={styles.featureDescription}>
                Oui ! Notre fonctionnalité de génération de code vous permet de transformer vos diagrammes UML
                en code Java ou en diagrammes Mermaid. Le code généré inclut les classes,
                attributs, méthodes et relations détectées.
              </p>
            </div>

            <div style={{ ...styles.featureCard, marginBottom: "20px" }}>
              <h3 style={{ ...styles.featureTitle, marginBottom: "10px" }}>
                🎯 Comment améliorer la précision de détection ?
              </h3>
              <p style={styles.featureDescription}>
                Pour optimiser les résultats : utilisez des diagrammes bien contrastés, assurez-vous que les textes
                sont lisibles (police minimum 12pt), évitez les images floues, utilisez des couleurs distinctes
                pour les différents éléments, et vérifiez que les lignes de relation sont clairement visibles.
              </p>
            </div>

            <div style={{ ...styles.featureCard, marginBottom: "20px" }}>
              <h3 style={{ ...styles.featureTitle, marginBottom: "10px" }}>
                🔒 Mes données sont-elles sécurisées ?
              </h3>
              <p style={styles.featureDescription}>
                Oui, nous utilisons Firebase Authentication pour la sécurité des comptes et Firestore pour
                le stockage sécurisé. Les images sont traitées temporairement et peuvent être supprimées
                après analyse. L'historique est lié à votre compte utilisateur.
              </p>
            </div>

            <div style={{ ...styles.featureCard, marginBottom: "20px" }}>
              <h3 style={{ ...styles.featureTitle, marginBottom: "10px" }}>
                ⚡ Quelle est la vitesse de traitement ?
              </h3>
              <p style={styles.featureDescription}>
                Le traitement prend généralement 15-30 secondes selon la complexité du diagramme.
                Les diagrammes simples (2-5 classes) sont traités en 10-15 secondes, tandis que
                les diagrammes complexes (10+ classes) peuvent prendre jusqu'à 45 secondes.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Section Contact */}
      <div style={{
        textAlign: "center",
        margin: "40px 0",
        backgroundColor: darkMode ? "rgba(79, 70, 229, 0.2)" : "rgba(37, 99, 235, 0.1)",
        borderRadius: "16px",
        padding: "40px 30px",
        border: darkMode ? "1px solid rgba(79, 70, 229, 0.3)" : "1px solid rgba(37, 99, 235, 0.2)"
      }}>
        <h3 style={{
          marginBottom: "20px",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          gap: "12px",
          color: darkMode ? "#e2e8f0" : "#1e40af",
          fontSize: "1.5rem"
        }}>
          <svg style={{ width: "24px", height: "24px", stroke: darkMode ? "#e2e8f0" : "#1e40af" }} viewBox="0 0 24 24" fill="none">
            <path d="M18 8C18 4.686 15.314 2 12 2C8.686 2 6 4.686 6 8C6 11.313 8.686 14 12 14C15.314 14 18 11.313 18 8Z" strokeWidth="2"/>
            <path d="M12 18C7.582 18 4 15.313 4 11.999V16C4 17.105 4.895 18 6 18H18C19.105 18 20 17.105 20 16V12C20 15.313 16.418 18 12 18Z" strokeWidth="2"/>
          </svg>
          Besoin d'aide supplémentaire ?
        </h3>
        <p style={{
          marginBottom: "25px",
          color: darkMode ? "#cbd5e1" : "#475569",
          fontSize: "1.1rem",
          lineHeight: "1.6"
        }}>
          Merci d'utiliser notre application UML Class Analyzer.
          Cette documentation vous guide dans l'utilisation de toutes les fonctionnalités disponibles.
        </p>
      </div>
    </div>
  );
};

export default Documentation;