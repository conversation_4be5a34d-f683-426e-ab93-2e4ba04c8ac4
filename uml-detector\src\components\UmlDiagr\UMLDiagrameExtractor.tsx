// UMLTextExtractor.tsx
import React, { useState, useEffect, useRef } from 'react';
import mermaid from 'mermaid';
import { createStyles } from './UMLDiagrameExtractorStyles';
import { convertToMermaid } from './convertToMermaid';
import { generateJavaFromMermaid } from './convertToJava';
import { useLanguage } from '../../context/LanguageContext';

interface UMLTextExtractorProps {
  darkMode?: boolean;
  textToConvert?: string; // Prop pour recevoir le texte extrait
}

const UMLTextExtractor: React.FC<UMLTextExtractorProps> = ({ darkMode = false, textToConvert = '' }) => {
  const [umlText, setUmlText] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [svgContent, setSvgContent] = useState<string>('');
  const [exportType, setExportType] = useState<'mermaid' | 'java' | 'python'>('mermaid');
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [copiedAlert, setCopiedAlert] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [zoomLevel, setZoomLevel] = useState(1); // État pour gérer le niveau de zoom
  const containerRef = useRef<HTMLDivElement>(null);
  const previewRef = useRef<HTMLDivElement>(null);

  const { t } = useLanguage();
  
  // Création des styles en fonction du mode sombre
  const styles = createStyles(darkMode, isFullscreen, copiedAlert, showModal);
  
  // Ajout des styles pour les contrôles de zoom
  const zoomStyles = {
    zoomControls: {
      display: "flex",
      gap: "0.5rem",
      marginLeft: "auto"
    },
    zoomButton: {
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      width: "28px",
      height: "28px",
      borderRadius: "4px",
      border: "none",
      backgroundColor: darkMode ? "#334155" : "#e2e8f0",
      color: darkMode ? "#e2e8f0" : "#334155",
      cursor: "pointer",
      transition: "background-color 0.2s"
    },
    // Combinons les styles pour l'utilisation
    ...styles
  };
  
  // Initialisation de Mermaid avec le thème approprié
  useEffect(() => {
    mermaid.initialize({ 
      startOnLoad: false, 
      theme: darkMode ? 'dark' : 'default',
      securityLevel: 'loose',
    });
    
    // Si nous avons déjà du contenu, le re-rendre avec le nouveau thème
    if (umlText.trim() !== '') {
      renderMermaidDiagram(umlText);
    }
  }, [darkMode]);

  // Effect to convert incoming text to Mermaid
  useEffect(() => {
    if (textToConvert && textToConvert.trim() !== '') {
      const mermaidCode = convertToMermaid(textToConvert);
      setUmlText(mermaidCode);
      setExportType('mermaid');
    }
  }, [textToConvert]);

  // Fonction de rendu du diagramme - utilise un état pour stocker le SVG au lieu de manipuler directement le DOM
  const renderMermaidDiagram = async (text: string) => {
    if (text.trim() === '' || exportType !== 'mermaid') {
      setSvgContent('');
      setError(null);
      return;
    }
    
    try {
      setError(null);
      const id = `mermaid-diagram-${Date.now()}`;
      const { svg } = await mermaid.render(id, text);
      setSvgContent(svg);
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
      setSvgContent('');
    }
  };

  // Effet pour rendre le diagramme quand le texte change
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (exportType === 'mermaid') {
        renderMermaidDiagram(umlText);
      }
    }, 300);
    
    return () => clearTimeout(timeoutId);
  }, [umlText, exportType]);
  
  // Fonction pour gérer le zoom
  const handleZoom = (direction: 'in' | 'out') => {
    if (direction === 'in') {
      // Augmenter le zoom (max 3)
      setZoomLevel(prev => Math.min(prev + 0.1, 3));
    } else {
      // Diminuer le zoom (min 0.5)
      setZoomLevel(prev => Math.max(prev - 0.1, 0.5));
    }
  };
  
  const handleCopyToClipboard = () => {
    if (umlText.trim() === '') return;
    
    navigator.clipboard.writeText(umlText)
      .then(() => {
        setCopiedAlert(true);
        setTimeout(() => setCopiedAlert(false), 2000);
      })
      .catch(err => console.error('Erreur lors de la copie:', err));
  };

  const handleExportSVG = () => {
    if (!svgContent) return;
    
    const blob = new Blob([svgContent], { type: "image/svg+xml" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "uml_diagram.svg";
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleExportPNG = () => {
    if (!svgContent || !previewRef.current) return;
    
    // Cette méthode est simplifiée - pour une véritable implémentation,
    // il faudrait utiliser une bibliothèque comme html2canvas ou svg2png
    alert("Fonctionnalité d'export PNG à implémenter avec une bibliothèque dédiée");
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const handleClearEditor = () => {
    setShowModal(true);
  };

  const confirmClear = () => {
    setUmlText('');
    setSvgContent('');
    setShowModal(false);
  };

  const handleDownloadCode = () => {
    const blob = new Blob([umlText], { type: "text/plain" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "diagram.mmd";
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };
  
 const handleDownloadCodeJava = () => {
    generateJavaFromMermaid(umlText)
      .then(() => console.log("Download complete"))
      .catch((err) => console.error("Error generating Java code:", err));
  };

  return (
    <div style={styles.container} ref={containerRef}>
      {/* Barre d'outils principale */}
      <div style={{
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center"
      }}>
        <div style={{
          display: "flex", 
          alignItems: "center", 
          gap: "12px"
        }}>
          <button 
            onClick={handleClearEditor}
            style={{
              ...styles.exportButton,
              backgroundColor: darkMode ? "rgba(239, 68, 68, 0.2)" : "rgba(239, 68, 68, 0.1)",
              color: darkMode ? "#f87171" : "#ef4444",
              borderColor: darkMode ? "rgba(248, 113, 113, 0.3)" : "rgba(239, 68, 68, 0.2)"
            }}
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M3 6h18M19 6v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6m3 0V4a2 2 0 012-2h4a2 2 0 012 2v2"/>
            </svg>
            Effacer
          </button>
          
          <button 
            onClick={handleCopyToClipboard}
            style={{
              ...styles.exportButton,
              opacity: umlText.trim() === '' ? 0.5 : 1,
              cursor: umlText.trim() === '' ? 'not-allowed' : 'pointer'
            }}
            disabled={umlText.trim() === ''}
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
              <path d="M5 15H4a2 2 0 01-2-2V4a2 2 0 012-2h9a2 2 0 012 2v1"></path>
            </svg>
            {t('umlExtractor.button.copy')}
          </button>
        </div>
        
        <div style={{
          display: "flex", 
          alignItems: "center", 
          gap: "12px"
        }}>
          <button
            onClick={toggleFullscreen}
            style={styles.iconButton}
            title={t('umlExtractor.button.fullscreen')}
          >
            {isFullscreen ? (
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M8 3v3a2 2 0 01-2 2H3m18 0h-3a2 2 0 01-2-2V3m0 18v-3a2 2 0 012-2h3M3 16h3a2 2 0 012 2v3"/>
              </svg>
            ) : (
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M15 3h6v6M9 21H3v-6M21 3l-7 7M3 21l7-7"/>
              </svg>
            )}
          </button>
        </div>
      </div>

      {/* Conteneur principal (éditeur + aperçu) */}
      <div style={styles.editorContainer}>
        {/* Panneau d'édition */}
        <div style={styles.editorPanel}>
          <div style={styles.toolBar}>
            <h3 style={styles.toolBarTitle}>
              <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M12 20h9M16.5 3.5a2.12 2.12 0 0 1 3 3L7 19l-4 1 1-4Z"/>
              </svg>
              Éditeur de code
            </h3>
            <div style={styles.toolBarActions}>
              {/* Boutons déplacés dans la barre principale */}
            </div>
          </div>
          
          <div style={styles.formatSelector}>
            <div 
              onClick={() => setExportType('mermaid')}
              style={{
                ...styles.formatOption,
                ...(exportType === 'mermaid' ? styles.formatOptionActive : {})
              }}
            >
              {t('umlExtractor.format.diagram')}
            </div>
            <div
              onClick={() => setExportType('java')}
              style={{
                ...styles.formatOption,
                ...(exportType === 'java' ? styles.formatOptionActive : {})
              }}
            >
              {t('umlExtractor.format.java')}
            </div>
          </div>
          
          <textarea
            value={umlText}
            onChange={(e) => setUmlText(e.target.value)}
            placeholder={exportType === 'mermaid' ? t('umlExtractor.placeholder.diagram') : exportType === 'java' ? t('umlExtractor.placeholder.java') : t('umlExtractor.placeholder.python')}
            style={styles.editor}
            spellCheck={false}
          />
        </div>
        
        {/* Panneau d'aperçu avec les contrôles de zoom ajoutés */}
        <div style={styles.previewPanel}>
          <div style={styles.toolBar}>
            <h3 style={styles.toolBarTitle}>
              <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path>
                <circle cx="12" cy="12" r="3"></circle>
              </svg>
              {t('umlExtractor.title')}
            </h3>
            
            {/* Boutons de zoom */}
            <div style={zoomStyles.zoomControls}>
              <button 
                onClick={() => handleZoom('out')}
                style={zoomStyles.zoomButton}
                title={t('umlExtractor.button.zoomOut')}
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <circle cx="11" cy="11" r="8"></circle>
                  <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                  <line x1="8" y1="11" x2="14" y2="11"></line>
                </svg>
              </button>
              <button 
                onClick={() => handleZoom('in')}
                style={zoomStyles.zoomButton}
                title={t('umlExtractor.button.zoomIn')}
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <circle cx="11" cy="11" r="8"></circle>
                  <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                  <line x1="11" y1="8" x2="11" y2="14"></line>
                  <line x1="8" y1="11" x2="14" y2="11"></line>
                </svg>
              </button>
              <button
                onClick={() => setZoomLevel(1)}
                style={zoomStyles.zoomButton}
                title={t('umlExtractor.button.resetZoom')}
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M1 4v6h6"></path>
                  <path d="M3.51 15a9 9 0 1 0 2.13-9.36L1 10"></path>
                </svg>
              </button>
            </div>
          </div>
          
          <div style={styles.previewContainer} ref={previewRef}>
            {exportType === 'mermaid' ? (
              svgContent ? (
                <div 
                  dangerouslySetInnerHTML={{ __html: svgContent }} 
                  style={{ 
                    maxWidth: "100%", 
                    overflow: "auto",
                    transform: `scale(${zoomLevel})`,
                    transformOrigin: "top left",
                    transition: "transform 0.2s ease-out"
                  }}
                />
              ) : (
                <div style={{ 
                  display: "flex", 
                  flexDirection: "column", 
                  alignItems: "center", 
                  opacity: 0.7,
                  gap: "1rem"
                }}>
                  <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke={darkMode ? "#94a3b8" : "#64748b"} strokeWidth="1" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M8 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8z"></path>
                    <path d="M8 2v4a2 2 0 002 2h4a2 2 0 002-2V2M10 14H8M16 14h-2M8 10h8"></path>
                  </svg>
                  <p style={{ textAlign: "center", fontSize: "0.9rem" }}>
                    {t('umlExtractor.preview.empty').replace('{format}', 'diagramme')}
                  </p>
                </div>
              )
            ) : (
              <div style={{ 
                padding: "1rem",
                backgroundColor: darkMode ? "#1e293b" : "#f1f5f9", 
                width: "100%",
                borderRadius: "6px",
                overflow: "auto"
              }}>
                <pre style={{ 
                  margin: 0,
                  fontFamily: "monospace",
                  fontSize: "0.9rem",
                  lineHeight: "1.5",
                  color: darkMode ? "#e2e8f0" : "#1e293b"
                }}>
                  {umlText || t('umlExtractor.preview.empty').replace('{format}', exportType)}
                </pre>
              </div>
            )}
            
            {error && (
              <div style={{
                position: "absolute",
                bottom: "1rem",
                left: "1rem",
                right: "1rem",
                padding: "0.75rem 1rem",
                backgroundColor: darkMode ? "rgba(220, 38, 38, 0.2)" : "rgba(254, 226, 226, 0.9)",
                color: darkMode ? "#f87171" : "#dc2626",
                borderRadius: "6px",
                fontSize: "0.8rem",
                borderLeft: "4px solid #ef4444"
              }}>
                <strong>{t('umlExtractor.error.syntax')}</strong> {error}
              </div>
            )}
            
            <div style={styles.tooltip}>
              <span>{t('umlExtractor.copied')}</span>
            </div>
          </div>
          
          {exportType === 'mermaid' && svgContent && (
            <div style={styles.exportMenu}>
              <button 
                onClick={handleExportSVG}
                style={styles.exportButton}
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M21 15v4a2 2 0 01-2 2H5a2 2 0 01-2-2v-4M17 8l-5-5-5 5M12 3v12"></path>
                </svg>
                Exporter SVG
              </button>
              
              <button 
                onClick={handleExportPNG}
                style={styles.exportButton}
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M4 14.899A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 2.5 8.242M12 12v9M8 17h8"></path>
                </svg>
                Exporter PNG
              </button>
              
              <button 
                onClick={handleDownloadCode}
                style={styles.exportButton}
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M19 9l-7 7-7-7"></path>
                  <path d="M12 16V4"></path>
                  <path d="M5 20h14"></path>
                </svg>
                {t('umlExtractor.button.export')}
              </button>
              <button 
                onClick={handleDownloadCodeJava}
                style={styles.exportButton}
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M19 9l-7 7-7-7"></path>
                  <path d="M12 16V4"></path>
                  <path d="M5 20h14"></path>
                </svg>
                {t('umlExtractor.button.export')}
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Modal de confirmation pour effacer le contenu */}
      {showModal && (
        <div style={styles.modalOverlay}>
          <div style={styles.modalContent}>
            <div style={styles.modalHeader}>
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke={darkMode ? "#f87171" : "#ef4444"} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
                <line x1="12" y1="9" x2="12" y2="13"></line>
                <line x1="12" y1="17" x2="12.01" y2="17"></line>
              </svg>
              <h4 style={styles.modalTitle}>Confirmer la suppression</h4>
            </div>
            <p style={styles.modalText}>
              Êtes-vous sûr de vouloir effacer tout le contenu de l'éditeur ? Cette action est irréversible.
            </p>
            <div style={styles.modalActions}>
              <button 
                onClick={() => setShowModal(false)}
                style={{
                  ...styles.modalButton,
                  backgroundColor: darkMode ? "rgba(51, 65, 85, 0.8)" : "#e2e8f0",
                  color: darkMode ? "#cbd5e1" : "#475569",
                }}
              >
                Annuler
              </button>
              <button 
                onClick={confirmClear}
                style={{
                  ...styles.modalButton,
                  backgroundColor: darkMode ? "rgba(239, 68, 68, 0.2)" : "#ef4444",
                  color: darkMode ? "#f87171" : "#ffffff",
                }}
              >
                Effacer le contenu
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UMLTextExtractor;