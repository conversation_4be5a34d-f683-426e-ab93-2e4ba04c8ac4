{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\FixTorchUMLDGM\\\\uml-detector\\\\src\\\\components\\\\imageUp\\\\HistoryAnalysisSection.tsx\",\n  _s = $RefreshSig$();\n// HistoryAnalysisSection.tsx - Composant pour l'analyse historique et l'import de classes\nimport React, { useState, useEffect } from 'react';\nimport { useHistory } from '../../context/HistoryContext';\nimport { useAuth } from '../../context/AuthContext';\nimport { useLanguage } from '../../context/LanguageContext';\nimport { HistoryAnalysisService } from '../../services/HistoryAnalysisService';\nimport { ChevronDown, Eye, Clock, Zap, AlertTriangle, Shield, Users, Lock } from 'lucide-react';\nimport './HistoryAnalysisSection.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst HistoryAnalysisSection = ({\n  darkMode,\n  targetClassName,\n  currentClassData,\n  onImport,\n  currentDiagramText\n}) => {\n  _s();\n  const {\n    historyItems\n  } = useHistory();\n  const {\n    currentUser\n  } = useAuth();\n  const {\n    t\n  } = useLanguage();\n  const [isExpanded, setIsExpanded] = useState(false);\n  const [matches, setMatches] = useState([]);\n  const [selectedMatches, setSelectedMatches] = useState(new Set());\n  const [selectedAttributes, setSelectedAttributes] = useState(new Map());\n  const [selectedMethods, setSelectedMethods] = useState(new Map());\n  const [sortOption, setSortOption] = useState(HistoryAnalysisService.getSortOptions()[0]);\n  const [showPreview, setShowPreview] = useState(null);\n  const [conflicts, setConflicts] = useState({\n    attributes: [],\n    methods: []\n  });\n  const [previewData, setPreviewData] = useState(null);\n\n  // Rechercher les correspondances lors du changement de classe cible\n  useEffect(() => {\n    if (!targetClassName || !currentUser) return;\n    const foundMatches = HistoryAnalysisService.findMatchingDiagrams(targetClassName, historyItems, currentUser.uid, currentDiagramText);\n    const sortedMatches = HistoryAnalysisService.sortMatches(foundMatches, sortOption);\n    setMatches(sortedMatches);\n  }, [targetClassName, historyItems, currentUser, sortOption, currentDiagramText]);\n\n  // Détecter les conflits lors du changement de sélection\n  useEffect(() => {\n    const selectedMatchObjects = matches.filter(match => selectedMatches.has(match.historyItem.id));\n    const selectedClasses = selectedMatchObjects.flatMap(match => match.matchingClasses);\n    if (selectedClasses.length > 0) {\n      const detectedConflicts = HistoryAnalysisService.detectConflicts(currentClassData, selectedClasses);\n      setConflicts(detectedConflicts);\n    } else {\n      setConflicts({\n        attributes: [],\n        methods: []\n      });\n    }\n  }, [selectedMatches, matches, currentClassData]);\n  const handleToggleExpand = () => {\n    setIsExpanded(!isExpanded);\n  };\n  const handleMatchSelection = (matchId, selected) => {\n    const newSelection = new Set(selectedMatches);\n    if (selected) {\n      newSelection.add(matchId);\n    } else {\n      newSelection.delete(matchId);\n      // Nettoyer les sélections d'attributs/méthodes pour ce match\n      const newSelectedAttributes = new Map(selectedAttributes);\n      const newSelectedMethods = new Map(selectedMethods);\n      newSelectedAttributes.delete(matchId);\n      newSelectedMethods.delete(matchId);\n      setSelectedAttributes(newSelectedAttributes);\n      setSelectedMethods(newSelectedMethods);\n    }\n    setSelectedMatches(newSelection);\n  };\n  const handleAttributeSelection = (matchId, attribute, selected) => {\n    const newSelectedAttributes = new Map(selectedAttributes);\n    const currentAttributes = newSelectedAttributes.get(matchId) || new Set();\n    if (selected) {\n      currentAttributes.add(attribute);\n    } else {\n      currentAttributes.delete(attribute);\n    }\n    if (currentAttributes.size > 0) {\n      newSelectedAttributes.set(matchId, currentAttributes);\n    } else {\n      newSelectedAttributes.delete(matchId);\n    }\n    setSelectedAttributes(newSelectedAttributes);\n  };\n  const handleMethodSelection = (matchId, method, selected) => {\n    const newSelectedMethods = new Map(selectedMethods);\n    const currentMethods = newSelectedMethods.get(matchId) || new Set();\n    if (selected) {\n      currentMethods.add(method);\n    } else {\n      currentMethods.delete(method);\n    }\n    if (currentMethods.size > 0) {\n      newSelectedMethods.set(matchId, currentMethods);\n    } else {\n      newSelectedMethods.delete(matchId);\n    }\n    setSelectedMethods(newSelectedMethods);\n  };\n\n  // Fonction pour vérifier si tous les éléments d'un match sont sélectionnés\n  const isAllSelectedForMatch = match => {\n    const matchId = match.historyItem.id;\n    const matchingClass = match.matchingClasses[0];\n    if (!matchingClass) return false;\n    const selectedAttrs = selectedAttributes.get(matchId) || new Set();\n    const selectedMethodsSet = selectedMethods.get(matchId) || new Set();\n    const allAttrsSelected = matchingClass.attributes.every(attr => selectedAttrs.has(attr));\n    const allMethodsSelected = matchingClass.methods.every(method => selectedMethodsSet.has(method));\n    return allAttrsSelected && allMethodsSelected && (matchingClass.attributes.length > 0 || matchingClass.methods.length > 0);\n  };\n\n  // Fonction pour cocher/décocher tous les éléments d'un match\n  const handleCheckAll = (match, checked) => {\n    const matchId = match.historyItem.id;\n    const matchingClass = match.matchingClasses[0];\n    if (!matchingClass) return;\n    if (checked) {\n      // Cocher tous les attributs et méthodes\n      setSelectedAttributes(prev => {\n        const newMap = new Map(prev);\n        newMap.set(matchId, new Set(matchingClass.attributes));\n        return newMap;\n      });\n      setSelectedMethods(prev => {\n        const newMap = new Map(prev);\n        newMap.set(matchId, new Set(matchingClass.methods));\n        return newMap;\n      });\n    } else {\n      // Décocher tous les attributs et méthodes\n      setSelectedAttributes(prev => {\n        const newMap = new Map(prev);\n        newMap.set(matchId, new Set());\n        return newMap;\n      });\n      setSelectedMethods(prev => {\n        const newMap = new Map(prev);\n        newMap.set(matchId, new Set());\n        return newMap;\n      });\n    }\n  };\n  const handleImport = () => {\n    // Construire les données à importer basées sur les sélections granulaires\n    const attributesToImport = [];\n    const methodsToImport = [];\n\n    // Collecter tous les attributs et méthodes sélectionnés\n    selectedAttributes.forEach((attributes, matchId) => {\n      attributes.forEach(attr => {\n        if (!attributesToImport.includes(attr)) {\n          attributesToImport.push(attr);\n        }\n      });\n    });\n    selectedMethods.forEach((methods, matchId) => {\n      methods.forEach(method => {\n        if (!methodsToImport.includes(method)) {\n          methodsToImport.push(method);\n        }\n      });\n    });\n    if (attributesToImport.length > 0 || methodsToImport.length > 0) {\n      const importedData = {\n        name: currentClassData.name,\n        attributes: attributesToImport,\n        methods: methodsToImport\n      };\n      onImport(importedData);\n\n      // Reset toutes les sélections\n      setSelectedMatches(new Set());\n      setSelectedAttributes(new Map());\n      setSelectedMethods(new Map());\n    }\n  };\n  const handlePreview = matchId => {\n    if (showPreview === matchId) {\n      setShowPreview(null);\n      setPreviewData(null);\n    } else {\n      const match = matches.find(m => m.historyItem.id === matchId);\n      setShowPreview(matchId);\n      setPreviewData(match || null);\n    }\n  };\n  const getAccessIcon = match => {\n    const accessLevel = HistoryAnalysisService.getAccessLevel(match.historyItem, (currentUser === null || currentUser === void 0 ? void 0 : currentUser.uid) || '');\n    switch (accessLevel) {\n      case 'owner':\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          title: t('historyAnalysis.access.owner'),\n          children: /*#__PURE__*/_jsxDEV(Shield, {\n            size: 12,\n            color: \"#10b981\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 64\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 16\n        }, this);\n      case 'shared':\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          title: t('historyAnalysis.access.shared'),\n          children: /*#__PURE__*/_jsxDEV(Users, {\n            size: 12,\n            color: \"#f59e0b\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 65\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 16\n        }, this);\n      case 'public':\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          title: t('historyAnalysis.access.public'),\n          children: /*#__PURE__*/_jsxDEV(Eye, {\n            size: 12,\n            color: \"#3b82f6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 65\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          title: t('historyAnalysis.access.restricted'),\n          children: /*#__PURE__*/_jsxDEV(Lock, {\n            size: 12,\n            color: \"#ef4444\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 69\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  const getSectionStyles = () => ({\n    container: {\n      marginBottom: '24px',\n      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)'}`,\n      borderRadius: '12px',\n      background: darkMode ? 'linear-gradient(135deg, rgba(10, 15, 28, 0.8) 0%, rgba(30, 41, 59, 0.6) 100%)' : 'linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(255, 255, 255, 0.9) 100%)',\n      backdropFilter: 'blur(10px)',\n      WebkitBackdropFilter: 'blur(10px)'\n    },\n    header: {\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'space-between',\n      padding: '16px 20px',\n      cursor: 'pointer',\n      borderBottom: isExpanded ? `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.15)' : 'rgba(59, 130, 246, 0.08)'}` : 'none'\n    },\n    headerLeft: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '12px'\n    },\n    title: {\n      fontSize: '16px',\n      fontWeight: '600',\n      color: darkMode ? '#f8fafc' : '#0f172a',\n      margin: 0\n    },\n    badge: {\n      backgroundColor: matches.length > 0 ? '#3b82f6' : darkMode ? '#64748b' : '#94a3b8',\n      color: '#ffffff',\n      fontSize: '12px',\n      fontWeight: '600',\n      padding: '4px 8px',\n      borderRadius: '12px',\n      minWidth: '20px',\n      textAlign: 'center'\n    },\n    expandIcon: {\n      color: darkMode ? '#94a3b8' : '#64748b',\n      transition: 'transform 0.2s ease',\n      transform: isExpanded ? 'rotate(180deg)' : 'rotate(0deg)'\n    },\n    content: {\n      padding: isExpanded ? '20px' : '0',\n      maxHeight: isExpanded ? '400px' : '0',\n      overflow: 'hidden',\n      transition: 'all 0.3s ease'\n    },\n    sortContainer: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '12px',\n      marginBottom: '16px'\n    },\n    sortLabel: {\n      fontSize: '14px',\n      color: darkMode ? '#cbd5e1' : '#64748b',\n      fontWeight: '500'\n    },\n    sortSelect: {\n      backgroundColor: darkMode ? 'rgba(30, 41, 59, 0.8)' : 'rgba(248, 250, 252, 0.8)',\n      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)'}`,\n      borderRadius: '8px',\n      padding: '6px 12px',\n      fontSize: '14px',\n      color: darkMode ? '#e2e8f0' : '#374151',\n      cursor: 'pointer'\n    },\n    matchesList: {\n      maxHeight: '250px',\n      overflowY: 'auto',\n      marginBottom: '16px'\n    },\n    matchItem: {\n      display: 'flex',\n      alignItems: 'flex-start',\n      flexDirection: 'column',\n      gap: '12px',\n      padding: '12px',\n      marginBottom: '8px',\n      backgroundColor: darkMode ? 'rgba(30, 41, 59, 0.4)' : 'rgba(248, 250, 252, 0.6)',\n      borderRadius: '8px',\n      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.1)' : 'rgba(59, 130, 246, 0.05)'}`,\n      transition: 'all 0.2s ease'\n    },\n    matchItemHeader: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '12px',\n      width: '100%'\n    },\n    checkbox: {\n      width: '16px',\n      height: '16px',\n      accentColor: '#3b82f6',\n      cursor: 'pointer'\n    },\n    matchInfo: {\n      flex: 1\n    },\n    matchTitle: {\n      fontSize: '14px',\n      fontWeight: '500',\n      color: darkMode ? '#e2e8f0' : '#374151',\n      marginBottom: '4px'\n    },\n    matchMeta: {\n      fontSize: '12px',\n      color: darkMode ? '#94a3b8' : '#64748b',\n      display: 'flex',\n      alignItems: 'center',\n      gap: '8px'\n    },\n    similarityBadge: {\n      backgroundColor: '#10b981',\n      color: '#ffffff',\n      fontSize: '11px',\n      fontWeight: '600',\n      padding: '2px 6px',\n      borderRadius: '8px'\n    },\n    actionButtons: {\n      display: 'flex',\n      gap: '8px'\n    },\n    actionButton: {\n      backgroundColor: 'transparent',\n      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.3)' : 'rgba(59, 130, 246, 0.2)'}`,\n      borderRadius: '6px',\n      padding: '6px',\n      cursor: 'pointer',\n      color: darkMode ? '#60a5fa' : '#3b82f6',\n      transition: 'all 0.2s ease'\n    },\n    conflictsWarning: {\n      backgroundColor: darkMode ? 'rgba(245, 158, 11, 0.1)' : 'rgba(245, 158, 11, 0.05)',\n      border: `1px solid ${darkMode ? 'rgba(245, 158, 11, 0.3)' : 'rgba(245, 158, 11, 0.2)'}`,\n      borderRadius: '8px',\n      padding: '12px',\n      marginBottom: '16px',\n      display: 'flex',\n      alignItems: 'center',\n      gap: '8px'\n    },\n    conflictsText: {\n      fontSize: '13px',\n      color: darkMode ? '#fbbf24' : '#d97706',\n      fontWeight: '500'\n    },\n    importButton: {\n      backgroundColor: selectedMatches.size > 0 ? '#3b82f6' : darkMode ? '#374151' : '#9ca3af',\n      color: '#ffffff',\n      border: 'none',\n      borderRadius: '8px',\n      padding: '10px 16px',\n      fontSize: '14px',\n      fontWeight: '600',\n      cursor: selectedMatches.size > 0 ? 'pointer' : 'not-allowed',\n      transition: 'all 0.2s ease',\n      display: 'flex',\n      alignItems: 'center',\n      gap: '8px'\n    },\n    emptyState: {\n      textAlign: 'center',\n      color: darkMode ? '#64748b' : '#9ca3af',\n      fontSize: '14px',\n      fontStyle: 'italic',\n      padding: '20px'\n    },\n    previewContainer: {\n      backgroundColor: darkMode ? 'rgba(15, 23, 42, 0.8)' : 'rgba(248, 250, 252, 0.9)',\n      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)'}`,\n      borderRadius: '8px',\n      padding: '16px',\n      marginTop: '12px',\n      animation: 'slideDown 0.3s ease'\n    },\n    previewHeader: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '8px',\n      marginBottom: '12px',\n      fontSize: '14px',\n      fontWeight: '600',\n      color: darkMode ? '#e2e8f0' : '#374151'\n    },\n    previewContent: {\n      display: 'grid',\n      gridTemplateColumns: '1fr 1fr',\n      gap: '16px'\n    },\n    previewSection: {\n      fontSize: '13px',\n      color: darkMode ? '#cbd5e1' : '#64748b'\n    },\n    previewLabel: {\n      fontWeight: '600',\n      marginBottom: '4px',\n      color: darkMode ? '#f1f5f9' : '#374151'\n    },\n    relatedClassesList: {\n      display: 'flex',\n      flexWrap: 'wrap',\n      gap: '4px',\n      marginTop: '4px'\n    },\n    relatedClassTag: {\n      backgroundColor: darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)',\n      color: darkMode ? '#93c5fd' : '#3b82f6',\n      fontSize: '11px',\n      padding: '2px 6px',\n      borderRadius: '4px',\n      fontWeight: '500'\n    },\n    // Styles pour la sélection granulaire\n    granularSelection: {\n      marginTop: '12px',\n      padding: '12px',\n      backgroundColor: darkMode ? 'rgba(59, 130, 246, 0.05)' : 'rgba(59, 130, 246, 0.02)',\n      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.1)' : 'rgba(59, 130, 246, 0.08)'}`,\n      borderRadius: '8px'\n    },\n    granularHeader: {\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'space-between',\n      fontSize: '13px',\n      fontWeight: '600',\n      color: darkMode ? '#e2e8f0' : '#374151',\n      marginBottom: '8px'\n    },\n    checkAllLabel: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '6px',\n      fontSize: '12px',\n      fontWeight: '500',\n      color: darkMode ? '#60a5fa' : '#3b82f6',\n      // Couleur bleue harmonisée avec le thème\n      cursor: 'pointer',\n      transition: 'color 0.2s ease'\n    },\n    checkAllCheckbox: {\n      width: '14px',\n      height: '14px',\n      cursor: 'pointer',\n      accentColor: darkMode ? '#60a5fa' : '#3b82f6' // Couleur de la checkbox harmonisée\n    },\n    granularTitle: {\n      fontSize: '13px',\n      fontWeight: '600',\n      color: darkMode ? '#e2e8f0' : '#374151'\n    },\n    granularContent: {\n      display: 'flex',\n      flexDirection: 'column',\n      gap: '12px'\n    },\n    granularSection: {\n      display: 'flex',\n      flexDirection: 'column',\n      gap: '6px'\n    },\n    granularSectionTitle: {\n      fontSize: '12px',\n      fontWeight: '600',\n      color: darkMode ? '#94a3b8' : '#6b7280',\n      textTransform: 'uppercase',\n      letterSpacing: '0.5px'\n    },\n    granularItems: {\n      display: 'flex',\n      flexDirection: 'column',\n      gap: '4px',\n      paddingLeft: '8px'\n    },\n    granularItem: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '8px',\n      cursor: 'pointer',\n      padding: '4px 0'\n    },\n    granularCheckbox: {\n      width: '14px',\n      height: '14px',\n      cursor: 'pointer'\n    },\n    granularItemText: {\n      fontSize: '12px',\n      color: darkMode ? '#cbd5e1' : '#4b5563',\n      fontFamily: 'monospace'\n    }\n  });\n  const styles = getSectionStyles();\n  if (!currentUser) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: styles.container,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.header,\n      onClick: handleToggleExpand,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.headerLeft,\n        children: [/*#__PURE__*/_jsxDEV(Zap, {\n          size: 18,\n          color: darkMode ? '#60a5fa' : '#3b82f6'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 560,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n          style: styles.title,\n          children: t('historyAnalysis.title')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 561,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: styles.badge,\n          children: matches.length\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 562,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 559,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ChevronDown, {\n        size: 20,\n        style: styles.expandIcon\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 564,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 558,\n      columnNumber: 7\n    }, this), isExpanded && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.content,\n      children: matches.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.emptyState,\n        children: t('historyAnalysis.noResults').replace('{className}', targetClassName)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 570,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.sortContainer,\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: styles.sortLabel,\n            children: t('historyAnalysis.sortBy')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 576,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            style: styles.sortSelect,\n            value: `${sortOption.key}-${sortOption.direction}`,\n            onChange: e => {\n              const [key, direction] = e.target.value.split('-');\n              const option = HistoryAnalysisService.getSortOptions().find(opt => opt.key === key && opt.direction === direction);\n              if (option) setSortOption(option);\n            },\n            children: HistoryAnalysisService.getSortOptions().map(option => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: `${option.key}-${option.direction}`,\n              children: option.label\n            }, `${option.key}-${option.direction}`, false, {\n              fileName: _jsxFileName,\n              lineNumber: 589,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 577,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 575,\n          columnNumber: 15\n        }, this), conflicts.attributes.length > 0 || conflicts.methods.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.conflictsWarning,\n          children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 598,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: styles.conflictsText,\n            children: [\"Conflits d\\xE9tect\\xE9s: \", conflicts.attributes.length, \" attribut(s), \", conflicts.methods.length, \" m\\xE9thode(s)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 599,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 597,\n          columnNumber: 17\n        }, this) : null, /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.matchesList,\n          children: matches.map(match => {\n            var _match$matchingClasse, _previewData$previewD, _previewData$previewD2, _previewData$previewD3, _match$matchingClasse2, _match$matchingClasse3;\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              style: styles.matchItem,\n              className: \"history-match-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: styles.matchItemHeader,\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  style: styles.checkbox,\n                  checked: selectedMatches.has(match.historyItem.id),\n                  onChange: e => handleMatchSelection(match.historyItem.id, e.target.checked)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 609,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: styles.matchInfo,\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: styles.matchTitle,\n                    children: match.historyItem.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 616,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: styles.matchMeta,\n                    className: \"history-match-meta\",\n                    children: [/*#__PURE__*/_jsxDEV(Clock, {\n                      size: 12\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 618,\n                      columnNumber: 27\n                    }, this), match.historyItem.createdAt.toLocaleDateString('fr-FR'), /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: styles.similarityBadge,\n                      className: \"history-similarity-badge\",\n                      children: [Math.round(match.similarity), \"%\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 620,\n                      columnNumber: 27\n                    }, this), getAccessIcon(match), match.isShared && /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        fontSize: '11px',\n                        color: darkMode ? '#fbbf24' : '#d97706'\n                      },\n                      children: \"Partag\\xE9\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 624,\n                      columnNumber: 46\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 617,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 615,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: styles.actionButtons,\n                  children: /*#__PURE__*/_jsxDEV(\"button\", {\n                    style: styles.actionButton,\n                    className: \"history-action-button\",\n                    onClick: () => handlePreview(match.historyItem.id),\n                    title: \"Voir aper\\xE7u\",\n                    children: /*#__PURE__*/_jsxDEV(Eye, {\n                      size: 14\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 634,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 628,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 627,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 608,\n                columnNumber: 21\n              }, this), showPreview === match.historyItem.id && previewData && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: styles.previewContainer,\n                className: \"history-preview-container\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: styles.previewHeader,\n                  children: [/*#__PURE__*/_jsxDEV(Eye, {\n                    size: 16\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 643,\n                    columnNumber: 27\n                  }, this), \"Aper\\xE7u de la classe \\\"\", (_match$matchingClasse = match.matchingClasses[0]) === null || _match$matchingClasse === void 0 ? void 0 : _match$matchingClasse.name, \"\\\"\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 642,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: styles.previewContent,\n                  className: \"history-preview-content\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: styles.previewSection,\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: styles.previewLabel,\n                      children: \"Contenu de la classe:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 648,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: (_previewData$previewD = previewData.previewData) === null || _previewData$previewD === void 0 ? void 0 : _previewData$previewD.classContext\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 649,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 647,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: styles.previewSection,\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: styles.previewLabel,\n                      children: \"Classes li\\xE9es:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 652,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: styles.relatedClassesList,\n                      className: \"history-related-classes-list\",\n                      children: [(_previewData$previewD2 = previewData.previewData) === null || _previewData$previewD2 === void 0 ? void 0 : _previewData$previewD2.relatedClasses.map((className, idx) => /*#__PURE__*/_jsxDEV(\"span\", {\n                        style: styles.relatedClassTag,\n                        children: className\n                      }, idx, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 655,\n                        columnNumber: 33\n                      }, this)), (!((_previewData$previewD3 = previewData.previewData) !== null && _previewData$previewD3 !== void 0 && _previewData$previewD3.relatedClasses) || previewData.previewData.relatedClasses.length === 0) && /*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          fontStyle: 'italic',\n                          color: darkMode ? '#64748b' : '#9ca3af'\n                        },\n                        children: \"Aucune classe li\\xE9e d\\xE9tect\\xE9e\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 660,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 653,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 651,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 646,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 641,\n                columnNumber: 23\n              }, this), selectedMatches.has(match.historyItem.id) && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: styles.granularSelection,\n                className: \"history-granular-selection\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: styles.granularHeader,\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    style: styles.granularTitle,\n                    children: \"S\\xE9lectionner les \\xE9l\\xE9ments \\xE0 importer :\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 674,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    style: styles.checkAllLabel,\n                    onMouseEnter: e => {\n                      e.currentTarget.style.color = darkMode ? '#93c5fd' : '#2563eb';\n                    },\n                    onMouseLeave: e => {\n                      e.currentTarget.style.color = darkMode ? '#60a5fa' : '#3b82f6';\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"checkbox\",\n                      checked: isAllSelectedForMatch(match),\n                      onChange: e => handleCheckAll(match, e.target.checked),\n                      style: styles.checkAllCheckbox\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 684,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Cocher tous\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 690,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 675,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 673,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: styles.granularContent,\n                  children: [((_match$matchingClasse2 = match.matchingClasses[0]) === null || _match$matchingClasse2 === void 0 ? void 0 : _match$matchingClasse2.attributes) && match.matchingClasses[0].attributes.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: styles.granularSection,\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: styles.granularSectionTitle,\n                      children: \"Attributs :\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 698,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: styles.granularItems,\n                      children: match.matchingClasses[0].attributes.map((attribute, idx) => {\n                        var _selectedAttributes$g;\n                        return /*#__PURE__*/_jsxDEV(\"label\", {\n                          style: styles.granularItem,\n                          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                            type: \"checkbox\",\n                            style: styles.granularCheckbox,\n                            checked: ((_selectedAttributes$g = selectedAttributes.get(match.historyItem.id)) === null || _selectedAttributes$g === void 0 ? void 0 : _selectedAttributes$g.has(attribute)) || false,\n                            onChange: e => handleAttributeSelection(match.historyItem.id, attribute, e.target.checked)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 702,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            style: styles.granularItemText,\n                            children: attribute\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 708,\n                            columnNumber: 37\n                          }, this)]\n                        }, idx, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 701,\n                          columnNumber: 35\n                        }, this);\n                      })\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 699,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 697,\n                    columnNumber: 29\n                  }, this), ((_match$matchingClasse3 = match.matchingClasses[0]) === null || _match$matchingClasse3 === void 0 ? void 0 : _match$matchingClasse3.methods) && match.matchingClasses[0].methods.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: styles.granularSection,\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: styles.granularSectionTitle,\n                      children: \"M\\xE9thodes :\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 718,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: styles.granularItems,\n                      children: match.matchingClasses[0].methods.map((method, idx) => {\n                        var _selectedMethods$get;\n                        return /*#__PURE__*/_jsxDEV(\"label\", {\n                          style: styles.granularItem,\n                          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                            type: \"checkbox\",\n                            style: styles.granularCheckbox,\n                            checked: ((_selectedMethods$get = selectedMethods.get(match.historyItem.id)) === null || _selectedMethods$get === void 0 ? void 0 : _selectedMethods$get.has(method)) || false,\n                            onChange: e => handleMethodSelection(match.historyItem.id, method, e.target.checked)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 722,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            style: styles.granularItemText,\n                            children: method\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 728,\n                            columnNumber: 37\n                          }, this)]\n                        }, idx, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 721,\n                          columnNumber: 35\n                        }, this);\n                      })\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 719,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 717,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 694,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 672,\n                columnNumber: 23\n              }, this)]\n            }, match.historyItem.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 607,\n              columnNumber: 19\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 605,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          style: styles.importButton,\n          onClick: handleImport,\n          disabled: (() => {\n            const totalAttributes = Array.from(selectedAttributes.values()).reduce((sum, attrs) => sum + attrs.size, 0);\n            const totalMethods = Array.from(selectedMethods.values()).reduce((sum, methods) => sum + methods.size, 0);\n            return totalAttributes + totalMethods === 0;\n          })(),\n          children: [/*#__PURE__*/_jsxDEV(Zap, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 750,\n            columnNumber: 17\n          }, this), (() => {\n            const totalAttributes = Array.from(selectedAttributes.values()).reduce((sum, attrs) => sum + attrs.size, 0);\n            const totalMethods = Array.from(selectedMethods.values()).reduce((sum, methods) => sum + methods.size, 0);\n            const totalElements = totalAttributes + totalMethods;\n            if (totalElements === 0) {\n              return `Importer (${selectedMatches.size} diagramme${selectedMatches.size > 1 ? 's' : ''} sélectionné${selectedMatches.size > 1 ? 's' : ''})`;\n            }\n            return `Importer (${totalElements} élément${totalElements > 1 ? 's' : ''} sélectionné${totalElements > 1 ? 's' : ''})`;\n          })()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 741,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 568,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 557,\n    columnNumber: 5\n  }, this);\n};\n_s(HistoryAnalysisSection, \"n0ORoxmj3nTovnlzOnFHhTMlmrs=\", false, function () {\n  return [useHistory, useAuth, useLanguage];\n});\n_c = HistoryAnalysisSection;\nexport default HistoryAnalysisSection;\nvar _c;\n$RefreshReg$(_c, \"HistoryAnalysisSection\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useHistory", "useAuth", "useLanguage", "HistoryAnalysisService", "ChevronDown", "Eye", "Clock", "Zap", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Shield", "Users", "Lock", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "HistoryAnalysisSection", "darkMode", "targetClassName", "currentClassData", "onImport", "currentDiagramText", "_s", "historyItems", "currentUser", "t", "isExpanded", "setIsExpanded", "matches", "setMatches", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedMatches", "Set", "selectedAttributes", "setSelectedAttributes", "Map", "selectedMethods", "setSelectedMethods", "sortOption", "setSortOption", "getSortOptions", "showPreview", "setShowPreview", "conflicts", "setConflicts", "attributes", "methods", "previewData", "setPreviewData", "foundMatches", "findMatchingDiagrams", "uid", "sortedMatches", "sortMatches", "selectedMatchObjects", "filter", "match", "has", "historyItem", "id", "selectedClasses", "flatMap", "matchingClasses", "length", "detectedConflicts", "detectConflicts", "handleToggleExpand", "handleMatchSelection", "matchId", "selected", "newSelection", "add", "delete", "newSelectedAttributes", "newSelectedMethods", "handleAttributeSelection", "attribute", "currentAttributes", "get", "size", "set", "handleMethodSelection", "method", "currentMethods", "isAllSelectedForMatch", "matchingClass", "selected<PERSON>tt<PERSON>", "selectedMethodsSet", "allAttrsSelected", "every", "attr", "allMethodsSelected", "handleCheckAll", "checked", "prev", "newMap", "handleImport", "attributesToImport", "methodsToImport", "for<PERSON>ach", "includes", "push", "importedData", "name", "handlePreview", "find", "m", "getAccessIcon", "accessLevel", "getAccessLevel", "title", "children", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getSectionStyles", "container", "marginBottom", "border", "borderRadius", "background", "<PERSON><PERSON>ilter", "WebkitBackdropFilter", "header", "display", "alignItems", "justifyContent", "padding", "cursor", "borderBottom", "headerLeft", "gap", "fontSize", "fontWeight", "margin", "badge", "backgroundColor", "min<PERSON><PERSON><PERSON>", "textAlign", "expandIcon", "transition", "transform", "content", "maxHeight", "overflow", "sortContainer", "sortLabel", "sortSelect", "matchesList", "overflowY", "matchItem", "flexDirection", "matchItemHeader", "width", "checkbox", "height", "accentColor", "matchInfo", "flex", "matchTitle", "matchMeta", "similarityBadge", "actionButtons", "actionButton", "conflictsWarning", "conflictsText", "importButton", "emptyState", "fontStyle", "previewContainer", "marginTop", "animation", "previewHeader", "previewContent", "gridTemplateColumns", "previewSection", "previewLabel", "relatedClassesList", "flexWrap", "relatedClassTag", "granularSelection", "granularHeader", "checkAllLabel", "checkAllCheckbox", "granularTitle", "granular<PERSON>ontent", "granularSection", "granularSectionTitle", "textTransform", "letterSpacing", "granularItems", "paddingLeft", "granularItem", "granularCheckbox", "granularItemText", "fontFamily", "styles", "style", "onClick", "replace", "value", "key", "direction", "onChange", "e", "target", "split", "option", "opt", "map", "label", "_match$matchingClasse", "_previewData$previewD", "_previewData$previewD2", "_previewData$previewD3", "_match$matchingClasse2", "_match$matchingClasse3", "className", "type", "createdAt", "toLocaleDateString", "Math", "round", "similarity", "isShared", "classContext", "relatedClasses", "idx", "onMouseEnter", "currentTarget", "onMouseLeave", "_selectedAttributes$g", "_selectedMethods$get", "disabled", "totalAttributes", "Array", "from", "values", "reduce", "sum", "attrs", "totalMethods", "totalElements", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/FixTorchUMLDGM/uml-detector/src/components/imageUp/HistoryAnalysisSection.tsx"], "sourcesContent": ["// HistoryAnalysisSection.tsx - Composant pour l'analyse historique et l'import de classes\nimport React, { useState, useEffect } from 'react';\nimport { useHistory } from '../../context/HistoryContext';\nimport { useAuth } from '../../context/AuthContext';\nimport { useLanguage } from '../../context/LanguageContext';\nimport { HistoryAnalysisService, HistoryMatch, SortOption, ClassData } from '../../services/HistoryAnalysisService';\nimport { ChevronDown, Eye, Clock, Zap, AlertTriangle, Shield, Users, Lock } from 'lucide-react';\nimport './HistoryAnalysisSection.css';\n\ninterface HistoryAnalysisSectionProps {\n  darkMode: boolean;\n  targetClassName: string;\n  currentClassData: ClassData;\n  onImport: (importedData: ClassData) => void;\n  currentDiagramText?: string; // Texte du diagramme actuel pour l'exclure\n}\n\nconst HistoryAnalysisSection: React.FC<HistoryAnalysisSectionProps> = ({\n  darkMode,\n  targetClassName,\n  currentClassData,\n  onImport,\n  currentDiagramText\n}) => {\n  const { historyItems } = useHistory();\n  const { currentUser } = useAuth();\n  const { t } = useLanguage();\n  const [isExpanded, setIsExpanded] = useState(false);\n  const [matches, setMatches] = useState<HistoryMatch[]>([]);\n  const [selectedMatches, setSelectedMatches] = useState<Set<string>>(new Set());\n  const [selectedAttributes, setSelectedAttributes] = useState<Map<string, Set<string>>>(new Map());\n  const [selectedMethods, setSelectedMethods] = useState<Map<string, Set<string>>>(new Map());\n  const [sortOption, setSortOption] = useState<SortOption>(HistoryAnalysisService.getSortOptions()[0]);\n  const [showPreview, setShowPreview] = useState<string | null>(null);\n  const [conflicts, setConflicts] = useState<{ attributes: string[], methods: string[] }>({ attributes: [], methods: [] });\n  const [previewData, setPreviewData] = useState<HistoryMatch | null>(null);\n\n  // Rechercher les correspondances lors du changement de classe cible\n  useEffect(() => {\n    if (!targetClassName || !currentUser) return;\n    \n    const foundMatches = HistoryAnalysisService.findMatchingDiagrams(\n      targetClassName,\n      historyItems,\n      currentUser.uid,\n      currentDiagramText\n    );\n    \n    const sortedMatches = HistoryAnalysisService.sortMatches(foundMatches, sortOption);\n    setMatches(sortedMatches);\n  }, [targetClassName, historyItems, currentUser, sortOption, currentDiagramText]);\n\n  // Détecter les conflits lors du changement de sélection\n  useEffect(() => {\n    const selectedMatchObjects = matches.filter(match => selectedMatches.has(match.historyItem.id));\n    const selectedClasses = selectedMatchObjects.flatMap(match => match.matchingClasses);\n    \n    if (selectedClasses.length > 0) {\n      const detectedConflicts = HistoryAnalysisService.detectConflicts(currentClassData, selectedClasses);\n      setConflicts(detectedConflicts);\n    } else {\n      setConflicts({ attributes: [], methods: [] });\n    }\n  }, [selectedMatches, matches, currentClassData]);\n\n  const handleToggleExpand = () => {\n    setIsExpanded(!isExpanded);\n  };\n\n  const handleMatchSelection = (matchId: string, selected: boolean) => {\n    const newSelection = new Set(selectedMatches);\n    if (selected) {\n      newSelection.add(matchId);\n    } else {\n      newSelection.delete(matchId);\n      // Nettoyer les sélections d'attributs/méthodes pour ce match\n      const newSelectedAttributes = new Map(selectedAttributes);\n      const newSelectedMethods = new Map(selectedMethods);\n      newSelectedAttributes.delete(matchId);\n      newSelectedMethods.delete(matchId);\n      setSelectedAttributes(newSelectedAttributes);\n      setSelectedMethods(newSelectedMethods);\n    }\n    setSelectedMatches(newSelection);\n  };\n\n  const handleAttributeSelection = (matchId: string, attribute: string, selected: boolean) => {\n    const newSelectedAttributes = new Map(selectedAttributes);\n    const currentAttributes = newSelectedAttributes.get(matchId) || new Set();\n\n    if (selected) {\n      currentAttributes.add(attribute);\n    } else {\n      currentAttributes.delete(attribute);\n    }\n\n    if (currentAttributes.size > 0) {\n      newSelectedAttributes.set(matchId, currentAttributes);\n    } else {\n      newSelectedAttributes.delete(matchId);\n    }\n\n    setSelectedAttributes(newSelectedAttributes);\n  };\n\n  const handleMethodSelection = (matchId: string, method: string, selected: boolean) => {\n    const newSelectedMethods = new Map(selectedMethods);\n    const currentMethods = newSelectedMethods.get(matchId) || new Set();\n\n    if (selected) {\n      currentMethods.add(method);\n    } else {\n      currentMethods.delete(method);\n    }\n\n    if (currentMethods.size > 0) {\n      newSelectedMethods.set(matchId, currentMethods);\n    } else {\n      newSelectedMethods.delete(matchId);\n    }\n\n    setSelectedMethods(newSelectedMethods);\n  };\n\n  // Fonction pour vérifier si tous les éléments d'un match sont sélectionnés\n  const isAllSelectedForMatch = (match: HistoryMatch): boolean => {\n    const matchId = match.historyItem.id;\n    const matchingClass = match.matchingClasses[0];\n\n    if (!matchingClass) return false;\n\n    const selectedAttrs = selectedAttributes.get(matchId) || new Set<string>();\n    const selectedMethodsSet = selectedMethods.get(matchId) || new Set<string>();\n\n    const allAttrsSelected = matchingClass.attributes.every(attr => selectedAttrs.has(attr));\n    const allMethodsSelected = matchingClass.methods.every(method => selectedMethodsSet.has(method));\n\n    return allAttrsSelected && allMethodsSelected &&\n           (matchingClass.attributes.length > 0 || matchingClass.methods.length > 0);\n  };\n\n  // Fonction pour cocher/décocher tous les éléments d'un match\n  const handleCheckAll = (match: HistoryMatch, checked: boolean) => {\n    const matchId = match.historyItem.id;\n    const matchingClass = match.matchingClasses[0];\n\n    if (!matchingClass) return;\n\n    if (checked) {\n      // Cocher tous les attributs et méthodes\n      setSelectedAttributes(prev => {\n        const newMap = new Map(prev);\n        newMap.set(matchId, new Set(matchingClass.attributes));\n        return newMap;\n      });\n\n      setSelectedMethods(prev => {\n        const newMap = new Map(prev);\n        newMap.set(matchId, new Set(matchingClass.methods));\n        return newMap;\n      });\n    } else {\n      // Décocher tous les attributs et méthodes\n      setSelectedAttributes(prev => {\n        const newMap = new Map(prev);\n        newMap.set(matchId, new Set<string>());\n        return newMap;\n      });\n\n      setSelectedMethods(prev => {\n        const newMap = new Map(prev);\n        newMap.set(matchId, new Set<string>());\n        return newMap;\n      });\n    }\n  };\n\n  const handleImport = () => {\n    // Construire les données à importer basées sur les sélections granulaires\n    const attributesToImport: string[] = [];\n    const methodsToImport: string[] = [];\n\n    // Collecter tous les attributs et méthodes sélectionnés\n    selectedAttributes.forEach((attributes, matchId) => {\n      attributes.forEach(attr => {\n        if (!attributesToImport.includes(attr)) {\n          attributesToImport.push(attr);\n        }\n      });\n    });\n\n    selectedMethods.forEach((methods, matchId) => {\n      methods.forEach(method => {\n        if (!methodsToImport.includes(method)) {\n          methodsToImport.push(method);\n        }\n      });\n    });\n\n    if (attributesToImport.length > 0 || methodsToImport.length > 0) {\n      const importedData: ClassData = {\n        name: currentClassData.name,\n        attributes: attributesToImport,\n        methods: methodsToImport\n      };\n\n      onImport(importedData);\n\n      // Reset toutes les sélections\n      setSelectedMatches(new Set());\n      setSelectedAttributes(new Map());\n      setSelectedMethods(new Map());\n    }\n  };\n\n  const handlePreview = (matchId: string) => {\n    if (showPreview === matchId) {\n      setShowPreview(null);\n      setPreviewData(null);\n    } else {\n      const match = matches.find(m => m.historyItem.id === matchId);\n      setShowPreview(matchId);\n      setPreviewData(match || null);\n    }\n  };\n\n  const getAccessIcon = (match: HistoryMatch) => {\n    const accessLevel = HistoryAnalysisService.getAccessLevel(match.historyItem, currentUser?.uid || '');\n\n    switch (accessLevel) {\n      case 'owner':\n        return <span title={t('historyAnalysis.access.owner')}><Shield size={12} color=\"#10b981\" /></span>;\n      case 'shared':\n        return <span title={t('historyAnalysis.access.shared')}><Users size={12} color=\"#f59e0b\" /></span>;\n      case 'public':\n        return <span title={t('historyAnalysis.access.public')}><Eye size={12} color=\"#3b82f6\" /></span>;\n      default:\n        return <span title={t('historyAnalysis.access.restricted')}><Lock size={12} color=\"#ef4444\" /></span>;\n    }\n  };\n\n  const getSectionStyles = () => ({\n    container: {\n      marginBottom: '24px',\n      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)'}`,\n      borderRadius: '12px',\n      background: darkMode \n        ? 'linear-gradient(135deg, rgba(10, 15, 28, 0.8) 0%, rgba(30, 41, 59, 0.6) 100%)' \n        : 'linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(255, 255, 255, 0.9) 100%)',\n      backdropFilter: 'blur(10px)',\n      WebkitBackdropFilter: 'blur(10px)',\n    },\n    header: {\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'space-between',\n      padding: '16px 20px',\n      cursor: 'pointer',\n      borderBottom: isExpanded ? `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.15)' : 'rgba(59, 130, 246, 0.08)'}` : 'none',\n    },\n    headerLeft: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '12px',\n    },\n    title: {\n      fontSize: '16px',\n      fontWeight: '600',\n      color: darkMode ? '#f8fafc' : '#0f172a',\n      margin: 0,\n    },\n    badge: {\n      backgroundColor: matches.length > 0 ? '#3b82f6' : darkMode ? '#64748b' : '#94a3b8',\n      color: '#ffffff',\n      fontSize: '12px',\n      fontWeight: '600',\n      padding: '4px 8px',\n      borderRadius: '12px',\n      minWidth: '20px',\n      textAlign: 'center' as const,\n    },\n    expandIcon: {\n      color: darkMode ? '#94a3b8' : '#64748b',\n      transition: 'transform 0.2s ease',\n      transform: isExpanded ? 'rotate(180deg)' : 'rotate(0deg)',\n    },\n    content: {\n      padding: isExpanded ? '20px' : '0',\n      maxHeight: isExpanded ? '400px' : '0',\n      overflow: 'hidden' as const,\n      transition: 'all 0.3s ease',\n    },\n    sortContainer: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '12px',\n      marginBottom: '16px',\n    },\n    sortLabel: {\n      fontSize: '14px',\n      color: darkMode ? '#cbd5e1' : '#64748b',\n      fontWeight: '500',\n    },\n    sortSelect: {\n      backgroundColor: darkMode ? 'rgba(30, 41, 59, 0.8)' : 'rgba(248, 250, 252, 0.8)',\n      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)'}`,\n      borderRadius: '8px',\n      padding: '6px 12px',\n      fontSize: '14px',\n      color: darkMode ? '#e2e8f0' : '#374151',\n      cursor: 'pointer',\n    },\n    matchesList: {\n      maxHeight: '250px',\n      overflowY: 'auto' as const,\n      marginBottom: '16px',\n    },\n    matchItem: {\n      display: 'flex',\n      alignItems: 'flex-start',\n      flexDirection: 'column' as const,\n      gap: '12px',\n      padding: '12px',\n      marginBottom: '8px',\n      backgroundColor: darkMode ? 'rgba(30, 41, 59, 0.4)' : 'rgba(248, 250, 252, 0.6)',\n      borderRadius: '8px',\n      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.1)' : 'rgba(59, 130, 246, 0.05)'}`,\n      transition: 'all 0.2s ease',\n    },\n    matchItemHeader: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '12px',\n      width: '100%',\n    },\n    checkbox: {\n      width: '16px',\n      height: '16px',\n      accentColor: '#3b82f6',\n      cursor: 'pointer',\n    },\n    matchInfo: {\n      flex: 1,\n    },\n    matchTitle: {\n      fontSize: '14px',\n      fontWeight: '500',\n      color: darkMode ? '#e2e8f0' : '#374151',\n      marginBottom: '4px',\n    },\n    matchMeta: {\n      fontSize: '12px',\n      color: darkMode ? '#94a3b8' : '#64748b',\n      display: 'flex',\n      alignItems: 'center',\n      gap: '8px',\n    },\n    similarityBadge: {\n      backgroundColor: '#10b981',\n      color: '#ffffff',\n      fontSize: '11px',\n      fontWeight: '600',\n      padding: '2px 6px',\n      borderRadius: '8px',\n    },\n    actionButtons: {\n      display: 'flex',\n      gap: '8px',\n    },\n    actionButton: {\n      backgroundColor: 'transparent',\n      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.3)' : 'rgba(59, 130, 246, 0.2)'}`,\n      borderRadius: '6px',\n      padding: '6px',\n      cursor: 'pointer',\n      color: darkMode ? '#60a5fa' : '#3b82f6',\n      transition: 'all 0.2s ease',\n    },\n    conflictsWarning: {\n      backgroundColor: darkMode ? 'rgba(245, 158, 11, 0.1)' : 'rgba(245, 158, 11, 0.05)',\n      border: `1px solid ${darkMode ? 'rgba(245, 158, 11, 0.3)' : 'rgba(245, 158, 11, 0.2)'}`,\n      borderRadius: '8px',\n      padding: '12px',\n      marginBottom: '16px',\n      display: 'flex',\n      alignItems: 'center',\n      gap: '8px',\n    },\n    conflictsText: {\n      fontSize: '13px',\n      color: darkMode ? '#fbbf24' : '#d97706',\n      fontWeight: '500',\n    },\n    importButton: {\n      backgroundColor: selectedMatches.size > 0 ? '#3b82f6' : darkMode ? '#374151' : '#9ca3af',\n      color: '#ffffff',\n      border: 'none',\n      borderRadius: '8px',\n      padding: '10px 16px',\n      fontSize: '14px',\n      fontWeight: '600',\n      cursor: selectedMatches.size > 0 ? 'pointer' : 'not-allowed',\n      transition: 'all 0.2s ease',\n      display: 'flex',\n      alignItems: 'center',\n      gap: '8px',\n    },\n    emptyState: {\n      textAlign: 'center' as const,\n      color: darkMode ? '#64748b' : '#9ca3af',\n      fontSize: '14px',\n      fontStyle: 'italic',\n      padding: '20px',\n    },\n    previewContainer: {\n      backgroundColor: darkMode ? 'rgba(15, 23, 42, 0.8)' : 'rgba(248, 250, 252, 0.9)',\n      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)'}`,\n      borderRadius: '8px',\n      padding: '16px',\n      marginTop: '12px',\n      animation: 'slideDown 0.3s ease',\n    },\n    previewHeader: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '8px',\n      marginBottom: '12px',\n      fontSize: '14px',\n      fontWeight: '600',\n      color: darkMode ? '#e2e8f0' : '#374151',\n    },\n    previewContent: {\n      display: 'grid',\n      gridTemplateColumns: '1fr 1fr',\n      gap: '16px',\n    },\n    previewSection: {\n      fontSize: '13px',\n      color: darkMode ? '#cbd5e1' : '#64748b',\n    },\n    previewLabel: {\n      fontWeight: '600',\n      marginBottom: '4px',\n      color: darkMode ? '#f1f5f9' : '#374151',\n    },\n    relatedClassesList: {\n      display: 'flex',\n      flexWrap: 'wrap' as const,\n      gap: '4px',\n      marginTop: '4px',\n    },\n    relatedClassTag: {\n      backgroundColor: darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)',\n      color: darkMode ? '#93c5fd' : '#3b82f6',\n      fontSize: '11px',\n      padding: '2px 6px',\n      borderRadius: '4px',\n      fontWeight: '500',\n    },\n\n    // Styles pour la sélection granulaire\n    granularSelection: {\n      marginTop: '12px',\n      padding: '12px',\n      backgroundColor: darkMode ? 'rgba(59, 130, 246, 0.05)' : 'rgba(59, 130, 246, 0.02)',\n      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.1)' : 'rgba(59, 130, 246, 0.08)'}`,\n      borderRadius: '8px',\n    },\n\n    granularHeader: {\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'space-between',\n      fontSize: '13px',\n      fontWeight: '600',\n      color: darkMode ? '#e2e8f0' : '#374151',\n      marginBottom: '8px',\n    },\n\n    checkAllLabel: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '6px',\n      fontSize: '12px',\n      fontWeight: '500',\n      color: darkMode ? '#60a5fa' : '#3b82f6', // Couleur bleue harmonisée avec le thème\n      cursor: 'pointer',\n      transition: 'color 0.2s ease',\n    },\n\n    checkAllCheckbox: {\n      width: '14px',\n      height: '14px',\n      cursor: 'pointer',\n      accentColor: darkMode ? '#60a5fa' : '#3b82f6', // Couleur de la checkbox harmonisée\n    },\n\n    granularTitle: {\n      fontSize: '13px',\n      fontWeight: '600',\n      color: darkMode ? '#e2e8f0' : '#374151',\n    },\n\n    granularContent: {\n      display: 'flex',\n      flexDirection: 'column' as const,\n      gap: '12px',\n    },\n\n    granularSection: {\n      display: 'flex',\n      flexDirection: 'column' as const,\n      gap: '6px',\n    },\n\n    granularSectionTitle: {\n      fontSize: '12px',\n      fontWeight: '600',\n      color: darkMode ? '#94a3b8' : '#6b7280',\n      textTransform: 'uppercase' as const,\n      letterSpacing: '0.5px',\n    },\n\n    granularItems: {\n      display: 'flex',\n      flexDirection: 'column' as const,\n      gap: '4px',\n      paddingLeft: '8px',\n    },\n\n    granularItem: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '8px',\n      cursor: 'pointer',\n      padding: '4px 0',\n    },\n\n    granularCheckbox: {\n      width: '14px',\n      height: '14px',\n      cursor: 'pointer',\n    },\n\n    granularItemText: {\n      fontSize: '12px',\n      color: darkMode ? '#cbd5e1' : '#4b5563',\n      fontFamily: 'monospace',\n    }\n  });\n\n  const styles = getSectionStyles();\n\n  if (!currentUser) return null;\n\n  return (\n    <div style={styles.container}>\n      <div style={styles.header} onClick={handleToggleExpand}>\n        <div style={styles.headerLeft}>\n          <Zap size={18} color={darkMode ? '#60a5fa' : '#3b82f6'} />\n          <h4 style={styles.title}>{t('historyAnalysis.title')}</h4>\n          <span style={styles.badge}>{matches.length}</span>\n        </div>\n        <ChevronDown size={20} style={styles.expandIcon} />\n      </div>\n      \n      {isExpanded && (\n        <div style={styles.content}>\n          {matches.length === 0 ? (\n            <div style={styles.emptyState}>\n{t('historyAnalysis.noResults').replace('{className}', targetClassName)}\n            </div>\n          ) : (\n            <>\n              <div style={styles.sortContainer}>\n                <span style={styles.sortLabel}>{t('historyAnalysis.sortBy')}</span>\n                <select \n                  style={styles.sortSelect}\n                  value={`${sortOption.key}-${sortOption.direction}`}\n                  onChange={(e) => {\n                    const [key, direction] = e.target.value.split('-');\n                    const option = HistoryAnalysisService.getSortOptions().find(\n                      opt => opt.key === key && opt.direction === direction\n                    );\n                    if (option) setSortOption(option);\n                  }}\n                >\n                  {HistoryAnalysisService.getSortOptions().map(option => (\n                    <option key={`${option.key}-${option.direction}`} value={`${option.key}-${option.direction}`}>\n                      {option.label}\n                    </option>\n                  ))}\n                </select>\n              </div>\n\n              {conflicts.attributes.length > 0 || conflicts.methods.length > 0 ? (\n                <div style={styles.conflictsWarning}>\n                  <AlertTriangle size={16} />\n                  <span style={styles.conflictsText}>\n                    Conflits détectés: {conflicts.attributes.length} attribut(s), {conflicts.methods.length} méthode(s)\n                  </span>\n                </div>\n              ) : null}\n\n              <div style={styles.matchesList}>\n                {matches.map(match => (\n                  <div key={match.historyItem.id} style={styles.matchItem} className=\"history-match-item\">\n                    <div style={styles.matchItemHeader}>\n                      <input\n                        type=\"checkbox\"\n                        style={styles.checkbox}\n                        checked={selectedMatches.has(match.historyItem.id)}\n                        onChange={(e) => handleMatchSelection(match.historyItem.id, e.target.checked)}\n                      />\n                      <div style={styles.matchInfo}>\n                        <div style={styles.matchTitle}>{match.historyItem.title}</div>\n                        <div style={styles.matchMeta} className=\"history-match-meta\">\n                          <Clock size={12} />\n                          {match.historyItem.createdAt.toLocaleDateString('fr-FR')}\n                          <span style={styles.similarityBadge} className=\"history-similarity-badge\">\n                            {Math.round(match.similarity)}%\n                          </span>\n                          {getAccessIcon(match)}\n                          {match.isShared && <span style={{ fontSize: '11px', color: darkMode ? '#fbbf24' : '#d97706' }}>Partagé</span>}\n                        </div>\n                      </div>\n                      <div style={styles.actionButtons}>\n                        <button\n                          style={styles.actionButton}\n                          className=\"history-action-button\"\n                          onClick={() => handlePreview(match.historyItem.id)}\n                          title=\"Voir aperçu\"\n                        >\n                          <Eye size={14} />\n                        </button>\n                      </div>\n                    </div>\n\n                    {/* Aperçu de la classe */}\n                    {showPreview === match.historyItem.id && previewData && (\n                      <div style={styles.previewContainer} className=\"history-preview-container\">\n                        <div style={styles.previewHeader}>\n                          <Eye size={16} />\n                          Aperçu de la classe \"{match.matchingClasses[0]?.name}\"\n                        </div>\n                        <div style={styles.previewContent} className=\"history-preview-content\">\n                          <div style={styles.previewSection}>\n                            <div style={styles.previewLabel}>Contenu de la classe:</div>\n                            <div>{previewData.previewData?.classContext}</div>\n                          </div>\n                          <div style={styles.previewSection}>\n                            <div style={styles.previewLabel}>Classes liées:</div>\n                            <div style={styles.relatedClassesList} className=\"history-related-classes-list\">\n                              {previewData.previewData?.relatedClasses.map((className, idx) => (\n                                <span key={idx} style={styles.relatedClassTag}>\n                                  {className}\n                                </span>\n                              ))}\n                              {(!previewData.previewData?.relatedClasses || previewData.previewData.relatedClasses.length === 0) && (\n                                <span style={{ fontStyle: 'italic', color: darkMode ? '#64748b' : '#9ca3af' }}>\n                                  Aucune classe liée détectée\n                                </span>\n                              )}\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    )}\n\n                    {/* Sélection granulaire des attributs et méthodes */}\n                    {selectedMatches.has(match.historyItem.id) && (\n                      <div style={styles.granularSelection} className=\"history-granular-selection\">\n                        <div style={styles.granularHeader}>\n                          <span style={styles.granularTitle}>Sélectionner les éléments à importer :</span>\n                          <label\n                            style={styles.checkAllLabel}\n                            onMouseEnter={(e) => {\n                              e.currentTarget.style.color = darkMode ? '#93c5fd' : '#2563eb';\n                            }}\n                            onMouseLeave={(e) => {\n                              e.currentTarget.style.color = darkMode ? '#60a5fa' : '#3b82f6';\n                            }}\n                          >\n                            <input\n                              type=\"checkbox\"\n                              checked={isAllSelectedForMatch(match)}\n                              onChange={(e) => handleCheckAll(match, e.target.checked)}\n                              style={styles.checkAllCheckbox}\n                            />\n                            <span>Cocher tous</span>\n                          </label>\n                        </div>\n\n                        <div style={styles.granularContent}>\n                          {/* Attributs */}\n                          {match.matchingClasses[0]?.attributes && match.matchingClasses[0].attributes.length > 0 && (\n                            <div style={styles.granularSection}>\n                              <div style={styles.granularSectionTitle}>Attributs :</div>\n                              <div style={styles.granularItems}>\n                                {match.matchingClasses[0].attributes.map((attribute, idx) => (\n                                  <label key={idx} style={styles.granularItem}>\n                                    <input\n                                      type=\"checkbox\"\n                                      style={styles.granularCheckbox}\n                                      checked={selectedAttributes.get(match.historyItem.id)?.has(attribute) || false}\n                                      onChange={(e) => handleAttributeSelection(match.historyItem.id, attribute, e.target.checked)}\n                                    />\n                                    <span style={styles.granularItemText}>{attribute}</span>\n                                  </label>\n                                ))}\n                              </div>\n                            </div>\n                          )}\n\n                          {/* Méthodes */}\n                          {match.matchingClasses[0]?.methods && match.matchingClasses[0].methods.length > 0 && (\n                            <div style={styles.granularSection}>\n                              <div style={styles.granularSectionTitle}>Méthodes :</div>\n                              <div style={styles.granularItems}>\n                                {match.matchingClasses[0].methods.map((method, idx) => (\n                                  <label key={idx} style={styles.granularItem}>\n                                    <input\n                                      type=\"checkbox\"\n                                      style={styles.granularCheckbox}\n                                      checked={selectedMethods.get(match.historyItem.id)?.has(method) || false}\n                                      onChange={(e) => handleMethodSelection(match.historyItem.id, method, e.target.checked)}\n                                    />\n                                    <span style={styles.granularItemText}>{method}</span>\n                                  </label>\n                                ))}\n                              </div>\n                            </div>\n                          )}\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                ))}\n              </div>\n\n              <button\n                style={styles.importButton}\n                onClick={handleImport}\n                disabled={(() => {\n                  const totalAttributes = Array.from(selectedAttributes.values()).reduce((sum, attrs) => sum + attrs.size, 0);\n                  const totalMethods = Array.from(selectedMethods.values()).reduce((sum, methods) => sum + methods.size, 0);\n                  return totalAttributes + totalMethods === 0;\n                })()}\n              >\n                <Zap size={16} />\n                {(() => {\n                  const totalAttributes = Array.from(selectedAttributes.values()).reduce((sum, attrs) => sum + attrs.size, 0);\n                  const totalMethods = Array.from(selectedMethods.values()).reduce((sum, methods) => sum + methods.size, 0);\n                  const totalElements = totalAttributes + totalMethods;\n\n                  if (totalElements === 0) {\n                    return `Importer (${selectedMatches.size} diagramme${selectedMatches.size > 1 ? 's' : ''} sélectionné${selectedMatches.size > 1 ? 's' : ''})`;\n                  }\n\n                  return `Importer (${totalElements} élément${totalElements > 1 ? 's' : ''} sélectionné${totalElements > 1 ? 's' : ''})`;\n                })()}\n              </button>\n            </>\n          )}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default HistoryAnalysisSection;\n"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,UAAU,QAAQ,8BAA8B;AACzD,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,WAAW,QAAQ,+BAA+B;AAC3D,SAASC,sBAAsB,QAA6C,uCAAuC;AACnH,SAASC,WAAW,EAAEC,GAAG,EAAEC,KAAK,EAAEC,GAAG,EAAEC,aAAa,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,QAAQ,cAAc;AAC/F,OAAO,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAUtC,MAAMC,sBAA6D,GAAGA,CAAC;EACrEC,QAAQ;EACRC,eAAe;EACfC,gBAAgB;EAChBC,QAAQ;EACRC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM;IAAEC;EAAa,CAAC,GAAGvB,UAAU,CAAC,CAAC;EACrC,MAAM;IAAEwB;EAAY,CAAC,GAAGvB,OAAO,CAAC,CAAC;EACjC,MAAM;IAAEwB;EAAE,CAAC,GAAGvB,WAAW,CAAC,CAAC;EAC3B,MAAM,CAACwB,UAAU,EAAEC,aAAa,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC8B,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAiB,EAAE,CAAC;EAC1D,MAAM,CAACgC,eAAe,EAAEC,kBAAkB,CAAC,GAAGjC,QAAQ,CAAc,IAAIkC,GAAG,CAAC,CAAC,CAAC;EAC9E,MAAM,CAACC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGpC,QAAQ,CAA2B,IAAIqC,GAAG,CAAC,CAAC,CAAC;EACjG,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGvC,QAAQ,CAA2B,IAAIqC,GAAG,CAAC,CAAC,CAAC;EAC3F,MAAM,CAACG,UAAU,EAAEC,aAAa,CAAC,GAAGzC,QAAQ,CAAaK,sBAAsB,CAACqC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpG,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG5C,QAAQ,CAAgB,IAAI,CAAC;EACnE,MAAM,CAAC6C,SAAS,EAAEC,YAAY,CAAC,GAAG9C,QAAQ,CAA8C;IAAE+C,UAAU,EAAE,EAAE;IAAEC,OAAO,EAAE;EAAG,CAAC,CAAC;EACxH,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGlD,QAAQ,CAAsB,IAAI,CAAC;;EAEzE;EACAC,SAAS,CAAC,MAAM;IACd,IAAI,CAACmB,eAAe,IAAI,CAACM,WAAW,EAAE;IAEtC,MAAMyB,YAAY,GAAG9C,sBAAsB,CAAC+C,oBAAoB,CAC9DhC,eAAe,EACfK,YAAY,EACZC,WAAW,CAAC2B,GAAG,EACf9B,kBACF,CAAC;IAED,MAAM+B,aAAa,GAAGjD,sBAAsB,CAACkD,WAAW,CAACJ,YAAY,EAAEX,UAAU,CAAC;IAClFT,UAAU,CAACuB,aAAa,CAAC;EAC3B,CAAC,EAAE,CAAClC,eAAe,EAAEK,YAAY,EAAEC,WAAW,EAAEc,UAAU,EAAEjB,kBAAkB,CAAC,CAAC;;EAEhF;EACAtB,SAAS,CAAC,MAAM;IACd,MAAMuD,oBAAoB,GAAG1B,OAAO,CAAC2B,MAAM,CAACC,KAAK,IAAI1B,eAAe,CAAC2B,GAAG,CAACD,KAAK,CAACE,WAAW,CAACC,EAAE,CAAC,CAAC;IAC/F,MAAMC,eAAe,GAAGN,oBAAoB,CAACO,OAAO,CAACL,KAAK,IAAIA,KAAK,CAACM,eAAe,CAAC;IAEpF,IAAIF,eAAe,CAACG,MAAM,GAAG,CAAC,EAAE;MAC9B,MAAMC,iBAAiB,GAAG7D,sBAAsB,CAAC8D,eAAe,CAAC9C,gBAAgB,EAAEyC,eAAe,CAAC;MACnGhB,YAAY,CAACoB,iBAAiB,CAAC;IACjC,CAAC,MAAM;MACLpB,YAAY,CAAC;QAAEC,UAAU,EAAE,EAAE;QAAEC,OAAO,EAAE;MAAG,CAAC,CAAC;IAC/C;EACF,CAAC,EAAE,CAAChB,eAAe,EAAEF,OAAO,EAAET,gBAAgB,CAAC,CAAC;EAEhD,MAAM+C,kBAAkB,GAAGA,CAAA,KAAM;IAC/BvC,aAAa,CAAC,CAACD,UAAU,CAAC;EAC5B,CAAC;EAED,MAAMyC,oBAAoB,GAAGA,CAACC,OAAe,EAAEC,QAAiB,KAAK;IACnE,MAAMC,YAAY,GAAG,IAAItC,GAAG,CAACF,eAAe,CAAC;IAC7C,IAAIuC,QAAQ,EAAE;MACZC,YAAY,CAACC,GAAG,CAACH,OAAO,CAAC;IAC3B,CAAC,MAAM;MACLE,YAAY,CAACE,MAAM,CAACJ,OAAO,CAAC;MAC5B;MACA,MAAMK,qBAAqB,GAAG,IAAItC,GAAG,CAACF,kBAAkB,CAAC;MACzD,MAAMyC,kBAAkB,GAAG,IAAIvC,GAAG,CAACC,eAAe,CAAC;MACnDqC,qBAAqB,CAACD,MAAM,CAACJ,OAAO,CAAC;MACrCM,kBAAkB,CAACF,MAAM,CAACJ,OAAO,CAAC;MAClClC,qBAAqB,CAACuC,qBAAqB,CAAC;MAC5CpC,kBAAkB,CAACqC,kBAAkB,CAAC;IACxC;IACA3C,kBAAkB,CAACuC,YAAY,CAAC;EAClC,CAAC;EAED,MAAMK,wBAAwB,GAAGA,CAACP,OAAe,EAAEQ,SAAiB,EAAEP,QAAiB,KAAK;IAC1F,MAAMI,qBAAqB,GAAG,IAAItC,GAAG,CAACF,kBAAkB,CAAC;IACzD,MAAM4C,iBAAiB,GAAGJ,qBAAqB,CAACK,GAAG,CAACV,OAAO,CAAC,IAAI,IAAIpC,GAAG,CAAC,CAAC;IAEzE,IAAIqC,QAAQ,EAAE;MACZQ,iBAAiB,CAACN,GAAG,CAACK,SAAS,CAAC;IAClC,CAAC,MAAM;MACLC,iBAAiB,CAACL,MAAM,CAACI,SAAS,CAAC;IACrC;IAEA,IAAIC,iBAAiB,CAACE,IAAI,GAAG,CAAC,EAAE;MAC9BN,qBAAqB,CAACO,GAAG,CAACZ,OAAO,EAAES,iBAAiB,CAAC;IACvD,CAAC,MAAM;MACLJ,qBAAqB,CAACD,MAAM,CAACJ,OAAO,CAAC;IACvC;IAEAlC,qBAAqB,CAACuC,qBAAqB,CAAC;EAC9C,CAAC;EAED,MAAMQ,qBAAqB,GAAGA,CAACb,OAAe,EAAEc,MAAc,EAAEb,QAAiB,KAAK;IACpF,MAAMK,kBAAkB,GAAG,IAAIvC,GAAG,CAACC,eAAe,CAAC;IACnD,MAAM+C,cAAc,GAAGT,kBAAkB,CAACI,GAAG,CAACV,OAAO,CAAC,IAAI,IAAIpC,GAAG,CAAC,CAAC;IAEnE,IAAIqC,QAAQ,EAAE;MACZc,cAAc,CAACZ,GAAG,CAACW,MAAM,CAAC;IAC5B,CAAC,MAAM;MACLC,cAAc,CAACX,MAAM,CAACU,MAAM,CAAC;IAC/B;IAEA,IAAIC,cAAc,CAACJ,IAAI,GAAG,CAAC,EAAE;MAC3BL,kBAAkB,CAACM,GAAG,CAACZ,OAAO,EAAEe,cAAc,CAAC;IACjD,CAAC,MAAM;MACLT,kBAAkB,CAACF,MAAM,CAACJ,OAAO,CAAC;IACpC;IAEA/B,kBAAkB,CAACqC,kBAAkB,CAAC;EACxC,CAAC;;EAED;EACA,MAAMU,qBAAqB,GAAI5B,KAAmB,IAAc;IAC9D,MAAMY,OAAO,GAAGZ,KAAK,CAACE,WAAW,CAACC,EAAE;IACpC,MAAM0B,aAAa,GAAG7B,KAAK,CAACM,eAAe,CAAC,CAAC,CAAC;IAE9C,IAAI,CAACuB,aAAa,EAAE,OAAO,KAAK;IAEhC,MAAMC,aAAa,GAAGrD,kBAAkB,CAAC6C,GAAG,CAACV,OAAO,CAAC,IAAI,IAAIpC,GAAG,CAAS,CAAC;IAC1E,MAAMuD,kBAAkB,GAAGnD,eAAe,CAAC0C,GAAG,CAACV,OAAO,CAAC,IAAI,IAAIpC,GAAG,CAAS,CAAC;IAE5E,MAAMwD,gBAAgB,GAAGH,aAAa,CAACxC,UAAU,CAAC4C,KAAK,CAACC,IAAI,IAAIJ,aAAa,CAAC7B,GAAG,CAACiC,IAAI,CAAC,CAAC;IACxF,MAAMC,kBAAkB,GAAGN,aAAa,CAACvC,OAAO,CAAC2C,KAAK,CAACP,MAAM,IAAIK,kBAAkB,CAAC9B,GAAG,CAACyB,MAAM,CAAC,CAAC;IAEhG,OAAOM,gBAAgB,IAAIG,kBAAkB,KACrCN,aAAa,CAACxC,UAAU,CAACkB,MAAM,GAAG,CAAC,IAAIsB,aAAa,CAACvC,OAAO,CAACiB,MAAM,GAAG,CAAC,CAAC;EAClF,CAAC;;EAED;EACA,MAAM6B,cAAc,GAAGA,CAACpC,KAAmB,EAAEqC,OAAgB,KAAK;IAChE,MAAMzB,OAAO,GAAGZ,KAAK,CAACE,WAAW,CAACC,EAAE;IACpC,MAAM0B,aAAa,GAAG7B,KAAK,CAACM,eAAe,CAAC,CAAC,CAAC;IAE9C,IAAI,CAACuB,aAAa,EAAE;IAEpB,IAAIQ,OAAO,EAAE;MACX;MACA3D,qBAAqB,CAAC4D,IAAI,IAAI;QAC5B,MAAMC,MAAM,GAAG,IAAI5D,GAAG,CAAC2D,IAAI,CAAC;QAC5BC,MAAM,CAACf,GAAG,CAACZ,OAAO,EAAE,IAAIpC,GAAG,CAACqD,aAAa,CAACxC,UAAU,CAAC,CAAC;QACtD,OAAOkD,MAAM;MACf,CAAC,CAAC;MAEF1D,kBAAkB,CAACyD,IAAI,IAAI;QACzB,MAAMC,MAAM,GAAG,IAAI5D,GAAG,CAAC2D,IAAI,CAAC;QAC5BC,MAAM,CAACf,GAAG,CAACZ,OAAO,EAAE,IAAIpC,GAAG,CAACqD,aAAa,CAACvC,OAAO,CAAC,CAAC;QACnD,OAAOiD,MAAM;MACf,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA7D,qBAAqB,CAAC4D,IAAI,IAAI;QAC5B,MAAMC,MAAM,GAAG,IAAI5D,GAAG,CAAC2D,IAAI,CAAC;QAC5BC,MAAM,CAACf,GAAG,CAACZ,OAAO,EAAE,IAAIpC,GAAG,CAAS,CAAC,CAAC;QACtC,OAAO+D,MAAM;MACf,CAAC,CAAC;MAEF1D,kBAAkB,CAACyD,IAAI,IAAI;QACzB,MAAMC,MAAM,GAAG,IAAI5D,GAAG,CAAC2D,IAAI,CAAC;QAC5BC,MAAM,CAACf,GAAG,CAACZ,OAAO,EAAE,IAAIpC,GAAG,CAAS,CAAC,CAAC;QACtC,OAAO+D,MAAM;MACf,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB;IACA,MAAMC,kBAA4B,GAAG,EAAE;IACvC,MAAMC,eAAyB,GAAG,EAAE;;IAEpC;IACAjE,kBAAkB,CAACkE,OAAO,CAAC,CAACtD,UAAU,EAAEuB,OAAO,KAAK;MAClDvB,UAAU,CAACsD,OAAO,CAACT,IAAI,IAAI;QACzB,IAAI,CAACO,kBAAkB,CAACG,QAAQ,CAACV,IAAI,CAAC,EAAE;UACtCO,kBAAkB,CAACI,IAAI,CAACX,IAAI,CAAC;QAC/B;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFtD,eAAe,CAAC+D,OAAO,CAAC,CAACrD,OAAO,EAAEsB,OAAO,KAAK;MAC5CtB,OAAO,CAACqD,OAAO,CAACjB,MAAM,IAAI;QACxB,IAAI,CAACgB,eAAe,CAACE,QAAQ,CAAClB,MAAM,CAAC,EAAE;UACrCgB,eAAe,CAACG,IAAI,CAACnB,MAAM,CAAC;QAC9B;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,IAAIe,kBAAkB,CAAClC,MAAM,GAAG,CAAC,IAAImC,eAAe,CAACnC,MAAM,GAAG,CAAC,EAAE;MAC/D,MAAMuC,YAAuB,GAAG;QAC9BC,IAAI,EAAEpF,gBAAgB,CAACoF,IAAI;QAC3B1D,UAAU,EAAEoD,kBAAkB;QAC9BnD,OAAO,EAAEoD;MACX,CAAC;MAED9E,QAAQ,CAACkF,YAAY,CAAC;;MAEtB;MACAvE,kBAAkB,CAAC,IAAIC,GAAG,CAAC,CAAC,CAAC;MAC7BE,qBAAqB,CAAC,IAAIC,GAAG,CAAC,CAAC,CAAC;MAChCE,kBAAkB,CAAC,IAAIF,GAAG,CAAC,CAAC,CAAC;IAC/B;EACF,CAAC;EAED,MAAMqE,aAAa,GAAIpC,OAAe,IAAK;IACzC,IAAI3B,WAAW,KAAK2B,OAAO,EAAE;MAC3B1B,cAAc,CAAC,IAAI,CAAC;MACpBM,cAAc,CAAC,IAAI,CAAC;IACtB,CAAC,MAAM;MACL,MAAMQ,KAAK,GAAG5B,OAAO,CAAC6E,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAChD,WAAW,CAACC,EAAE,KAAKS,OAAO,CAAC;MAC7D1B,cAAc,CAAC0B,OAAO,CAAC;MACvBpB,cAAc,CAACQ,KAAK,IAAI,IAAI,CAAC;IAC/B;EACF,CAAC;EAED,MAAMmD,aAAa,GAAInD,KAAmB,IAAK;IAC7C,MAAMoD,WAAW,GAAGzG,sBAAsB,CAAC0G,cAAc,CAACrD,KAAK,CAACE,WAAW,EAAE,CAAAlC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE2B,GAAG,KAAI,EAAE,CAAC;IAEpG,QAAQyD,WAAW;MACjB,KAAK,OAAO;QACV,oBAAO/F,OAAA;UAAMiG,KAAK,EAAErF,CAAC,CAAC,8BAA8B,CAAE;UAAAsF,QAAA,eAAClG,OAAA,CAACJ,MAAM;YAACsE,IAAI,EAAE,EAAG;YAACiC,KAAK,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MACpG,KAAK,QAAQ;QACX,oBAAOvG,OAAA;UAAMiG,KAAK,EAAErF,CAAC,CAAC,+BAA+B,CAAE;UAAAsF,QAAA,eAAClG,OAAA,CAACH,KAAK;YAACqE,IAAI,EAAE,EAAG;YAACiC,KAAK,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MACpG,KAAK,QAAQ;QACX,oBAAOvG,OAAA;UAAMiG,KAAK,EAAErF,CAAC,CAAC,+BAA+B,CAAE;UAAAsF,QAAA,eAAClG,OAAA,CAACR,GAAG;YAAC0E,IAAI,EAAE,EAAG;YAACiC,KAAK,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAClG;QACE,oBAAOvG,OAAA;UAAMiG,KAAK,EAAErF,CAAC,CAAC,mCAAmC,CAAE;UAAAsF,QAAA,eAAClG,OAAA,CAACF,IAAI;YAACoE,IAAI,EAAE,EAAG;YAACiC,KAAK,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;IACzG;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,MAAO;IAC9BC,SAAS,EAAE;MACTC,YAAY,EAAE,MAAM;MACpBC,MAAM,EAAE,aAAavG,QAAQ,GAAG,yBAAyB,GAAG,yBAAyB,EAAE;MACvFwG,YAAY,EAAE,MAAM;MACpBC,UAAU,EAAEzG,QAAQ,GAChB,+EAA+E,GAC/E,qFAAqF;MACzF0G,cAAc,EAAE,YAAY;MAC5BC,oBAAoB,EAAE;IACxB,CAAC;IACDC,MAAM,EAAE;MACNC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,eAAe;MAC/BC,OAAO,EAAE,WAAW;MACpBC,MAAM,EAAE,SAAS;MACjBC,YAAY,EAAEzG,UAAU,GAAG,aAAaT,QAAQ,GAAG,0BAA0B,GAAG,0BAA0B,EAAE,GAAG;IACjH,CAAC;IACDmH,UAAU,EAAE;MACVN,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBM,GAAG,EAAE;IACP,CAAC;IACDvB,KAAK,EAAE;MACLwB,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,KAAK;MACjBvB,KAAK,EAAE/F,QAAQ,GAAG,SAAS,GAAG,SAAS;MACvCuH,MAAM,EAAE;IACV,CAAC;IACDC,KAAK,EAAE;MACLC,eAAe,EAAE9G,OAAO,CAACmC,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG9C,QAAQ,GAAG,SAAS,GAAG,SAAS;MAClF+F,KAAK,EAAE,SAAS;MAChBsB,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,KAAK;MACjBN,OAAO,EAAE,SAAS;MAClBR,YAAY,EAAE,MAAM;MACpBkB,QAAQ,EAAE,MAAM;MAChBC,SAAS,EAAE;IACb,CAAC;IACDC,UAAU,EAAE;MACV7B,KAAK,EAAE/F,QAAQ,GAAG,SAAS,GAAG,SAAS;MACvC6H,UAAU,EAAE,qBAAqB;MACjCC,SAAS,EAAErH,UAAU,GAAG,gBAAgB,GAAG;IAC7C,CAAC;IACDsH,OAAO,EAAE;MACPf,OAAO,EAAEvG,UAAU,GAAG,MAAM,GAAG,GAAG;MAClCuH,SAAS,EAAEvH,UAAU,GAAG,OAAO,GAAG,GAAG;MACrCwH,QAAQ,EAAE,QAAiB;MAC3BJ,UAAU,EAAE;IACd,CAAC;IACDK,aAAa,EAAE;MACbrB,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBM,GAAG,EAAE,MAAM;MACXd,YAAY,EAAE;IAChB,CAAC;IACD6B,SAAS,EAAE;MACTd,QAAQ,EAAE,MAAM;MAChBtB,KAAK,EAAE/F,QAAQ,GAAG,SAAS,GAAG,SAAS;MACvCsH,UAAU,EAAE;IACd,CAAC;IACDc,UAAU,EAAE;MACVX,eAAe,EAAEzH,QAAQ,GAAG,uBAAuB,GAAG,0BAA0B;MAChFuG,MAAM,EAAE,aAAavG,QAAQ,GAAG,yBAAyB,GAAG,yBAAyB,EAAE;MACvFwG,YAAY,EAAE,KAAK;MACnBQ,OAAO,EAAE,UAAU;MACnBK,QAAQ,EAAE,MAAM;MAChBtB,KAAK,EAAE/F,QAAQ,GAAG,SAAS,GAAG,SAAS;MACvCiH,MAAM,EAAE;IACV,CAAC;IACDoB,WAAW,EAAE;MACXL,SAAS,EAAE,OAAO;MAClBM,SAAS,EAAE,MAAe;MAC1BhC,YAAY,EAAE;IAChB,CAAC;IACDiC,SAAS,EAAE;MACT1B,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,YAAY;MACxB0B,aAAa,EAAE,QAAiB;MAChCpB,GAAG,EAAE,MAAM;MACXJ,OAAO,EAAE,MAAM;MACfV,YAAY,EAAE,KAAK;MACnBmB,eAAe,EAAEzH,QAAQ,GAAG,uBAAuB,GAAG,0BAA0B;MAChFwG,YAAY,EAAE,KAAK;MACnBD,MAAM,EAAE,aAAavG,QAAQ,GAAG,yBAAyB,GAAG,0BAA0B,EAAE;MACxF6H,UAAU,EAAE;IACd,CAAC;IACDY,eAAe,EAAE;MACf5B,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBM,GAAG,EAAE,MAAM;MACXsB,KAAK,EAAE;IACT,CAAC;IACDC,QAAQ,EAAE;MACRD,KAAK,EAAE,MAAM;MACbE,MAAM,EAAE,MAAM;MACdC,WAAW,EAAE,SAAS;MACtB5B,MAAM,EAAE;IACV,CAAC;IACD6B,SAAS,EAAE;MACTC,IAAI,EAAE;IACR,CAAC;IACDC,UAAU,EAAE;MACV3B,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,KAAK;MACjBvB,KAAK,EAAE/F,QAAQ,GAAG,SAAS,GAAG,SAAS;MACvCsG,YAAY,EAAE;IAChB,CAAC;IACD2C,SAAS,EAAE;MACT5B,QAAQ,EAAE,MAAM;MAChBtB,KAAK,EAAE/F,QAAQ,GAAG,SAAS,GAAG,SAAS;MACvC6G,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBM,GAAG,EAAE;IACP,CAAC;IACD8B,eAAe,EAAE;MACfzB,eAAe,EAAE,SAAS;MAC1B1B,KAAK,EAAE,SAAS;MAChBsB,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,KAAK;MACjBN,OAAO,EAAE,SAAS;MAClBR,YAAY,EAAE;IAChB,CAAC;IACD2C,aAAa,EAAE;MACbtC,OAAO,EAAE,MAAM;MACfO,GAAG,EAAE;IACP,CAAC;IACDgC,YAAY,EAAE;MACZ3B,eAAe,EAAE,aAAa;MAC9BlB,MAAM,EAAE,aAAavG,QAAQ,GAAG,yBAAyB,GAAG,yBAAyB,EAAE;MACvFwG,YAAY,EAAE,KAAK;MACnBQ,OAAO,EAAE,KAAK;MACdC,MAAM,EAAE,SAAS;MACjBlB,KAAK,EAAE/F,QAAQ,GAAG,SAAS,GAAG,SAAS;MACvC6H,UAAU,EAAE;IACd,CAAC;IACDwB,gBAAgB,EAAE;MAChB5B,eAAe,EAAEzH,QAAQ,GAAG,yBAAyB,GAAG,0BAA0B;MAClFuG,MAAM,EAAE,aAAavG,QAAQ,GAAG,yBAAyB,GAAG,yBAAyB,EAAE;MACvFwG,YAAY,EAAE,KAAK;MACnBQ,OAAO,EAAE,MAAM;MACfV,YAAY,EAAE,MAAM;MACpBO,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBM,GAAG,EAAE;IACP,CAAC;IACDkC,aAAa,EAAE;MACbjC,QAAQ,EAAE,MAAM;MAChBtB,KAAK,EAAE/F,QAAQ,GAAG,SAAS,GAAG,SAAS;MACvCsH,UAAU,EAAE;IACd,CAAC;IACDiC,YAAY,EAAE;MACZ9B,eAAe,EAAE5G,eAAe,CAACiD,IAAI,GAAG,CAAC,GAAG,SAAS,GAAG9D,QAAQ,GAAG,SAAS,GAAG,SAAS;MACxF+F,KAAK,EAAE,SAAS;MAChBQ,MAAM,EAAE,MAAM;MACdC,YAAY,EAAE,KAAK;MACnBQ,OAAO,EAAE,WAAW;MACpBK,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,KAAK;MACjBL,MAAM,EAAEpG,eAAe,CAACiD,IAAI,GAAG,CAAC,GAAG,SAAS,GAAG,aAAa;MAC5D+D,UAAU,EAAE,eAAe;MAC3BhB,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBM,GAAG,EAAE;IACP,CAAC;IACDoC,UAAU,EAAE;MACV7B,SAAS,EAAE,QAAiB;MAC5B5B,KAAK,EAAE/F,QAAQ,GAAG,SAAS,GAAG,SAAS;MACvCqH,QAAQ,EAAE,MAAM;MAChBoC,SAAS,EAAE,QAAQ;MACnBzC,OAAO,EAAE;IACX,CAAC;IACD0C,gBAAgB,EAAE;MAChBjC,eAAe,EAAEzH,QAAQ,GAAG,uBAAuB,GAAG,0BAA0B;MAChFuG,MAAM,EAAE,aAAavG,QAAQ,GAAG,yBAAyB,GAAG,yBAAyB,EAAE;MACvFwG,YAAY,EAAE,KAAK;MACnBQ,OAAO,EAAE,MAAM;MACf2C,SAAS,EAAE,MAAM;MACjBC,SAAS,EAAE;IACb,CAAC;IACDC,aAAa,EAAE;MACbhD,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBM,GAAG,EAAE,KAAK;MACVd,YAAY,EAAE,MAAM;MACpBe,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,KAAK;MACjBvB,KAAK,EAAE/F,QAAQ,GAAG,SAAS,GAAG;IAChC,CAAC;IACD8J,cAAc,EAAE;MACdjD,OAAO,EAAE,MAAM;MACfkD,mBAAmB,EAAE,SAAS;MAC9B3C,GAAG,EAAE;IACP,CAAC;IACD4C,cAAc,EAAE;MACd3C,QAAQ,EAAE,MAAM;MAChBtB,KAAK,EAAE/F,QAAQ,GAAG,SAAS,GAAG;IAChC,CAAC;IACDiK,YAAY,EAAE;MACZ3C,UAAU,EAAE,KAAK;MACjBhB,YAAY,EAAE,KAAK;MACnBP,KAAK,EAAE/F,QAAQ,GAAG,SAAS,GAAG;IAChC,CAAC;IACDkK,kBAAkB,EAAE;MAClBrD,OAAO,EAAE,MAAM;MACfsD,QAAQ,EAAE,MAAe;MACzB/C,GAAG,EAAE,KAAK;MACVuC,SAAS,EAAE;IACb,CAAC;IACDS,eAAe,EAAE;MACf3C,eAAe,EAAEzH,QAAQ,GAAG,yBAAyB,GAAG,yBAAyB;MACjF+F,KAAK,EAAE/F,QAAQ,GAAG,SAAS,GAAG,SAAS;MACvCqH,QAAQ,EAAE,MAAM;MAChBL,OAAO,EAAE,SAAS;MAClBR,YAAY,EAAE,KAAK;MACnBc,UAAU,EAAE;IACd,CAAC;IAED;IACA+C,iBAAiB,EAAE;MACjBV,SAAS,EAAE,MAAM;MACjB3C,OAAO,EAAE,MAAM;MACfS,eAAe,EAAEzH,QAAQ,GAAG,0BAA0B,GAAG,0BAA0B;MACnFuG,MAAM,EAAE,aAAavG,QAAQ,GAAG,yBAAyB,GAAG,0BAA0B,EAAE;MACxFwG,YAAY,EAAE;IAChB,CAAC;IAED8D,cAAc,EAAE;MACdzD,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,eAAe;MAC/BM,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,KAAK;MACjBvB,KAAK,EAAE/F,QAAQ,GAAG,SAAS,GAAG,SAAS;MACvCsG,YAAY,EAAE;IAChB,CAAC;IAEDiE,aAAa,EAAE;MACb1D,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBM,GAAG,EAAE,KAAK;MACVC,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,KAAK;MACjBvB,KAAK,EAAE/F,QAAQ,GAAG,SAAS,GAAG,SAAS;MAAE;MACzCiH,MAAM,EAAE,SAAS;MACjBY,UAAU,EAAE;IACd,CAAC;IAED2C,gBAAgB,EAAE;MAChB9B,KAAK,EAAE,MAAM;MACbE,MAAM,EAAE,MAAM;MACd3B,MAAM,EAAE,SAAS;MACjB4B,WAAW,EAAE7I,QAAQ,GAAG,SAAS,GAAG,SAAS,CAAE;IACjD,CAAC;IAEDyK,aAAa,EAAE;MACbpD,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,KAAK;MACjBvB,KAAK,EAAE/F,QAAQ,GAAG,SAAS,GAAG;IAChC,CAAC;IAED0K,eAAe,EAAE;MACf7D,OAAO,EAAE,MAAM;MACf2B,aAAa,EAAE,QAAiB;MAChCpB,GAAG,EAAE;IACP,CAAC;IAEDuD,eAAe,EAAE;MACf9D,OAAO,EAAE,MAAM;MACf2B,aAAa,EAAE,QAAiB;MAChCpB,GAAG,EAAE;IACP,CAAC;IAEDwD,oBAAoB,EAAE;MACpBvD,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,KAAK;MACjBvB,KAAK,EAAE/F,QAAQ,GAAG,SAAS,GAAG,SAAS;MACvC6K,aAAa,EAAE,WAAoB;MACnCC,aAAa,EAAE;IACjB,CAAC;IAEDC,aAAa,EAAE;MACblE,OAAO,EAAE,MAAM;MACf2B,aAAa,EAAE,QAAiB;MAChCpB,GAAG,EAAE,KAAK;MACV4D,WAAW,EAAE;IACf,CAAC;IAEDC,YAAY,EAAE;MACZpE,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBM,GAAG,EAAE,KAAK;MACVH,MAAM,EAAE,SAAS;MACjBD,OAAO,EAAE;IACX,CAAC;IAEDkE,gBAAgB,EAAE;MAChBxC,KAAK,EAAE,MAAM;MACbE,MAAM,EAAE,MAAM;MACd3B,MAAM,EAAE;IACV,CAAC;IAEDkE,gBAAgB,EAAE;MAChB9D,QAAQ,EAAE,MAAM;MAChBtB,KAAK,EAAE/F,QAAQ,GAAG,SAAS,GAAG,SAAS;MACvCoL,UAAU,EAAE;IACd;EACF,CAAC,CAAC;EAEF,MAAMC,MAAM,GAAGjF,gBAAgB,CAAC,CAAC;EAEjC,IAAI,CAAC7F,WAAW,EAAE,OAAO,IAAI;EAE7B,oBACEX,OAAA;IAAK0L,KAAK,EAAED,MAAM,CAAChF,SAAU;IAAAP,QAAA,gBAC3BlG,OAAA;MAAK0L,KAAK,EAAED,MAAM,CAACzE,MAAO;MAAC2E,OAAO,EAAEtI,kBAAmB;MAAA6C,QAAA,gBACrDlG,OAAA;QAAK0L,KAAK,EAAED,MAAM,CAAClE,UAAW;QAAArB,QAAA,gBAC5BlG,OAAA,CAACN,GAAG;UAACwE,IAAI,EAAE,EAAG;UAACiC,KAAK,EAAE/F,QAAQ,GAAG,SAAS,GAAG;QAAU;UAAAgG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1DvG,OAAA;UAAI0L,KAAK,EAAED,MAAM,CAACxF,KAAM;UAAAC,QAAA,EAAEtF,CAAC,CAAC,uBAAuB;QAAC;UAAAwF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC1DvG,OAAA;UAAM0L,KAAK,EAAED,MAAM,CAAC7D,KAAM;UAAA1B,QAAA,EAAEnF,OAAO,CAACmC;QAAM;UAAAkD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC,eACNvG,OAAA,CAACT,WAAW;QAAC2E,IAAI,EAAE,EAAG;QAACwH,KAAK,EAAED,MAAM,CAACzD;MAAW;QAAA5B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CAAC,EAEL1F,UAAU,iBACTb,OAAA;MAAK0L,KAAK,EAAED,MAAM,CAACtD,OAAQ;MAAAjC,QAAA,EACxBnF,OAAO,CAACmC,MAAM,KAAK,CAAC,gBACnBlD,OAAA;QAAK0L,KAAK,EAAED,MAAM,CAAC7B,UAAW;QAAA1D,QAAA,EACzCtF,CAAC,CAAC,2BAA2B,CAAC,CAACgL,OAAO,CAAC,aAAa,EAAEvL,eAAe;MAAC;QAAA+F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CAAC,gBAENvG,OAAA,CAAAE,SAAA;QAAAgG,QAAA,gBACElG,OAAA;UAAK0L,KAAK,EAAED,MAAM,CAACnD,aAAc;UAAApC,QAAA,gBAC/BlG,OAAA;YAAM0L,KAAK,EAAED,MAAM,CAAClD,SAAU;YAAArC,QAAA,EAAEtF,CAAC,CAAC,wBAAwB;UAAC;YAAAwF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACnEvG,OAAA;YACE0L,KAAK,EAAED,MAAM,CAACjD,UAAW;YACzBqD,KAAK,EAAE,GAAGpK,UAAU,CAACqK,GAAG,IAAIrK,UAAU,CAACsK,SAAS,EAAG;YACnDC,QAAQ,EAAGC,CAAC,IAAK;cACf,MAAM,CAACH,GAAG,EAAEC,SAAS,CAAC,GAAGE,CAAC,CAACC,MAAM,CAACL,KAAK,CAACM,KAAK,CAAC,GAAG,CAAC;cAClD,MAAMC,MAAM,GAAG9M,sBAAsB,CAACqC,cAAc,CAAC,CAAC,CAACiE,IAAI,CACzDyG,GAAG,IAAIA,GAAG,CAACP,GAAG,KAAKA,GAAG,IAAIO,GAAG,CAACN,SAAS,KAAKA,SAC9C,CAAC;cACD,IAAIK,MAAM,EAAE1K,aAAa,CAAC0K,MAAM,CAAC;YACnC,CAAE;YAAAlG,QAAA,EAED5G,sBAAsB,CAACqC,cAAc,CAAC,CAAC,CAAC2K,GAAG,CAACF,MAAM,iBACjDpM,OAAA;cAAkD6L,KAAK,EAAE,GAAGO,MAAM,CAACN,GAAG,IAAIM,MAAM,CAACL,SAAS,EAAG;cAAA7F,QAAA,EAC1FkG,MAAM,CAACG;YAAK,GADF,GAAGH,MAAM,CAACN,GAAG,IAAIM,MAAM,CAACL,SAAS,EAAE;cAAA3F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAExC,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAELzE,SAAS,CAACE,UAAU,CAACkB,MAAM,GAAG,CAAC,IAAIpB,SAAS,CAACG,OAAO,CAACiB,MAAM,GAAG,CAAC,gBAC9DlD,OAAA;UAAK0L,KAAK,EAAED,MAAM,CAAChC,gBAAiB;UAAAvD,QAAA,gBAClClG,OAAA,CAACL,aAAa;YAACuE,IAAI,EAAE;UAAG;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3BvG,OAAA;YAAM0L,KAAK,EAAED,MAAM,CAAC/B,aAAc;YAAAxD,QAAA,GAAC,2BACd,EAACpE,SAAS,CAACE,UAAU,CAACkB,MAAM,EAAC,gBAAc,EAACpB,SAAS,CAACG,OAAO,CAACiB,MAAM,EAAC,gBAC1F;UAAA;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,GACJ,IAAI,eAERvG,OAAA;UAAK0L,KAAK,EAAED,MAAM,CAAChD,WAAY;UAAAvC,QAAA,EAC5BnF,OAAO,CAACuL,GAAG,CAAC3J,KAAK;YAAA,IAAA6J,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;YAAA,oBAChB7M,OAAA;cAAgC0L,KAAK,EAAED,MAAM,CAAC9C,SAAU;cAACmE,SAAS,EAAC,oBAAoB;cAAA5G,QAAA,gBACrFlG,OAAA;gBAAK0L,KAAK,EAAED,MAAM,CAAC5C,eAAgB;gBAAA3C,QAAA,gBACjClG,OAAA;kBACE+M,IAAI,EAAC,UAAU;kBACfrB,KAAK,EAAED,MAAM,CAAC1C,QAAS;kBACvB/D,OAAO,EAAE/D,eAAe,CAAC2B,GAAG,CAACD,KAAK,CAACE,WAAW,CAACC,EAAE,CAAE;kBACnDkJ,QAAQ,EAAGC,CAAC,IAAK3I,oBAAoB,CAACX,KAAK,CAACE,WAAW,CAACC,EAAE,EAAEmJ,CAAC,CAACC,MAAM,CAAClH,OAAO;gBAAE;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/E,CAAC,eACFvG,OAAA;kBAAK0L,KAAK,EAAED,MAAM,CAACvC,SAAU;kBAAAhD,QAAA,gBAC3BlG,OAAA;oBAAK0L,KAAK,EAAED,MAAM,CAACrC,UAAW;oBAAAlD,QAAA,EAAEvD,KAAK,CAACE,WAAW,CAACoD;kBAAK;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC9DvG,OAAA;oBAAK0L,KAAK,EAAED,MAAM,CAACpC,SAAU;oBAACyD,SAAS,EAAC,oBAAoB;oBAAA5G,QAAA,gBAC1DlG,OAAA,CAACP,KAAK;sBAACyE,IAAI,EAAE;oBAAG;sBAAAkC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EAClB5D,KAAK,CAACE,WAAW,CAACmK,SAAS,CAACC,kBAAkB,CAAC,OAAO,CAAC,eACxDjN,OAAA;sBAAM0L,KAAK,EAAED,MAAM,CAACnC,eAAgB;sBAACwD,SAAS,EAAC,0BAA0B;sBAAA5G,QAAA,GACtEgH,IAAI,CAACC,KAAK,CAACxK,KAAK,CAACyK,UAAU,CAAC,EAAC,GAChC;oBAAA;sBAAAhH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,EACNT,aAAa,CAACnD,KAAK,CAAC,EACpBA,KAAK,CAAC0K,QAAQ,iBAAIrN,OAAA;sBAAM0L,KAAK,EAAE;wBAAEjE,QAAQ,EAAE,MAAM;wBAAEtB,KAAK,EAAE/F,QAAQ,GAAG,SAAS,GAAG;sBAAU,CAAE;sBAAA8F,QAAA,EAAC;oBAAO;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1G,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNvG,OAAA;kBAAK0L,KAAK,EAAED,MAAM,CAAClC,aAAc;kBAAArD,QAAA,eAC/BlG,OAAA;oBACE0L,KAAK,EAAED,MAAM,CAACjC,YAAa;oBAC3BsD,SAAS,EAAC,uBAAuB;oBACjCnB,OAAO,EAAEA,CAAA,KAAMhG,aAAa,CAAChD,KAAK,CAACE,WAAW,CAACC,EAAE,CAAE;oBACnDmD,KAAK,EAAC,gBAAa;oBAAAC,QAAA,eAEnBlG,OAAA,CAACR,GAAG;sBAAC0E,IAAI,EAAE;oBAAG;sBAAAkC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGL3E,WAAW,KAAKe,KAAK,CAACE,WAAW,CAACC,EAAE,IAAIZ,WAAW,iBAClDlC,OAAA;gBAAK0L,KAAK,EAAED,MAAM,CAAC3B,gBAAiB;gBAACgD,SAAS,EAAC,2BAA2B;gBAAA5G,QAAA,gBACxElG,OAAA;kBAAK0L,KAAK,EAAED,MAAM,CAACxB,aAAc;kBAAA/D,QAAA,gBAC/BlG,OAAA,CAACR,GAAG;oBAAC0E,IAAI,EAAE;kBAAG;oBAAAkC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,6BACI,GAAAiG,qBAAA,GAAC7J,KAAK,CAACM,eAAe,CAAC,CAAC,CAAC,cAAAuJ,qBAAA,uBAAxBA,qBAAA,CAA0B9G,IAAI,EAAC,IACvD;gBAAA;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNvG,OAAA;kBAAK0L,KAAK,EAAED,MAAM,CAACvB,cAAe;kBAAC4C,SAAS,EAAC,yBAAyB;kBAAA5G,QAAA,gBACpElG,OAAA;oBAAK0L,KAAK,EAAED,MAAM,CAACrB,cAAe;oBAAAlE,QAAA,gBAChClG,OAAA;sBAAK0L,KAAK,EAAED,MAAM,CAACpB,YAAa;sBAAAnE,QAAA,EAAC;oBAAqB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC5DvG,OAAA;sBAAAkG,QAAA,GAAAuG,qBAAA,GAAMvK,WAAW,CAACA,WAAW,cAAAuK,qBAAA,uBAAvBA,qBAAA,CAAyBa;oBAAY;sBAAAlH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/C,CAAC,eACNvG,OAAA;oBAAK0L,KAAK,EAAED,MAAM,CAACrB,cAAe;oBAAAlE,QAAA,gBAChClG,OAAA;sBAAK0L,KAAK,EAAED,MAAM,CAACpB,YAAa;sBAAAnE,QAAA,EAAC;oBAAc;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACrDvG,OAAA;sBAAK0L,KAAK,EAAED,MAAM,CAACnB,kBAAmB;sBAACwC,SAAS,EAAC,8BAA8B;sBAAA5G,QAAA,IAAAwG,sBAAA,GAC5ExK,WAAW,CAACA,WAAW,cAAAwK,sBAAA,uBAAvBA,sBAAA,CAAyBa,cAAc,CAACjB,GAAG,CAAC,CAACQ,SAAS,EAAEU,GAAG,kBAC1DxN,OAAA;wBAAgB0L,KAAK,EAAED,MAAM,CAACjB,eAAgB;wBAAAtE,QAAA,EAC3C4G;sBAAS,GADDU,GAAG;wBAAApH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAER,CACP,CAAC,EACD,CAAC,GAAAoG,sBAAA,GAACzK,WAAW,CAACA,WAAW,cAAAyK,sBAAA,eAAvBA,sBAAA,CAAyBY,cAAc,KAAIrL,WAAW,CAACA,WAAW,CAACqL,cAAc,CAACrK,MAAM,KAAK,CAAC,kBAC/FlD,OAAA;wBAAM0L,KAAK,EAAE;0BAAE7B,SAAS,EAAE,QAAQ;0BAAE1D,KAAK,EAAE/F,QAAQ,GAAG,SAAS,GAAG;wBAAU,CAAE;wBAAA8F,QAAA,EAAC;sBAE/E;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CACP;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,EAGAtF,eAAe,CAAC2B,GAAG,CAACD,KAAK,CAACE,WAAW,CAACC,EAAE,CAAC,iBACxC9C,OAAA;gBAAK0L,KAAK,EAAED,MAAM,CAAChB,iBAAkB;gBAACqC,SAAS,EAAC,4BAA4B;gBAAA5G,QAAA,gBAC1ElG,OAAA;kBAAK0L,KAAK,EAAED,MAAM,CAACf,cAAe;kBAAAxE,QAAA,gBAChClG,OAAA;oBAAM0L,KAAK,EAAED,MAAM,CAACZ,aAAc;oBAAA3E,QAAA,EAAC;kBAAsC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChFvG,OAAA;oBACE0L,KAAK,EAAED,MAAM,CAACd,aAAc;oBAC5B8C,YAAY,EAAGxB,CAAC,IAAK;sBACnBA,CAAC,CAACyB,aAAa,CAAChC,KAAK,CAACvF,KAAK,GAAG/F,QAAQ,GAAG,SAAS,GAAG,SAAS;oBAChE,CAAE;oBACFuN,YAAY,EAAG1B,CAAC,IAAK;sBACnBA,CAAC,CAACyB,aAAa,CAAChC,KAAK,CAACvF,KAAK,GAAG/F,QAAQ,GAAG,SAAS,GAAG,SAAS;oBAChE,CAAE;oBAAA8F,QAAA,gBAEFlG,OAAA;sBACE+M,IAAI,EAAC,UAAU;sBACf/H,OAAO,EAAET,qBAAqB,CAAC5B,KAAK,CAAE;sBACtCqJ,QAAQ,EAAGC,CAAC,IAAKlH,cAAc,CAACpC,KAAK,EAAEsJ,CAAC,CAACC,MAAM,CAAClH,OAAO,CAAE;sBACzD0G,KAAK,EAAED,MAAM,CAACb;oBAAiB;sBAAAxE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChC,CAAC,eACFvG,OAAA;sBAAAkG,QAAA,EAAM;oBAAW;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eAENvG,OAAA;kBAAK0L,KAAK,EAAED,MAAM,CAACX,eAAgB;kBAAA5E,QAAA,GAEhC,EAAA0G,sBAAA,GAAAjK,KAAK,CAACM,eAAe,CAAC,CAAC,CAAC,cAAA2J,sBAAA,uBAAxBA,sBAAA,CAA0B5K,UAAU,KAAIW,KAAK,CAACM,eAAe,CAAC,CAAC,CAAC,CAACjB,UAAU,CAACkB,MAAM,GAAG,CAAC,iBACrFlD,OAAA;oBAAK0L,KAAK,EAAED,MAAM,CAACV,eAAgB;oBAAA7E,QAAA,gBACjClG,OAAA;sBAAK0L,KAAK,EAAED,MAAM,CAACT,oBAAqB;sBAAA9E,QAAA,EAAC;oBAAW;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC1DvG,OAAA;sBAAK0L,KAAK,EAAED,MAAM,CAACN,aAAc;sBAAAjF,QAAA,EAC9BvD,KAAK,CAACM,eAAe,CAAC,CAAC,CAAC,CAACjB,UAAU,CAACsK,GAAG,CAAC,CAACvI,SAAS,EAAEyJ,GAAG;wBAAA,IAAAI,qBAAA;wBAAA,oBACtD5N,OAAA;0BAAiB0L,KAAK,EAAED,MAAM,CAACJ,YAAa;0BAAAnF,QAAA,gBAC1ClG,OAAA;4BACE+M,IAAI,EAAC,UAAU;4BACfrB,KAAK,EAAED,MAAM,CAACH,gBAAiB;4BAC/BtG,OAAO,EAAE,EAAA4I,qBAAA,GAAAxM,kBAAkB,CAAC6C,GAAG,CAACtB,KAAK,CAACE,WAAW,CAACC,EAAE,CAAC,cAAA8K,qBAAA,uBAA5CA,qBAAA,CAA8ChL,GAAG,CAACmB,SAAS,CAAC,KAAI,KAAM;4BAC/EiI,QAAQ,EAAGC,CAAC,IAAKnI,wBAAwB,CAACnB,KAAK,CAACE,WAAW,CAACC,EAAE,EAAEiB,SAAS,EAAEkI,CAAC,CAACC,MAAM,CAAClH,OAAO;0BAAE;4BAAAoB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC9F,CAAC,eACFvG,OAAA;4BAAM0L,KAAK,EAAED,MAAM,CAACF,gBAAiB;4BAAArF,QAAA,EAAEnC;0BAAS;4BAAAqC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA,GAP9CiH,GAAG;0BAAApH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAQR,CAAC;sBAAA,CACT;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN,EAGA,EAAAsG,sBAAA,GAAAlK,KAAK,CAACM,eAAe,CAAC,CAAC,CAAC,cAAA4J,sBAAA,uBAAxBA,sBAAA,CAA0B5K,OAAO,KAAIU,KAAK,CAACM,eAAe,CAAC,CAAC,CAAC,CAAChB,OAAO,CAACiB,MAAM,GAAG,CAAC,iBAC/ElD,OAAA;oBAAK0L,KAAK,EAAED,MAAM,CAACV,eAAgB;oBAAA7E,QAAA,gBACjClG,OAAA;sBAAK0L,KAAK,EAAED,MAAM,CAACT,oBAAqB;sBAAA9E,QAAA,EAAC;oBAAU;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACzDvG,OAAA;sBAAK0L,KAAK,EAAED,MAAM,CAACN,aAAc;sBAAAjF,QAAA,EAC9BvD,KAAK,CAACM,eAAe,CAAC,CAAC,CAAC,CAAChB,OAAO,CAACqK,GAAG,CAAC,CAACjI,MAAM,EAAEmJ,GAAG;wBAAA,IAAAK,oBAAA;wBAAA,oBAChD7N,OAAA;0BAAiB0L,KAAK,EAAED,MAAM,CAACJ,YAAa;0BAAAnF,QAAA,gBAC1ClG,OAAA;4BACE+M,IAAI,EAAC,UAAU;4BACfrB,KAAK,EAAED,MAAM,CAACH,gBAAiB;4BAC/BtG,OAAO,EAAE,EAAA6I,oBAAA,GAAAtM,eAAe,CAAC0C,GAAG,CAACtB,KAAK,CAACE,WAAW,CAACC,EAAE,CAAC,cAAA+K,oBAAA,uBAAzCA,oBAAA,CAA2CjL,GAAG,CAACyB,MAAM,CAAC,KAAI,KAAM;4BACzE2H,QAAQ,EAAGC,CAAC,IAAK7H,qBAAqB,CAACzB,KAAK,CAACE,WAAW,CAACC,EAAE,EAAEuB,MAAM,EAAE4H,CAAC,CAACC,MAAM,CAAClH,OAAO;0BAAE;4BAAAoB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACxF,CAAC,eACFvG,OAAA;4BAAM0L,KAAK,EAAED,MAAM,CAACF,gBAAiB;4BAAArF,QAAA,EAAE7B;0BAAM;4BAAA+B,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA,GAP3CiH,GAAG;0BAAApH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAQR,CAAC;sBAAA,CACT;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA,GAjIO5D,KAAK,CAACE,WAAW,CAACC,EAAE;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAkIzB,CAAC;UAAA,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENvG,OAAA;UACE0L,KAAK,EAAED,MAAM,CAAC9B,YAAa;UAC3BgC,OAAO,EAAExG,YAAa;UACtB2I,QAAQ,EAAE,CAAC,MAAM;YACf,MAAMC,eAAe,GAAGC,KAAK,CAACC,IAAI,CAAC7M,kBAAkB,CAAC8M,MAAM,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKD,GAAG,GAAGC,KAAK,CAACnK,IAAI,EAAE,CAAC,CAAC;YAC3G,MAAMoK,YAAY,GAAGN,KAAK,CAACC,IAAI,CAAC1M,eAAe,CAAC2M,MAAM,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEnM,OAAO,KAAKmM,GAAG,GAAGnM,OAAO,CAACiC,IAAI,EAAE,CAAC,CAAC;YACzG,OAAO6J,eAAe,GAAGO,YAAY,KAAK,CAAC;UAC7C,CAAC,EAAE,CAAE;UAAApI,QAAA,gBAELlG,OAAA,CAACN,GAAG;YAACwE,IAAI,EAAE;UAAG;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAChB,CAAC,MAAM;YACN,MAAMwH,eAAe,GAAGC,KAAK,CAACC,IAAI,CAAC7M,kBAAkB,CAAC8M,MAAM,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKD,GAAG,GAAGC,KAAK,CAACnK,IAAI,EAAE,CAAC,CAAC;YAC3G,MAAMoK,YAAY,GAAGN,KAAK,CAACC,IAAI,CAAC1M,eAAe,CAAC2M,MAAM,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEnM,OAAO,KAAKmM,GAAG,GAAGnM,OAAO,CAACiC,IAAI,EAAE,CAAC,CAAC;YACzG,MAAMqK,aAAa,GAAGR,eAAe,GAAGO,YAAY;YAEpD,IAAIC,aAAa,KAAK,CAAC,EAAE;cACvB,OAAO,aAAatN,eAAe,CAACiD,IAAI,aAAajD,eAAe,CAACiD,IAAI,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,eAAejD,eAAe,CAACiD,IAAI,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG;YAC/I;YAEA,OAAO,aAAaqK,aAAa,WAAWA,aAAa,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,eAAeA,aAAa,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG;UACxH,CAAC,EAAE,CAAC;QAAA;UAAAnI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA,eACT;IACH;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC9F,EAAA,CA/uBIN,sBAA6D;EAAA,QAOxChB,UAAU,EACXC,OAAO,EACjBC,WAAW;AAAA;AAAAmP,EAAA,GATrBrO,sBAA6D;AAivBnE,eAAeA,sBAAsB;AAAC,IAAAqO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}