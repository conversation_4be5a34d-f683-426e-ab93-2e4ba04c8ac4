class 1:
NOM_CLASSE: Sweeping
ATTRIBUTS: 
- id: int
- pages: int
MÉTHODES:

class 2:
NOM_CLASSE: Publication
ATTRIBUTS: 
  title: string
MÉTHODES:

class 3:
NOM_CLASSE: Compilation
ATTRIBUTS: 
- id: number
- pages: int
MÉTHODES:

class 4:
NOM_CLASSE: Compilation
ATTRIBUTS: 
- id: int
- pages: int
MÉTHODES:

class 5:
NOM_CLASSE: Sweeping
ATTRIBUTS: 
id: int
pages: int
MÉTHODES:

class 6:
NOM_CLASSE: Hardcover
ATTRIBUTS: 
Id: int
MÉTHODES:

class 7:
NOM_CLASSE: Paperback
ATTRIBUTS: 
 id: int
MÉTHODES:



----- RÉSUMÉ DES RELATIONS -----
• 1 relation(s) de type generalization
• 2 relation(s) de type endpoint

----- RELATIONS DÉTECTÉES -----
• Flèche arrow 1 non interprétable (relation orpheline, aucune classe aux extrémités)
• Flèche arrow 2 non interprétable (relation orpheline, aucune classe aux extrémités)
• Flèche arrow 3 non interprétable (relation orpheline, aucune classe aux extrémités)
• Compilation hérite de Hardcover (héritage (généralisation))
• Il y a une relation de association entre Paperback et Publication
• Il y a une relation de association entre Publication et Hardcover