class 1:
NOM_CLASSE: Commande
ATTRIBUTS: 
date
modeReglement
delaLivraison
fraisDePort
montant
MÉTHODES:

class 2:
NOM_CLASSE: Panier
ATTRIBUTS: total
MÉTHODES:

class 3:
NOM_CLASSE: CartaBanca
ATTRIBUTS: 
  type
  numero
  dateValidite
MÉTHODES:

class 4:
NOM_CLASSE: Client
ATTRIBUTS: 
  nom
  prenom
  email
MÉTHODES:



----- RÉSUMÉ DES RELATIONS -----
• 3 relation(s) de type endpoint
• 1 relation(s) de type composition

----- RELATIONS DÉTECTÉES -----
• Il y a une relation de association entre Panier et Commande
• CartaBanca contient Client comme partie intégrante (composition (partie intégrante))
• Il y a une relation de association entre Commande et Client
• Il y a une relation de association entre Commande et Client