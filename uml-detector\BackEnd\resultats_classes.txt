class 1:
NOM_CLASSE: Web User
ATTRIBUTS: 
Id: string
password: string
state: UserState
MÉTHODES:

class 2:
NOM_CLASSE: Payment
ATTRIBUTS: 
id: string
paid Date
total
details
MÉTHODES:

class 3:
NOM_CLASSE: Shopping Cart
ATTRIBUTS: 
 date: string
MÉTHODES:

class 4:
NOM_CLASSE: Account
ATTRIBUTS: 
- Id: string
- address
- isClosed: boolean
- openDate
- closedDate
MÉTHODES:

class 5:
NOM_CLASSE: Customer
ATTRIBUTS: 
- id: string
- address: string
- phone: Phone
- email: string
MÉTHODES:

class 6:
NOM_CLASSE: Order
ATTRIBUTS: 
number
date
shippedDate
address
status
total
MÉTHODES:

class 7:
NOM_CLASSE: Line Item
ATTRIBUTS: 
- quantity
- price
MÉTHODES:

class 8:
NOM_CLASSE: Product
ATTRIBUTS: 
- Id
- name
- Supplich
MÉTHODES:



----- RÉSUMÉ DES RELATIONS -----
• 10 relation(s) de type endpoint

----- RELATIONS DÉTECTÉES -----
• Fl<PERSON>che partant de Payment sans classe d'arrivée identifiée (relation orpheline)
• Il y a une relation de association entre Customer et Account
• Il y a une relation de association entre Web User et Shopping Cart
• Il y a une relation de association entre Account et Order
• Il y a une relation de association entre Shopping Cart et Account
• Il y a une relation de association entre Line Item et Product
• Il y a une relation de association entre Shopping Cart et Line Item
• Il y a une relation de association entre Line Item et Order
• Il y a une relation de association entre Web User et Customer
• Il y a une relation de association entre Account et Payment
• Il y a une relation de association entre Line Item et Order