// src/components/Auth/Login.tsx
import React, { useState } from 'react';
import { X, AlertCircle } from 'lucide-react';
import { useAuth } from '../../context/AuthContext';
import { getLoginStyles } from './LoginStyles';
import { useLanguage } from '../../context/LanguageContext';

interface LoginProps {
  darkMode: boolean;
  onClose: () => void;
}

const Login: React.FC<LoginProps> = ({ darkMode, onClose }) => {
  // États du composant
  const [email, setEmail] = useState<string>('');
  const [password, setPassword] = useState<string>('');
  const [confirmPassword, setConfirmPassword] = useState<string>('');
  const [name, setName] = useState<string>('');
  const [rememberMe, setRememberMe] = useState<boolean>(false);
  const [isLogin, setIsLogin] = useState<boolean>(true);
  
  // États d'erreur
  const [emailError, setEmailError] = useState<string>('');
  const [passwordError, setPasswordError] = useState<string>('');
  const [confirmPasswordError, setConfirmPasswordError] = useState<string>('');
  const [nameError, setNameError] = useState<string>('');
  const [generalError, setGeneralError] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const { login, signup, loginWithGoogle } = useAuth();
  const { t } = useLanguage();

  // Fonction de validation email
  const validateEmail = (email: string): boolean => {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
  };

  // Réinitialiser toutes les erreurs
  const resetErrors = () => {
    setEmailError('');
    setPasswordError('');
    setConfirmPasswordError('');
    setNameError('');
    setGeneralError('');
  };

  // Gestionnaire de soumission du formulaire
  const handleSubmit = async (e?: React.FormEvent): Promise<void> => {
    if (e) e.preventDefault();
    
    resetErrors();
    let hasErrors = false;
    
    // Validation email
    if (!email) {
      setEmailError(t('auth.error.emailRequired'));
      hasErrors = true;
    } else if (!validateEmail(email)) {
      setEmailError(t('auth.error.emailInvalid'));
      hasErrors = true;
    }

    // Validation mot de passe
    if (!password) {
      setPasswordError(t('auth.error.passwordRequired'));
      hasErrors = true;
    } else if (password.length < 6) {
      setPasswordError(t('auth.error.passwordTooShort'));
      hasErrors = true;
    }

    // Validations supplémentaires pour l'inscription
    if (!isLogin) {
      // Validation nom
      if (!name) {
        setNameError(t('auth.error.nameRequired'));
        hasErrors = true;
      }

      // Validation confirmation mot de passe
      if (!confirmPassword) {
        setConfirmPasswordError(t('auth.error.confirmPasswordRequired'));
        hasErrors = true;
      } else if (password !== confirmPassword) {
        setConfirmPasswordError(t('auth.error.passwordMismatch'));
        hasErrors = true;
      }
    }
    
    if (!hasErrors) {
      setIsLoading(true);
      try {
        if (isLogin) {
          await login(email, password);
          onClose();
        } else {
          await signup(email, password, name);
          onClose();
        }
      } catch (error) {
        console.error("Authentication error:", error);
        let errorMessage = t('auth.error.authFailed');

        // Gestion des erreurs Firebase
        if (error instanceof Error) {
          const errorCode = (error as any).code;
          switch (errorCode) {
            case 'auth/user-not-found':
            case 'auth/wrong-password':
              errorMessage = t('auth.error.invalidCredentials');
              break;
            case 'auth/email-already-in-use':
              errorMessage = t('auth.error.emailInUse');
              break;
            case 'auth/weak-password':
              errorMessage = t('auth.error.weakPassword');
              break;
            case 'auth/invalid-email':
              errorMessage = t('auth.error.emailInvalid');
              break;
            case 'auth/too-many-requests':
              errorMessage = t('auth.error.tooManyRequests');
              break;
            default:
              errorMessage = error.message;
          }
        }
        
        setGeneralError(errorMessage);
      } finally {
        setIsLoading(false);
      }
    }
  };

  // Gestionnaire de connexion Google
  const handleGoogleLogin = async () => {
    setIsLoading(true);
    try {
      await loginWithGoogle();
      onClose();
    } catch (error) {
      console.error("Google login error:", error);
      setGeneralError(t('auth.error.googleLoginFailed'));
    } finally {
      setIsLoading(false);
    }
  };

  // Gestionnaire de changement de mode (connexion/inscription)
  const handleModeSwitch = () => {
    resetErrors();
    setIsLogin(!isLogin);
  };

  // Obtenir les styles
  const styles = getLoginStyles(darkMode);

  return (
    <div style={styles.overlay}>
      <div style={styles.modalContainer}>
        <div style={styles.modalHeader}>
          <h2 style={styles.modalTitle}>
            {isLogin ? t('auth.login.title') : t('auth.signup.title')}
          </h2>
          <button style={styles.closeButton} onClick={onClose}>
            <X size={20} />
          </button>
        </div>
        
        <div style={styles.modalBody}>
          {generalError && (
            <div style={styles.errorAlert}>
              <AlertCircle size={16} />
              <span>{generalError}</span>
            </div>
          )}

          <form onSubmit={handleSubmit} style={styles.formContainer}>
            {!isLogin && (
              <div style={styles.inputGroup}>
                <label style={styles.label} htmlFor="name">{t('auth.field.name')}</label>
                <input
                  id="name"
                  style={{
                    ...styles.input,
                    borderColor: nameError ? '#ef4444' : styles.input.borderColor
                  }}
                  type="text"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  placeholder={t('auth.placeholder.name')}
                />
                {nameError && <div style={styles.errorText}>{nameError}</div>}
              </div>
            )}
            
            <div style={styles.inputGroup}>
              <label style={styles.label} htmlFor="email">{t('auth.field.email')}</label>
              <input
                id="email"
                style={{
                  ...styles.input,
                  borderColor: emailError ? '#ef4444' : styles.input.borderColor
                }}
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder={t('auth.placeholder.email')}
              />
              {emailError && <div style={styles.errorText}>{emailError}</div>}
            </div>
            
            <div style={styles.inputGroup}>
              <label style={styles.label} htmlFor="password">{t('auth.field.password')}</label>
              <input
                id="password"
                style={{
                  ...styles.input,
                  borderColor: passwordError ? '#ef4444' : styles.input.borderColor
                }}
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder={isLogin ? '••••••••' : t('auth.placeholder.password')}
              />
              {passwordError && <div style={styles.errorText}>{passwordError}</div>}
            </div>

            {!isLogin && (
              <div style={styles.inputGroup}>
                <label style={styles.label} htmlFor="confirm-password">{t('auth.field.confirmPassword')}</label>
                <input
                  id="confirm-password"
                  style={{
                    ...styles.input,
                    borderColor: confirmPasswordError ? '#ef4444' : styles.input.borderColor
                  }}
                  type="password"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  placeholder={t('auth.placeholder.confirmPassword')}
                />
                {confirmPasswordError && <div style={styles.errorText}>{confirmPasswordError}</div>}
              </div>
            )}
            
            {isLogin && (
              <>
                <div style={styles.checkbox}>
                  <input
                    style={styles.checkboxInput}
                    type="checkbox"
                    id="remember-me"
                    checked={rememberMe}
                    onChange={(e) => setRememberMe(e.target.checked)}
                  />
                  <label htmlFor="remember-me" style={styles.checkboxLabel}>
                    {t('auth.rememberMe')}
                  </label>
                </div>

                <div style={styles.forgotPassword}>
                  <a href="#" style={styles.forgotLink}>{t('auth.forgotPassword')}</a>
                </div>
              </>
            )}
            
            {!isLogin && (
              <div style={styles.checkbox}>
                <input
                  style={styles.checkboxInput}
                  type="checkbox"
                  id="terms"
                  checked={rememberMe}
                  onChange={(e) => setRememberMe(e.target.checked)}
                />
                <label htmlFor="terms" style={styles.checkboxLabel}>
                  {t('auth.acceptTerms')}
                </label>
              </div>
            )}
            
            <button 
              type="submit" 
              style={{
                ...styles.submitButton,
                opacity: isLoading ? 0.7 : 1,
                cursor: isLoading ? 'not-allowed' : 'pointer'
              }}
              disabled={isLoading}
            >
              {isLoading ?
                (isLogin ? t('auth.button.loggingIn') : t('auth.button.creatingAccount')) :
                (isLogin ? t('auth.button.login') : t('auth.button.createAccount'))}
            </button>
          </form>
          
          <div style={styles.divider}>
            <div style={styles.dividerLine}></div>
            <span style={styles.dividerText}>{t('auth.or')}</span>
            <div style={styles.dividerLine}></div>
          </div>
          
          <div style={styles.socialLogin}>
            <button 
              type="button" 
              style={{
                ...styles.socialButton,
                opacity: isLoading ? 0.7 : 1,
                cursor: isLoading ? 'not-allowed' : 'pointer'
              }}
              onClick={handleGoogleLogin}
              disabled={isLoading}
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" fill="#4285F4"/>
                <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" fill="#34A853"/>
                <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l3.66-2.84z" fill="#FBBC05"/>
                <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" fill="#EA4335"/>
              </svg>
              {t('auth.button.continueWithGoogle')}
            </button>
          </div>
          
          <div style={styles.switchMode}>
            {isLogin ? t('auth.noAccount') : t('auth.haveAccount')}
            <button style={styles.switchButton} onClick={handleModeSwitch}>
              {isLogin ? t('auth.button.signup') : t('auth.button.login')}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;