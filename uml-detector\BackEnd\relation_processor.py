#relation_processor.py

import math
from typing import Dict, List, Tuple, Optional
import logging

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("relation_processor.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class RelationProcessor:
    """
    Classe pour traiter les relations entre les objets détectés dans les diagrammes UML.
    Associe les flèches détectées avec les types de relations et les classes correspondantes.
    """
    
    def __init__(self, distance_threshold: int = 100):
        """
        Initialise le processeur de relations.
        
        Args:
            distance_threshold: Distance maximale (en pixels) pour considérer qu'un point est proche d'un autre objet
        """
        self.distance_threshold = distance_threshold
        
        # Mappings des indices de classes pour les deux modèles
        self.MODEL1_NAMES = {0: 'arrow', 1: 'class'}
        self.MODEL2_NAMES = {
            0: 'A', 
            1: 'C', 
            2: 'generalization', 
            3: 'endpoint', 
            4: 'one-way-association', 
            5: 'composition', 
            6: 'aggregation'
        }
        
        # Descriptions des relations pour les phrases générées
        self.RELATION_DESCRIPTIONS = {
            'generalization': "héritage (généralisation)",
            'one-way-association': "association unidirectionnelle",
            'composition': "composition (partie intégrante)",
            'aggregation': "agrégation (contient)",
            'endpoint': "association",  # Simplification: endpoint devient association
            'association': "association",  # Relation par défaut si seulement des flèches sont détectées
            'A': "association de type A",
            'C': "association de type C"
        }
        
        # Définition de l'ordre de priorité des relations (du plus fort au plus faible)
        self.RELATION_PRIORITY = [
            'generalization',
            'composition',
            'aggregation',
            'one-way-association',
            'association',
            'endpoint',
            'A',
            'C'
        ]
        
        # Seuil pour considérer une flèche comme trop courte (potentiellement un symbole erroné)
        self.MIN_ARROW_LENGTH = 20  # en pixels
        
        logger.info("RelationProcessor initialisé avec un seuil de distance de %d pixels", distance_threshold)
    
    def calculate_distance(self, point1: Tuple[int, int], point2: Tuple[int, int]) -> float:
        """
        Calcule la distance euclidienne entre deux points.
        
        Args:
            point1: Premier point (x, y)
            point2: Deuxième point (x, y)
            
        Returns:
            La distance euclidienne entre les deux points
        """
        return math.sqrt((point1[0] - point2[0])**2 + (point1[1] - point2[1])**2)
    
    def get_box_center(self, box: Tuple[int, int, int, int]) -> Tuple[int, int]:
        """
        Calcule le centre d'une boîte délimitante.
        
        Args:
            box: Boîte délimitante (x1, y1, x2, y2)
            
        Returns:
            Coordonnées du centre (center_x, center_y)
        """
        x1, y1, x2, y2 = box
        return ((x1 + x2) // 2, (y1 + y2) // 2)
    
    def get_arrow_orientation(self, arrow_box: Tuple[int, int, int, int]) -> str:
        """
        Détermine l'orientation d'une flèche (horizontale ou verticale).
        
        Args:
            arrow_box: Boîte délimitante de la flèche (x1, y1, x2, y2)
            
        Returns:
            'horizontal' ou 'vertical'
        """
        x1, y1, x2, y2 = arrow_box
        width = x2 - x1
        height = y2 - y1
        
        return 'horizontal' if width > height else 'vertical'
    
    def get_arrow_endpoints(self, arrow_box: Tuple[int, int, int, int]) -> Tuple[Tuple[int, int], Tuple[int, int]]:
        """
        Obtient les points d'extrémité estimés d'une flèche.
        
        Args:
            arrow_box: Boîte délimitante de la flèche (x1, y1, x2, y2)
            
        Returns:
            Tuple contenant les deux extrémités ((x1, y1), (x2, y2))
        """
        x1, y1, x2, y2 = arrow_box
        orientation = self.get_arrow_orientation(arrow_box)
        
        if orientation == 'horizontal':
            # Pour les flèches horizontales, utiliser les points extrêmes gauche et droit
            start_point = (x1, (y1 + y2) // 2)
            end_point = (x2, (y1 + y2) // 2)
        else:
            # Pour les flèches verticales, utiliser les points extrêmes haut et bas
            start_point = ((x1 + x2) // 2, y1)
            end_point = ((x1 + x2) // 2, y2)
        
        return (start_point, end_point)
    
    def calculate_arrow_length(self, start_point: Tuple[int, int], end_point: Tuple[int, int]) -> float:
        """
        Calcule la longueur d'une flèche à partir de ses extrémités.
        
        Args:
            start_point: Point de départ (x, y)
            end_point: Point d'arrivée (x, y)
            
        Returns:
            Longueur de la flèche en pixels
        """
        return self.calculate_distance(start_point, end_point)
    
    def is_same_class(self, class1: Dict, class2: Dict) -> bool:
        """
        Détermine si deux objets de classe représentent la même classe.
        
        Args:
            class1: Premier objet classe
            class2: Deuxième objet classe
            
        Returns:
            True si c'est la même classe, False sinon
        """
        if not class1 or not class2:
            return False
        return class1['id'] == class2['id']
    
    def find_nearest_class(self, point: Tuple[int, int], class_boxes: List[Dict]) -> Optional[Dict]:
        """
        Trouve la classe la plus proche d'un point donné.
        
        Args:
            point: Coordonnées du point (x, y)
            class_boxes: Liste des boîtes de classe avec leurs informations
            
        Returns:
            La classe la plus proche si elle est dans le seuil de distance, sinon None
        """
        nearest_class = None
        min_distance = float('inf')
        
        for class_box in class_boxes:
            box = class_box['box']
            # Vérifier si le point est à l'intérieur de la boîte
            if (box[0] <= point[0] <= box[2]) and (box[1] <= point[1] <= box[3]):
                logger.debug(f"Point {point} à l'intérieur de la classe {class_box['label']}")
                return class_box
            
            # Calculer la distance au bord de la boîte le plus proche
            dist_x = max(box[0] - point[0], 0, point[0] - box[2])
            dist_y = max(box[1] - point[1], 0, point[1] - box[3])
            distance = math.sqrt(dist_x**2 + dist_y**2)
            
            if distance < min_distance and distance <= self.distance_threshold:
                min_distance = distance
                nearest_class = class_box
                logger.debug(f"Classe trouvée à distance {distance} pour le point {point}")
        
        return nearest_class
    
    def find_nearest_relation_type(self, point: Tuple[int, int], relation_boxes: List[Dict]) -> Optional[Dict]:
        """
        Trouve le type de relation le plus proche d'un point donné.
        
        Args:
            point: Coordonnées du point (x, y)
            relation_boxes: Liste des boîtes de relation avec leurs informations
            
        Returns:
            Dictionnaire contenant les informations sur la relation trouvée, ou None
        """
        nearest_relation = None
        min_distance = float('inf')
        
        for relation_box in relation_boxes:
            box = relation_box['box']
            # Vérifier si le point est à l'intérieur de la boîte
            if (box[0] <= point[0] <= box[2]) and (box[1] <= point[1] <= box[3]):
                logger.debug(f"Point {point} à l'intérieur de la relation {relation_box['class_name']}")
                return relation_box
            
            # Calculer la distance au centre de la boîte
            center_x = (box[0] + box[2]) // 2
            center_y = (box[1] + box[3]) // 2
            distance = self.calculate_distance(point, (center_x, center_y))
            
            # La boîte de relation doit être à une certaine distance maximale
            if distance < min_distance and distance <= self.distance_threshold:
                min_distance = distance
                nearest_relation = relation_box
                logger.debug(f"Relation '{relation_box['class_name']}' trouvée à distance {distance} pour le point {point}")
        
        return nearest_relation
    
    def extract_class_name(self, class_text: str) -> str:
        """
        Extrait le nom de la classe à partir du texte extrait.
        
        Args:
            class_text: Texte extrait de la classe par Groq
            
        Returns:
            Nom de la classe
        """
        # Si le texte est formaté comme attendu par Groq
        if "NOM_CLASSE:" in class_text:
            lines = class_text.split('\n')
            for line in lines:
                if line.startswith("NOM_CLASSE:"):
                    # Retourner le nom après "NOM_CLASSE:" en supprimant les espaces
                    return line[len("NOM_CLASSE:"):].strip()
        
        # Si format différent, essayer de prendre la première ligne comme nom de classe
        lines = class_text.split('\n')
        if lines:
            return lines[0].strip()
        
        return "Classe sans nom"
    
    def get_relation_priority(self, relation_type: str) -> int:
        """
        Obtient la priorité d'un type de relation.
        Plus le chiffre est petit, plus la priorité est élevée.
        
        Args:
            relation_type: Type de relation
            
        Returns:
            Valeur de priorité (plus petit = plus prioritaire)
        """
        try:
            return self.RELATION_PRIORITY.index(relation_type)
        except ValueError:
            # Si le type n'est pas dans la liste, lui donner la priorité la plus basse
            return len(self.RELATION_PRIORITY)
    
    def prioritize_relation_types(self, relation_types: List[str]) -> str:
        """
        Priorise les types de relations selon l'ordre défini.
        
        Args:
            relation_types: Liste des types de relations détectés
            
        Returns:
            Le type de relation le plus prioritaire
        """
        if not relation_types:
            return 'association'  # Type par défaut
        
        # Filtrer les valeurs None
        relation_types = [r for r in relation_types if r]
        
        if not relation_types:
            return 'association'
        
        # Trier par priorité (du plus prioritaire au moins prioritaire)
        sorted_types = sorted(relation_types, key=self.get_relation_priority)
        return sorted_types[0]
    
    def process_detections(self, model1_results: List[Dict], model2_results: List[Dict], extracted_texts: Dict) -> List[Dict]:
        """
        Traite les détections des deux modèles et génère des informations sur les relations.
        
        Args:
            model1_results: Résultats du modèle 1 (classes et flèches)
            model2_results: Résultats du modèle 2 (types de relations)
            extracted_texts: Dictionnaire des textes extraits par classe
            
        Returns:
            Liste de dictionnaires décrivant les relations trouvées
        """
        logger.info("Début du traitement des détections")
        
        # Extraire les classes et les flèches du modèle 1
        class_boxes = []
        arrow_boxes = []
        
        for detection in model1_results:
            if detection['class_name'] == 'class':
                # Stocker l'ID de la classe avec ses coordonnées
                class_id = detection.get('id', '')
                class_label = detection.get('label', '')
                
                # Vérifier si le texte extrait est disponible pour cette classe
                class_text = extracted_texts.get(class_id, extracted_texts.get(class_label, "Classe sans nom"))
                
                class_boxes.append({
                    'box': detection['box'],
                    'id': class_id,
                    'label': class_label,
                    'text': class_text
                })
            elif detection['class_name'] == 'arrow':
                arrow_boxes.append({
                    'box': detection['box'],
                    'id': detection.get('id', ''),
                    'label': detection.get('label', '')
                })
        
        # Extraire les relations du modèle 2
        relation_boxes = []
        for detection in model2_results:
            if detection['class_name'] in self.MODEL2_NAMES.values():
                relation_boxes.append({
                    'box': detection['box'],
                    'class_name': detection['class_name'],
                    'id': detection.get('id', ''),
                    'label': detection.get('label', ''),
                    'confidence': detection.get('confidence', 0.0)
                })
        
        logger.info(f"Détections extraites: {len(class_boxes)} classes, {len(arrow_boxes)} flèches, {len(relation_boxes)} relations")
        
        # Afficher les coordonnées des classes pour le debug
        for cls in class_boxes:
            logger.debug(f"Classe {cls['label']} à la position {cls['box']}")
        
        # Afficher les coordonnées des flèches pour le debug
        for arr in arrow_boxes:
            start, end = self.get_arrow_endpoints(arr['box'])
            logger.debug(f"Flèche {arr['label']} de {start} à {end}")
        
        # Analyser chaque flèche pour identifier les relations
        relations = []
        
        for arrow in arrow_boxes:
            # Obtenir les extrémités estimées de la flèche
            start_point, end_point = self.get_arrow_endpoints(arrow['box'])
            
            # Calculer la longueur de la flèche
            arrow_length = self.calculate_arrow_length(start_point, end_point)
            
            # Vérifier si la flèche n'est pas trop courte (symbole erroné potentiel)
            if arrow_length < self.MIN_ARROW_LENGTH:
                logger.warning(f"Flèche {arrow.get('label', 'sans nom')} trop courte ({arrow_length:.2f} px), ignorée")
                continue
            
            # Afficher les points calculés pour le debug
            logger.debug(f"Points calculés pour la flèche {arrow.get('label', 'sans nom')}: {start_point} -> {end_point}")
            
            # Trouver les classes les plus proches des deux extrémités de la flèche
            start_class = self.find_nearest_class(start_point, class_boxes)
            end_class = self.find_nearest_class(end_point, class_boxes)
            
            # Afficher les classes trouvées pour le debug
            if start_class:
                logger.debug(f"Classe de départ trouvée: {start_class['label']}")
            if end_class:
                logger.debug(f"Classe d'arrivée trouvée: {end_class['label']}")
            
            # Vérifier si c'est une relation réflexive (même classe)
            is_reflexive = self.is_same_class(start_class, end_class)
            
            # Trouver les types de relations les plus proches des extrémités et du milieu de la flèche
            relation_type_start = self.find_nearest_relation_type(start_point, relation_boxes)
            relation_type_end = self.find_nearest_relation_type(end_point, relation_boxes)
            
            # Calculer le point milieu de la flèche
            mid_point = ((start_point[0] + end_point[0]) // 2, (start_point[1] + end_point[1]) // 2)
            relation_type_mid = self.find_nearest_relation_type(mid_point, relation_boxes)
            
            # Afficher les types de relations trouvés pour le debug
            logger.debug(f"Type de relation au départ: {relation_type_start['class_name'] if relation_type_start else None}")
            logger.debug(f"Type de relation à l'arrivée: {relation_type_end['class_name'] if relation_type_end else None}")
            logger.debug(f"Type de relation au milieu: {relation_type_mid['class_name'] if relation_type_mid else None}")
            
            # Liste des types de relations détectés avec leur confiance
            detected_relations = []
            for rel in [relation_type_start, relation_type_end, relation_type_mid]:
                if rel:
                    detected_relations.append((rel['class_name'], rel.get('confidence', 0.0)))
            
            # Si plusieurs types, prendre celui avec la meilleure confiance et la meilleure priorité
            if detected_relations:
                # Trier d'abord par priorité, puis par confiance décroissante
                detected_relations.sort(key=lambda x: (self.get_relation_priority(x[0]), -x[1]))
                final_relation_type = detected_relations[0][0]
            else:
                final_relation_type = 'association'
            
            # Stocker la relation selon différents cas
            relation_info = {
                'arrow_id': arrow.get('id', ''),
                'arrow_label': arrow.get('label', ''),
                'start_point': start_point,
                'end_point': end_point,
                'relation_type': final_relation_type,
                'is_reflexive': is_reflexive,
                'orphan': False,  # Par défaut, pas orphelin
                'length': arrow_length
            }
            
            # Vérifier si c'est une flèche orpheline (pas de classe à une ou deux extrémités)
            if not start_class and not end_class:
                relation_info['orphan'] = True
                relation_info['start_class'] = None
                relation_info['end_class'] = None
                relation_info['start_class_name'] = None
                relation_info['end_class_name'] = None
                relation_info['relation_phrase'] = f"Flèche {arrow.get('label', '')} non interprétable (relation orpheline, aucune classe aux extrémités)"
                relations.append(relation_info)
                logger.warning(f"Flèche orpheline détectée: {arrow.get('label', 'sans nom')}")
                continue
            
            # Cas où une seule extrémité est liée à une classe
            if not start_class or not end_class:
                relation_info['orphan'] = True
                relation_info['start_class'] = start_class
                relation_info['end_class'] = end_class
                relation_info['start_class_name'] = self.extract_class_name(start_class['text']) if start_class else None
                relation_info['end_class_name'] = self.extract_class_name(end_class['text']) if end_class else None
                
                # Phrase décrivant la relation orpheline
                if start_class:
                    start_name = self.extract_class_name(start_class['text'])
                    relation_info['relation_phrase'] = f"Flèche partant de {start_name} sans classe d'arrivée identifiée (relation orpheline)"
                else:
                    end_name = self.extract_class_name(end_class['text'])
                    relation_info['relation_phrase'] = f"Flèche arrivant à {end_name} sans classe de départ identifiée (relation orpheline)"
                
                relations.append(relation_info)
                logger.warning(f"Flèche semi-orpheline détectée: {arrow.get('label', 'sans nom')}")
                continue
            
            # Cas standard: deux classes reliées par une flèche
            # Extraire les noms des classes
            start_class_name = self.extract_class_name(start_class['text'])
            end_class_name = self.extract_class_name(end_class['text'])
            
            relation_info['start_class'] = start_class
            relation_info['end_class'] = end_class
            relation_info['start_class_name'] = start_class_name
            relation_info['end_class_name'] = end_class_name
            
            # Description de la relation
            relation_description = self.RELATION_DESCRIPTIONS.get(final_relation_type, "association")
            
            # Formatage de phrase selon le type de relation et si c'est réflexif
            if is_reflexive:
                relation_info['relation_phrase'] = f"La classe {start_class_name} a une relation avec elle-même ({relation_description})"
            elif final_relation_type == "generalization":
                relation_info['relation_phrase'] = f"{end_class_name} hérite de {start_class_name} ({relation_description})"
            elif final_relation_type == "composition":
                relation_info['relation_phrase'] = f"{start_class_name} contient {end_class_name} comme partie intégrante ({relation_description})"
            elif final_relation_type == "aggregation":
                relation_info['relation_phrase'] = f"{start_class_name} contient des instances de {end_class_name} ({relation_description})"
            elif final_relation_type == "one-way-association":
                relation_info['relation_phrase'] = f"{start_class_name} est associé à {end_class_name} de façon unidirectionnelle ({relation_description})"
            else:
                relation_info['relation_phrase'] = f"Il y a une relation de {relation_description} entre {start_class_name} et {end_class_name}"
            
            relations.append(relation_info)
            logger.info(f"Relation détectée: {relation_info['relation_phrase']}")
        
        # Vérifier s'il existe des relations flottantes (types de relations sans flèche associée)
        floating_relations = []
        for relation in relation_boxes:
            is_floating = True
            relation_center = self.get_box_center(relation['box'])
            
            # Vérifier si cette relation est associée à une flèche
            for rel_info in relations:
                if not rel_info['orphan']:
                    # Calculer la distance aux extrémités et au milieu de la flèche
                    dist_start = self.calculate_distance(relation_center, rel_info['start_point'])
                    dist_end = self.calculate_distance(relation_center, rel_info['end_point'])
                    mid_point = ((rel_info['start_point'][0] + rel_info['end_point'][0]) // 2, 
                                (rel_info['start_point'][1] + rel_info['end_point'][1]) // 2)
                    dist_mid = self.calculate_distance(relation_center, mid_point)
                    
                    # Si la relation est proche d'une flèche, elle n'est pas flottante
                    if min(dist_start, dist_end, dist_mid) <= self.distance_threshold:
                        is_floating = False
                        break
            
            if is_floating:
                floating_relations.append({
                    'type': 'floating_relation',
                    'relation_type': relation['class_name'],
                    'box': relation['box'],
                    'center': relation_center,
                    'relation_phrase': f"Relation de type {relation['class_name']} détectée sans flèche associée (relation flottante)"
                })
                logger.warning(f"Relation flottante détectée: {relation['class_name']} à {relation_center}")
        
        # Si demandé, ajouter les relations flottantes à la liste des relations
        # relations.extend(floating_relations)
        
        # Si aucune relation n'a été trouvée, ajouter un message d'information
        if not relations:
            relations.append({
                'type': 'no_relations',
                'relation_phrase': "Aucune relation entre classes n'a été détectée sur ce diagramme."
            })
            logger.info("Aucune relation détectée")
        
        return relations
    
    def format_relations_output(self, relations: List[Dict]) -> str:
        """
        Formate la sortie des relations pour l'affichage.
        
        Args:
            relations: Liste des dictionnaires de relations
            
        Returns:
            Texte formaté pour l'affichage
        """
        if not relations:
            return "Aucune relation détectée."
        
        # Compter les types de relations valides
        relation_counts = {}
        valid_relations = [r for r in relations if not r.get('orphan', False) and 'relation_type' in r]
        
        for relation in valid_relations:
            rel_type = relation['relation_type']
            if rel_type in relation_counts:
                relation_counts[rel_type] += 1
            else:
                relation_counts[rel_type] = 1
        
        # Créer un résumé des relations
        summary_lines = []
        for rel_type, count in relation_counts.items():
            summary_lines.append(f"• {count} relation(s) de type {rel_type}")
        
        # Formater toutes les phrases de relations
        relation_lines = []
        for relation in relations:
            if 'relation_phrase' in relation:
                relation_lines.append(f"• {relation['relation_phrase']}")
        
        # Combiner le résumé et les phrases détaillées
        if summary_lines:
            output = "\n\n----- RÉSUMÉ DES RELATIONS -----\n" + "\n".join(summary_lines)
        else:
            output = ""
        
        output += "\n\n----- RELATIONS DÉTECTÉES -----\n" + "\n".join(relation_lines)
        
        return output


# Fonction pour convertir les résultats bruts des modèles au format attendu par le processeur
def convert_model_results(yolo_results, model_type):
    """
    Convertit les résultats bruts du modèle YOLO au format attendu par le processeur.
    
    Args:
        yolo_results: Résultats bruts du modèle YOLO
        model_type: Type de modèle ('model1' pour classes/flèches, 'model2' pour relations)
        
    Returns:
        Liste formatée des détections
    """
    formatted_results = []
    
    MODEL1_NAMES = {0: 'arrow', 1: 'class'}
    MODEL2_NAMES = {
        0: 'A', 
        1: 'C', 
        2: 'generalization', 
        3: 'endpoint', 
        4: 'one-way-association', 
        5: 'composition', 
        6: 'aggregation'
    }
    
    for result in yolo_results:
        if not hasattr(result, 'boxes') or not hasattr(result.boxes, 'xyxy'):
            continue
            
        boxes = result.boxes.xyxy.cpu().numpy()
        classes = result.boxes.cls.cpu().numpy()
        scores = result.boxes.conf.cpu().numpy()
        
        for i in range(len(boxes)):
            x1, y1, x2, y2 = map(int, boxes[i])
            class_index = int(classes[i])
            confidence = float(scores[i])
            
            if model_type == 'model1' and class_index in MODEL1_NAMES:
                class_name = MODEL1_NAMES[class_index]
                id_suffix = len([x for x in formatted_results if x['class_name'] == class_name]) + 1
                formatted_results.append({
                    'box': (x1, y1, x2, y2),
                    'class_name': class_name,
                    'id': f"{class_name}_{id_suffix}",
                    'label': f"{class_name} {id_suffix}",
                    'confidence': confidence
                })
            elif model_type == 'model2' and class_index in MODEL2_NAMES:
                class_name = MODEL2_NAMES[class_index]
                id_suffix = len([x for x in formatted_results if x['class_name'] == class_name]) + 1
                formatted_results.append({
                    'box': (x1, y1, x2, y2),
                    'class_name': class_name,
                    'id': f"{class_name}_{id_suffix}",
                    'label': f"{class_name} {id_suffix}",
                    'confidence': confidence
                })
    return formatted_results

# Fonction principale pour traiter les détections et extraire les relations
def process_diagram_relations(model1_results, model2_results, extracted_texts, distance_threshold=100):
    """
    Traite un diagramme UML et extrait les relations entre classes.
    
    Args:
        model1_results: Résultats du modèle 1 (classes et flèches)
        model2_results: Résultats du modèle 2 (types de relations)
        extracted_texts: Dictionnaire des textes extraits des classes
        distance_threshold: Seuil de distance pour associer les objets
        
    Returns:
        Texte formaté décrivant les relations détectées
    """
    # Convertir les résultats bruts des modèles au format attendu
    formatted_model1 = convert_model_results(model1_results, 'model1')
    formatted_model2 = convert_model_results(model2_results, 'model2')
    
    # Initialiser le processeur de relations
    processor = RelationProcessor(distance_threshold=distance_threshold)
    
    # Traiter les détections
    relations = processor.process_detections(formatted_model1, formatted_model2, extracted_texts)

    # Filtrer les relations dupliquées
    filtered_relations = filter_duplicate_relations(relations)

    # Formater la sortie
    output_text = processor.format_relations_output(filtered_relations)
    
    return output_text, filtered_relations





# Fonctions utilitaires supplémentaires

def filter_duplicate_relations(relations):
    """
    Filtre les relations dupliquées en fonction des classes impliquées et du type de relation.

    Args:
        relations: Liste des relations détectées

    Returns:
        Liste des relations filtrées
    """
    # Créer un dictionnaire pour regrouper les relations par paire de classes et type
    relation_groups = {}
    orphan_relations = []

    for relation in relations:
        # Traiter les relations orphelines séparément
        if relation.get('orphan', False):
            orphan_relations.append(relation)
            continue

        # Créer une clé unique pour chaque paire de classes et type de relation
        start_class = relation.get('start_class_name', '')
        end_class = relation.get('end_class_name', '')
        rel_type = relation.get('relation_type', 'unknown')

        # Créer une clé normalisée (ordre alphabétique pour les relations bidirectionnelles)
        if rel_type in ['association', 'endpoint']:
            # Pour les associations, l'ordre n'importe pas
            key = f"{min(start_class, end_class)}_{max(start_class, end_class)}_{rel_type}"
        else:
            # Pour les autres relations (héritage, composition, etc.), l'ordre importe
            key = f"{start_class}_{end_class}_{rel_type}"

        if key not in relation_groups:
            relation_groups[key] = []
        relation_groups[key].append(relation)

    # Filtrer les doublons dans chaque groupe
    filtered_relations = []

    for key, group in relation_groups.items():
        if len(group) == 1:
            # Pas de doublon, ajouter directement
            filtered_relations.append(group[0])
        else:
            # Plusieurs relations pour la même paire de classes et type
            # Garder celle avec la meilleure confiance ou la première si pas de confiance
            best_relation = max(group, key=lambda r: r.get('confidence', 0))
            filtered_relations.append(best_relation)

            # Log pour information
            logger.info(f"Relation dupliquée détectée pour {key}: {len(group)} relations trouvées, gardée celle avec confiance {best_relation.get('confidence', 0)}")

    # Ajouter les relations orphelines à la fin
    filtered_relations.extend(orphan_relations)

    return filtered_relations






