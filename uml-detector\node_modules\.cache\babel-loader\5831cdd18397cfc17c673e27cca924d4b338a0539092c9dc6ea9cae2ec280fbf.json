{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\FixTorchUMLDGM\\\\uml-detector\\\\src\\\\components\\\\imageUp\\\\ImageUploader.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from \"react\";\nimport LoadingSpinner from \"./LoadingSpinner\";\nimport AnalysisResults from \"./AnalysisResults\";\nimport { useHistory } from '../../context/HistoryContext';\nimport { useLanguage } from '../../context/LanguageContext';\n\n// Utilitaires intégrés pour la gestion des fichiers\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst fileToBase64 = file => {\n  return new Promise((resolve, reject) => {\n    const reader = new FileReader();\n    reader.onload = () => {\n      if (typeof reader.result === 'string') {\n        resolve(reader.result);\n      } else {\n        reject(new Error('Erreur lors de la conversion en Base64'));\n      }\n    };\n    reader.onerror = () => {\n      reject(new Error('Erreur lors de la lecture du fichier'));\n    };\n    reader.readAsDataURL(file);\n  });\n};\n\n// Fonction pour redimensionner une image et créer une miniature\nconst createThumbnail = (base64Url, maxWidth = 200, maxHeight = 200, quality = 0.7) => {\n  return new Promise(resolve => {\n    const img = new Image();\n    img.onload = () => {\n      const canvas = document.createElement('canvas');\n      const ctx = canvas.getContext('2d');\n\n      // Calculer les nouvelles dimensions en conservant le ratio\n      let {\n        width,\n        height\n      } = img;\n      if (width > height) {\n        if (width > maxWidth) {\n          height = height * maxWidth / width;\n          width = maxWidth;\n        }\n      } else {\n        if (height > maxHeight) {\n          width = width * maxHeight / height;\n          height = maxHeight;\n        }\n      }\n      canvas.width = width;\n      canvas.height = height;\n\n      // Dessiner l'image redimensionnée\n      ctx.drawImage(img, 0, 0, width, height);\n\n      // Convertir en Base64 avec compression\n      const thumbnailUrl = canvas.toDataURL('image/jpeg', quality);\n      resolve(thumbnailUrl);\n    };\n    img.src = base64Url;\n  });\n};\nconst isBase64Url = url => {\n  return url.startsWith('data:');\n};\nconst ImageUploader = ({\n  darkMode,\n  onAnalysisComplete,\n  savedImageUrl,\n  savedExtractedText,\n  savedTextUrl,\n  onViewAnnotatedImage,\n  onNavigateToUMLExtractor,\n  onUpdateExtractedText\n}) => {\n  _s();\n  const [imageUrl, setImageUrl] = useState(savedImageUrl || null);\n  const [textUrl, setTextUrl] = useState(savedTextUrl || null);\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\n  const [isDragging, setIsDragging] = useState(false);\n  const [extractedText, setExtractedText] = useState(savedExtractedText || \"\");\n  const [analysisProgress, setAnalysisProgress] = useState(\"\");\n  const [fileName, setFileName] = useState(\"\");\n  const [isConverting, setIsConverting] = useState(false);\n  const {\n    t\n  } = useLanguage();\n  const {\n    addHistoryItem\n  } = useHistory();\n\n  // Initialiser le texte de progression avec la traduction\n  useEffect(() => {\n    if (!analysisProgress) {\n      setAnalysisProgress(t('imageUploader.progress.waiting'));\n    }\n  }, [t, analysisProgress]);\n\n  // Référence pour éviter les ajouts multiples à l'historique\n  const historyAddedRef = useRef(false);\n  const currentAnalysisId = useRef(\"\");\n\n  // Stocker l'image originale et la miniature séparément\n  const currentImageBase64 = useRef(null);\n  const currentThumbnail = useRef(null);\n\n  // Réinitialiser l'état d'historique lorsqu'une nouvelle analyse commence\n  const startNewAnalysis = () => {\n    historyAddedRef.current = false;\n    currentAnalysisId.current = Date.now().toString();\n    console.log(`🔄 Nouvelle analyse démarrée: ${currentAnalysisId.current}`);\n  };\n\n  // Mettre à jour les états locaux lorsque les props changent\n  useEffect(() => {\n    if (savedImageUrl !== undefined) {\n      setImageUrl(savedImageUrl);\n      if (savedImageUrl && isBase64Url(savedImageUrl)) {\n        currentImageBase64.current = savedImageUrl;\n      }\n    }\n    if (savedExtractedText !== undefined) {\n      setExtractedText(savedExtractedText);\n    }\n    if (savedTextUrl !== undefined) {\n      setTextUrl(savedTextUrl);\n    }\n  }, [savedImageUrl, savedExtractedText, savedTextUrl]);\n\n  // Fonction pour traiter un fichier : conversion en Base64 + création de miniature\n  const processFileToBase64 = async file => {\n    setIsConverting(true);\n    setAnalysisProgress(t('imageUploader.progress.converting'));\n    try {\n      const base64Url = await fileToBase64(file);\n      const thumbnail = await createThumbnail(base64Url);\n      currentImageBase64.current = base64Url;\n      currentThumbnail.current = thumbnail;\n      console.log(`📊 Taille image originale: ${Math.round(base64Url.length / 1024)} KB`);\n      console.log(`🖼️ Taille miniature: ${Math.round(thumbnail.length / 1024)} KB`);\n      return {\n        fullImage: base64Url,\n        thumbnail\n      };\n    } catch (error) {\n      console.error(\"Erreur lors de la conversion en Base64:\", error);\n      throw error;\n    } finally {\n      setIsConverting(false);\n    }\n  };\n  const handleFileChange = async e => {\n    var _e$target$files;\n    const file = (_e$target$files = e.target.files) === null || _e$target$files === void 0 ? void 0 : _e$target$files[0];\n    if (file) {\n      await processImage(file);\n    }\n  };\n  const handleDrop = e => {\n    e.preventDefault();\n    setIsDragging(false);\n    if (e.dataTransfer.files && e.dataTransfer.files[0]) {\n      processImage(e.dataTransfer.files[0]);\n    }\n  };\n  const processImage = async file => {\n    // Commencer une nouvelle analyse\n    startNewAnalysis();\n    setFileName(file.name);\n    setExtractedText(\"\");\n    setTextUrl(null);\n    try {\n      // Convertir et créer la miniature\n      const {\n        fullImage\n      } = await processFileToBase64(file);\n      setImageUrl(fullImage);\n\n      // Analyser l'image\n      analyzeImage(file);\n    } catch (error) {\n      console.error(\"Erreur lors du traitement du fichier:\", error);\n      alert(t('imageUploader.error.processing'));\n    }\n  };\n\n  // Fonction pour ajouter à l'historique une seule fois avec gestion optimisée\n  const addToHistoryOnce = async (imageUrl, text, textUrl) => {\n    if (historyAddedRef.current) {\n      console.log(`⚠️ Tentative d'ajout multiple évitée pour l'analyse: ${currentAnalysisId.current}`);\n      return;\n    }\n    try {\n      // Marquer immédiatement pour éviter les doublons\n      historyAddedRef.current = true;\n\n      // Utiliser la miniature pour l'historique (beaucoup plus légère)\n      const thumbnail = currentThumbnail.current;\n      if (!thumbnail) {\n        throw new Error(t('imageUploader.error.thumbnail'));\n      }\n      const title = fileName ? `${fileName} - ${new Date().toLocaleDateString('fr-FR')}` : `Diagramme UML - ${new Date().toLocaleDateString('fr-FR')}`;\n      const annotatedImageUrl = fileName ? `http://127.0.0.1:8000/annotated_image_${encodeURIComponent(fileName)}` : \"http://127.0.0.1:8000/annotated_image.jpg\";\n      console.log(`💾 Ajout à l'historique pour l'analyse: ${currentAnalysisId.current}`);\n      await addHistoryItem({\n        title: title,\n        originalImageUrl: annotatedImageUrl,\n        // URL du serveur au lieu de Base64\n        annotatedImageUrl: annotatedImageUrl,\n        extractedText: text,\n        thumbnail: thumbnail,\n        // Miniature optimisée\n        userId: ''\n      });\n      console.log(`✅ Ajouté à l'historique avec succès: ${currentAnalysisId.current}`);\n    } catch (error) {\n      console.error(\"Erreur lors de l'ajout à l'historique:\", error);\n      // Réinitialiser en cas d'erreur pour permettre une nouvelle tentative\n      historyAddedRef.current = false;\n    }\n  };\n\n  // Fonction pour récupérer le texte extrait\n  useEffect(() => {\n    if (!textUrl || !imageUrl || historyAddedRef.current) return;\n    let isMounted = true;\n    const fetchResults = async () => {\n      setAnalysisProgress(t('imageUploader.progress.fetching'));\n      try {\n        const response = await fetch(textUrl);\n        if (!response.ok) {\n          throw new Error(t('imageUploader.error.fetchText'));\n        }\n        const text = await response.text();\n        if (!isMounted) return;\n        setExtractedText(text);\n        setAnalysisProgress(\"Analyse terminée\");\n        if (onAnalysisComplete) {\n          onAnalysisComplete(imageUrl, text, textUrl);\n        }\n\n        // Ajouter à l'historique une seule fois\n        await addToHistoryOnce(imageUrl, text, textUrl);\n      } catch (error) {\n        if (isMounted) {\n          console.error(\"Erreur lors de la récupération du texte :\", error);\n          setAnalysisProgress(\"Erreur lors de la récupération des résultats\");\n        }\n      }\n    };\n    fetchResults();\n    return () => {\n      isMounted = false;\n    };\n  }, [textUrl, imageUrl, onAnalysisComplete]);\n  const analyzeImage = async file => {\n    setIsAnalyzing(true);\n    setAnalysisProgress(\"Détection des classes et relations avec notre modèle IA...\");\n    const formData = new FormData();\n    formData.append(\"file\", file);\n    try {\n      const timeoutId = setTimeout(() => {\n        if (isAnalyzing) {\n          setAnalysisProgress(\"Analyse en cours ... Cette étape peut prendre quelques instants.\");\n        }\n      }, 3000);\n      const response = await fetch(\"http://127.0.0.1:8000/detect/\", {\n        method: \"POST\",\n        body: formData\n      });\n      clearTimeout(timeoutId);\n      if (!response.ok) {\n        try {\n          const errorData = await response.json();\n          throw new Error(`Erreur serveur: ${errorData.detail || response.status}`);\n        } catch (jsonError) {\n          throw new Error(`Erreur HTTP: ${response.status}`);\n        }\n      }\n      const textFileUrl = \"http://127.0.0.1:8000/resultats_classes.txt\";\n      setTextUrl(textFileUrl);\n    } catch (error) {\n      console.error(\"Erreur de connexion ou analyse :\", error);\n      setAnalysisProgress(\"Erreur pendant l'analyse de l'image\");\n      alert(`Erreur pendant l'analyse de l'image: ${error instanceof Error ? error.message : String(error)}`);\n    } finally {\n      setIsAnalyzing(false);\n    }\n  };\n  const resetAnalysis = () => {\n    // Réinitialiser tous les états\n    setImageUrl(null);\n    setTextUrl(null);\n    setExtractedText(\"\");\n    setFileName(\"\");\n    setAnalysisProgress(\"En attente de l'image...\");\n\n    // Réinitialiser l'état d'historique et les références\n    historyAddedRef.current = false;\n    currentAnalysisId.current = \"\";\n    currentImageBase64.current = null;\n    currentThumbnail.current = null;\n    console.log(\"🔄 Analyse réinitialisée\");\n    if (onAnalysisComplete) {\n      onAnalysisComplete(\"\", \"\", null);\n    }\n  };\n  const handleUpdateExtractedText = newText => {\n    console.log(\"Mise à jour du texte extrait dans ImageUploader.tsx\");\n    console.log(\"Nouveau texte:\", newText.substring(0, 100) + \"...\");\n\n    // Mettre à jour l'état local\n    setExtractedText(newText);\n\n    // Propager la mise à jour au parent\n    if (onUpdateExtractedText) {\n      onUpdateExtractedText(newText);\n    }\n\n    // Forcer un re-rendu si nécessaire\n    setTimeout(() => {\n      console.log(\"Forcer un re-rendu après mise à jour dans ImageUploader\");\n      setExtractedText(prevText => {\n        if (prevText === newText) {\n          // Si le texte n'a pas changé, forcer un re-rendu en créant une nouvelle référence\n          return newText + \" \"; // Ajouter un espace pour forcer un changement\n        }\n        return newText;\n      });\n    }, 100);\n  };\n  const getStyles = () => ({\n    container: {\n      maxWidth: \"800px\",\n      margin: \"0 auto\",\n      padding: \"20px\",\n      color: darkMode ? \"#e0e0e0\" : \"#333\"\n    },\n    dropZone: {\n      border: `2px dashed ${darkMode ? '#4f46e5' : '#2563eb'}`,\n      borderRadius: '8px',\n      padding: '40px',\n      textAlign: 'center',\n      backgroundColor: isDragging ? darkMode ? 'rgba(79, 70, 229, 0.2)' : 'rgba(37, 99, 235, 0.1)' : darkMode ? '#2d2d2d' : '#f5f5f5',\n      margin: '20px 0',\n      cursor: 'pointer',\n      transition: 'all 0.3s ease'\n    },\n    fileInput: {\n      display: 'none'\n    },\n    selectButton: {\n      backgroundColor: darkMode ? '#4f46e5' : '#2563eb',\n      color: 'white',\n      padding: '10px 20px',\n      borderRadius: '5px',\n      border: 'none',\n      cursor: 'pointer',\n      marginTop: '10px',\n      fontSize: '16px'\n    },\n    resultsContainer: {\n      backgroundColor: darkMode ? '#333' : '#fff',\n      borderRadius: '8px',\n      padding: '20px',\n      marginTop: '20px',\n      boxShadow: darkMode ? '0 2px 10px rgba(0,0,0,0.5)' : '0 2px 10px rgba(0,0,0,0.1)'\n    }\n  });\n  const styles = getStyles();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: styles.container,\n    children: !imageUrl ? /*#__PURE__*/_jsxDEV(\"div\", {\n      onDragEnter: () => setIsDragging(true),\n      onDragOver: e => e.preventDefault(),\n      onDragLeave: () => setIsDragging(false),\n      onDrop: handleDrop,\n      onClick: () => {\n        var _document$getElementB;\n        return (_document$getElementB = document.getElementById('file-input')) === null || _document$getElementB === void 0 ? void 0 : _document$getElementB.click();\n      },\n      style: styles.dropZone,\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Glissez-d\\xE9posez votre diagramme UML ou cliquez pour s\\xE9lectionner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 430,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        id: \"file-input\",\n        type: \"file\",\n        accept: \"image/*\",\n        onChange: handleFileChange,\n        style: styles.fileInput\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 431,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        style: styles.selectButton,\n        children: \"S\\xE9lectionner un fichier\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 438,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 422,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.resultsContainer,\n      children: isAnalyzing || isConverting ? /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n        darkMode: darkMode,\n        text: analysisProgress\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 443,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(AnalysisResults, {\n        darkMode: darkMode,\n        imageUrl: imageUrl,\n        extractedText: extractedText,\n        onViewAnnotatedImage: onViewAnnotatedImage,\n        onNavigateToUMLExtractor: onNavigateToUMLExtractor,\n        resetAnalysis: resetAnalysis,\n        onUpdateExtractedText: handleUpdateExtractedText\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 445,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 441,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 420,\n    columnNumber: 5\n  }, this);\n};\n_s(ImageUploader, \"/jA0dIktoaErZl5ivxdnuxzusJ4=\", false, function () {\n  return [useLanguage, useHistory];\n});\n_c = ImageUploader;\nexport default ImageUploader;\nvar _c;\n$RefreshReg$(_c, \"ImageUploader\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "LoadingSpinner", "AnalysisResults", "useHistory", "useLanguage", "jsxDEV", "_jsxDEV", "fileToBase64", "file", "Promise", "resolve", "reject", "reader", "FileReader", "onload", "result", "Error", "onerror", "readAsDataURL", "createThumbnail", "base64Url", "max<PERSON><PERSON><PERSON>", "maxHeight", "quality", "img", "Image", "canvas", "document", "createElement", "ctx", "getContext", "width", "height", "drawImage", "thumbnailUrl", "toDataURL", "src", "isBase64Url", "url", "startsWith", "ImageUploader", "darkMode", "onAnalysisComplete", "savedImageUrl", "savedExtractedText", "savedTextUrl", "onViewAnnotatedImage", "onNavigateToUMLExtractor", "onUpdateExtractedText", "_s", "imageUrl", "setImageUrl", "textUrl", "setTextUrl", "isAnalyzing", "setIsAnalyzing", "isDragging", "setIsDragging", "extractedText", "setExtractedText", "analysisProgress", "setAnalysisProgress", "fileName", "setFileName", "isConverting", "setIsConverting", "t", "addHistoryItem", "historyAddedRef", "currentAnalysisId", "currentImageBase64", "currentThumbnail", "startNewAnalysis", "current", "Date", "now", "toString", "console", "log", "undefined", "processFileToBase64", "thumbnail", "Math", "round", "length", "fullImage", "error", "handleFileChange", "e", "_e$target$files", "target", "files", "processImage", "handleDrop", "preventDefault", "dataTransfer", "name", "analyzeImage", "alert", "addToHistoryOnce", "text", "title", "toLocaleDateString", "annotatedImageUrl", "encodeURIComponent", "originalImageUrl", "userId", "isMounted", "fetchResults", "response", "fetch", "ok", "formData", "FormData", "append", "timeoutId", "setTimeout", "method", "body", "clearTimeout", "errorData", "json", "detail", "status", "jsonError", "textFileUrl", "message", "String", "resetAnalysis", "handleUpdateExtractedText", "newText", "substring", "prevText", "getStyles", "container", "margin", "padding", "color", "dropZone", "border", "borderRadius", "textAlign", "backgroundColor", "cursor", "transition", "fileInput", "display", "selectButton", "marginTop", "fontSize", "resultsContainer", "boxShadow", "styles", "style", "children", "onDragEnter", "onDragOver", "onDragLeave", "onDrop", "onClick", "_document$getElementB", "getElementById", "click", "_jsxFileName", "lineNumber", "columnNumber", "id", "type", "accept", "onChange", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/FixTorchUMLDGM/uml-detector/src/components/imageUp/ImageUploader.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from \"react\";\r\nimport Loading<PERSON>pinner from \"./LoadingSpinner\";\r\nimport AnalysisResults from \"./AnalysisResults\";\r\nimport { useHistory } from '../../context/HistoryContext';\r\nimport { useLanguage } from '../../context/LanguageContext';\r\n\r\n// Utilitaires intégrés pour la gestion des fichiers\r\nconst fileToBase64 = (file: File): Promise<string> => {\r\n  return new Promise((resolve, reject) => {\r\n    const reader = new FileReader();\r\n    \r\n    reader.onload = () => {\r\n      if (typeof reader.result === 'string') {\r\n        resolve(reader.result);\r\n      } else {\r\n        reject(new Error('Erreur lors de la conversion en Base64'));\r\n      }\r\n    };\r\n    \r\n    reader.onerror = () => {\r\n      reject(new Error('Erreur lors de la lecture du fichier'));\r\n    };\r\n    \r\n    reader.readAsDataURL(file);\r\n  });\r\n};\r\n\r\n// Fonction pour redimensionner une image et créer une miniature\r\nconst createThumbnail = (base64Url: string, maxWidth = 200, maxHeight = 200, quality = 0.7): Promise<string> => {\r\n  return new Promise((resolve) => {\r\n    const img = new Image();\r\n    img.onload = () => {\r\n      const canvas = document.createElement('canvas');\r\n      const ctx = canvas.getContext('2d')!;\r\n      \r\n      // Calculer les nouvelles dimensions en conservant le ratio\r\n      let { width, height } = img;\r\n      if (width > height) {\r\n        if (width > maxWidth) {\r\n          height = (height * maxWidth) / width;\r\n          width = maxWidth;\r\n        }\r\n      } else {\r\n        if (height > maxHeight) {\r\n          width = (width * maxHeight) / height;\r\n          height = maxHeight;\r\n        }\r\n      }\r\n      \r\n      canvas.width = width;\r\n      canvas.height = height;\r\n      \r\n      // Dessiner l'image redimensionnée\r\n      ctx.drawImage(img, 0, 0, width, height);\r\n      \r\n      // Convertir en Base64 avec compression\r\n      const thumbnailUrl = canvas.toDataURL('image/jpeg', quality);\r\n      resolve(thumbnailUrl);\r\n    };\r\n    img.src = base64Url;\r\n  });\r\n};\r\n\r\nconst isBase64Url = (url: string): boolean => {\r\n  return url.startsWith('data:');\r\n};\r\n\r\ninterface ImageUploaderProps {\r\n  onImageUpload?: (file: File) => void;\r\n  onAnalysisComplete?: (imageUrl: string, text: string, textFileUrl: string | null) => void;\r\n  darkMode: boolean;\r\n  savedImageUrl?: string | null;\r\n  savedExtractedText?: string;\r\n  savedTextUrl?: string | null;\r\n  onViewAnnotatedImage?: () => void;\r\n  onNavigateToUMLExtractor?: () => void;\r\n  onUpdateExtractedText?: (newText: string) => void;\r\n}\r\n\r\nconst ImageUploader: React.FC<ImageUploaderProps> = ({ \r\n  darkMode, \r\n  onAnalysisComplete, \r\n  savedImageUrl,\r\n  savedExtractedText,\r\n  savedTextUrl,\r\n  onViewAnnotatedImage,\r\n  onNavigateToUMLExtractor,\r\n  onUpdateExtractedText\r\n}) => {\r\n  const [imageUrl, setImageUrl] = useState<string | null>(savedImageUrl || null);\r\n  const [textUrl, setTextUrl] = useState<string | null>(savedTextUrl || null);\r\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\r\n  const [isDragging, setIsDragging] = useState(false);\r\n  const [extractedText, setExtractedText] = useState<string>(savedExtractedText || \"\");\r\n  const [analysisProgress, setAnalysisProgress] = useState<string>(\"\");\r\n  const [fileName, setFileName] = useState<string>(\"\");\r\n  const [isConverting, setIsConverting] = useState(false);\r\n\r\n  const { t } = useLanguage();\r\n\r\n  const { addHistoryItem } = useHistory();\r\n\r\n  // Initialiser le texte de progression avec la traduction\r\n  useEffect(() => {\r\n    if (!analysisProgress) {\r\n      setAnalysisProgress(t('imageUploader.progress.waiting'));\r\n    }\r\n  }, [t, analysisProgress]);\r\n  \r\n  // Référence pour éviter les ajouts multiples à l'historique\r\n  const historyAddedRef = useRef<boolean>(false);\r\n  const currentAnalysisId = useRef<string>(\"\");\r\n  \r\n  // Stocker l'image originale et la miniature séparément\r\n  const currentImageBase64 = useRef<string | null>(null);\r\n  const currentThumbnail = useRef<string | null>(null);\r\n\r\n  // Réinitialiser l'état d'historique lorsqu'une nouvelle analyse commence\r\n  const startNewAnalysis = () => {\r\n    historyAddedRef.current = false;\r\n    currentAnalysisId.current = Date.now().toString();\r\n    console.log(`🔄 Nouvelle analyse démarrée: ${currentAnalysisId.current}`);\r\n  };\r\n\r\n  // Mettre à jour les états locaux lorsque les props changent\r\n  useEffect(() => {\r\n    if (savedImageUrl !== undefined) {\r\n      setImageUrl(savedImageUrl);\r\n      if (savedImageUrl && isBase64Url(savedImageUrl)) {\r\n        currentImageBase64.current = savedImageUrl;\r\n      }\r\n    }\r\n    if (savedExtractedText !== undefined) {\r\n      setExtractedText(savedExtractedText);\r\n    }\r\n    if (savedTextUrl !== undefined) {\r\n      setTextUrl(savedTextUrl);\r\n    }\r\n  }, [savedImageUrl, savedExtractedText, savedTextUrl]);\r\n\r\n  // Fonction pour traiter un fichier : conversion en Base64 + création de miniature\r\n  const processFileToBase64 = async (file: File): Promise<{ fullImage: string; thumbnail: string }> => {\r\n    setIsConverting(true);\r\n    setAnalysisProgress(t('imageUploader.progress.converting'));\r\n\r\n    try {\r\n      const base64Url = await fileToBase64(file);\r\n      const thumbnail = await createThumbnail(base64Url);\r\n      \r\n      currentImageBase64.current = base64Url;\r\n      currentThumbnail.current = thumbnail;\r\n      \r\n      console.log(`📊 Taille image originale: ${Math.round(base64Url.length / 1024)} KB`);\r\n      console.log(`🖼️ Taille miniature: ${Math.round(thumbnail.length / 1024)} KB`);\r\n      \r\n      return { fullImage: base64Url, thumbnail };\r\n    } catch (error) {\r\n      console.error(\"Erreur lors de la conversion en Base64:\", error);\r\n      throw error;\r\n    } finally {\r\n      setIsConverting(false);\r\n    }\r\n  };\r\n\r\n  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const file = e.target.files?.[0];\r\n    if (file) {\r\n      await processImage(file);\r\n    }\r\n  };\r\n\r\n  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {\r\n    e.preventDefault();\r\n    setIsDragging(false);\r\n    if (e.dataTransfer.files && e.dataTransfer.files[0]) {\r\n      processImage(e.dataTransfer.files[0]);\r\n    }\r\n  };\r\n\r\n  const processImage = async (file: File) => {\r\n    // Commencer une nouvelle analyse\r\n    startNewAnalysis();\r\n    \r\n    setFileName(file.name);\r\n    setExtractedText(\"\");\r\n    setTextUrl(null);\r\n    \r\n    try {\r\n      // Convertir et créer la miniature\r\n      const { fullImage } = await processFileToBase64(file);\r\n      setImageUrl(fullImage);\r\n      \r\n      // Analyser l'image\r\n      analyzeImage(file);\r\n    } catch (error) {\r\n      console.error(\"Erreur lors du traitement du fichier:\", error);\r\n      alert(t('imageUploader.error.processing'));\r\n    }\r\n  };\r\n\r\n  // Fonction pour ajouter à l'historique une seule fois avec gestion optimisée\r\n  const addToHistoryOnce = async (imageUrl: string, text: string, textUrl: string) => {\r\n    if (historyAddedRef.current) {\r\n      console.log(`⚠️ Tentative d'ajout multiple évitée pour l'analyse: ${currentAnalysisId.current}`);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Marquer immédiatement pour éviter les doublons\r\n      historyAddedRef.current = true;\r\n      \r\n      // Utiliser la miniature pour l'historique (beaucoup plus légère)\r\n      const thumbnail = currentThumbnail.current;\r\n      if (!thumbnail) {\r\n        throw new Error(t('imageUploader.error.thumbnail'));\r\n      }\r\n      \r\n      const title = fileName \r\n        ? `${fileName} - ${new Date().toLocaleDateString('fr-FR')}` \r\n        : `Diagramme UML - ${new Date().toLocaleDateString('fr-FR')}`;\r\n        \r\n      const annotatedImageUrl = fileName\r\n        ? `http://127.0.0.1:8000/annotated_image_${encodeURIComponent(fileName)}`\r\n        : \"http://127.0.0.1:8000/annotated_image.jpg\";\r\n      \r\n      console.log(`💾 Ajout à l'historique pour l'analyse: ${currentAnalysisId.current}`);\r\n        \r\n      await addHistoryItem({\r\n        title: title,\r\n        originalImageUrl: annotatedImageUrl, // URL du serveur au lieu de Base64\r\n        annotatedImageUrl: annotatedImageUrl,\r\n        extractedText: text,\r\n        thumbnail: thumbnail, // Miniature optimisée\r\n        userId: ''\r\n      });\r\n      \r\n      console.log(`✅ Ajouté à l'historique avec succès: ${currentAnalysisId.current}`);\r\n    } catch (error) {\r\n      console.error(\"Erreur lors de l'ajout à l'historique:\", error);\r\n      // Réinitialiser en cas d'erreur pour permettre une nouvelle tentative\r\n      historyAddedRef.current = false;\r\n    }\r\n  };\r\n\r\n  // Fonction pour récupérer le texte extrait\r\n  useEffect(() => {\r\n    if (!textUrl || !imageUrl || historyAddedRef.current) return;\r\n\r\n    let isMounted = true;\r\n\r\n    const fetchResults = async () => {\r\n      setAnalysisProgress(t('imageUploader.progress.fetching'));\r\n\r\n      try {\r\n        const response = await fetch(textUrl);\r\n        if (!response.ok) {\r\n          throw new Error(t('imageUploader.error.fetchText'));\r\n        }\r\n        \r\n        const text = await response.text();\r\n        \r\n        if (!isMounted) return;\r\n        \r\n        setExtractedText(text);\r\n        setAnalysisProgress(\"Analyse terminée\");\r\n        \r\n        if (onAnalysisComplete) {\r\n          onAnalysisComplete(imageUrl, text, textUrl);\r\n        }\r\n        \r\n        // Ajouter à l'historique une seule fois\r\n        await addToHistoryOnce(imageUrl, text, textUrl);\r\n      } catch (error) {\r\n        if (isMounted) {\r\n          console.error(\"Erreur lors de la récupération du texte :\", error);\r\n          setAnalysisProgress(\"Erreur lors de la récupération des résultats\");\r\n        }\r\n      }\r\n    };\r\n\r\n    fetchResults();\r\n\r\n    return () => {\r\n      isMounted = false;\r\n    };\r\n  }, [textUrl, imageUrl, onAnalysisComplete]);\r\n\r\n  const analyzeImage = async (file: File) => {\r\n    setIsAnalyzing(true);\r\n    setAnalysisProgress(\"Détection des classes et relations avec notre modèle IA...\");\r\n    \r\n    const formData = new FormData();\r\n    formData.append(\"file\", file);\r\n\r\n    try {\r\n      const timeoutId = setTimeout(() => {\r\n        if (isAnalyzing) {\r\n          setAnalysisProgress(\"Analyse en cours ... Cette étape peut prendre quelques instants.\");\r\n        }\r\n      }, 3000);\r\n      \r\n      const response = await fetch(\"http://127.0.0.1:8000/detect/\", {\r\n        method: \"POST\",\r\n        body: formData,\r\n      });\r\n\r\n      clearTimeout(timeoutId);\r\n\r\n      if (!response.ok) {\r\n        try {\r\n          const errorData = await response.json();\r\n          throw new Error(`Erreur serveur: ${errorData.detail || response.status}`);\r\n        } catch (jsonError) {\r\n          throw new Error(`Erreur HTTP: ${response.status}`);\r\n        }\r\n      }\r\n\r\n      const textFileUrl = \"http://127.0.0.1:8000/resultats_classes.txt\";\r\n      setTextUrl(textFileUrl);\r\n      \r\n    } catch (error) {\r\n      console.error(\"Erreur de connexion ou analyse :\", error);\r\n      setAnalysisProgress(\"Erreur pendant l'analyse de l'image\");\r\n      alert(`Erreur pendant l'analyse de l'image: ${error instanceof Error ? error.message : String(error)}`);\r\n    } finally {\r\n      setIsAnalyzing(false);\r\n    }\r\n  };\r\n\r\n  const resetAnalysis = () => {\r\n    // Réinitialiser tous les états\r\n    setImageUrl(null);\r\n    setTextUrl(null);\r\n    setExtractedText(\"\");\r\n    setFileName(\"\");\r\n    setAnalysisProgress(\"En attente de l'image...\");\r\n    \r\n    // Réinitialiser l'état d'historique et les références\r\n    historyAddedRef.current = false;\r\n    currentAnalysisId.current = \"\";\r\n    currentImageBase64.current = null;\r\n    currentThumbnail.current = null;\r\n    \r\n    console.log(\"🔄 Analyse réinitialisée\");\r\n    \r\n    if (onAnalysisComplete) {\r\n      onAnalysisComplete(\"\", \"\", null);\r\n    }\r\n  };\r\n\r\n  const handleUpdateExtractedText = (newText: string) => {\r\n    console.log(\"Mise à jour du texte extrait dans ImageUploader.tsx\");\r\n    console.log(\"Nouveau texte:\", newText.substring(0, 100) + \"...\");\r\n    \r\n    // Mettre à jour l'état local\r\n    setExtractedText(newText);\r\n    \r\n    // Propager la mise à jour au parent\r\n    if (onUpdateExtractedText) {\r\n      onUpdateExtractedText(newText);\r\n    }\r\n    \r\n    // Forcer un re-rendu si nécessaire\r\n    setTimeout(() => {\r\n      console.log(\"Forcer un re-rendu après mise à jour dans ImageUploader\");\r\n      setExtractedText(prevText => {\r\n        if (prevText === newText) {\r\n          // Si le texte n'a pas changé, forcer un re-rendu en créant une nouvelle référence\r\n          return newText + \" \"; // Ajouter un espace pour forcer un changement\r\n        }\r\n        return newText;\r\n      });\r\n    }, 100);\r\n  };\r\n\r\n  const getStyles = () => ({\r\n    container: {\r\n      maxWidth: \"800px\",\r\n      margin: \"0 auto\",\r\n      padding: \"20px\",\r\n      color: darkMode ? \"#e0e0e0\" : \"#333\"\r\n    },\r\n    dropZone: {\r\n      border: `2px dashed ${darkMode ? '#4f46e5' : '#2563eb'}`,\r\n      borderRadius: '8px',\r\n      padding: '40px',\r\n      textAlign: 'center' as const,\r\n      backgroundColor: isDragging \r\n        ? (darkMode ? 'rgba(79, 70, 229, 0.2)' : 'rgba(37, 99, 235, 0.1)')\r\n        : (darkMode ? '#2d2d2d' : '#f5f5f5'),\r\n      margin: '20px 0',\r\n      cursor: 'pointer',\r\n      transition: 'all 0.3s ease'\r\n    },\r\n    fileInput: {\r\n      display: 'none'\r\n    },\r\n    selectButton: {\r\n      backgroundColor: darkMode ? '#4f46e5' : '#2563eb',\r\n      color: 'white',\r\n      padding: '10px 20px',\r\n      borderRadius: '5px',\r\n      border: 'none',\r\n      cursor: 'pointer',\r\n      marginTop: '10px',\r\n      fontSize: '16px'\r\n    },\r\n    resultsContainer: {\r\n      backgroundColor: darkMode ? '#333' : '#fff',\r\n      borderRadius: '8px',\r\n      padding: '20px',\r\n      marginTop: '20px',\r\n      boxShadow: darkMode ? '0 2px 10px rgba(0,0,0,0.5)' : '0 2px 10px rgba(0,0,0,0.1)'\r\n    }\r\n  });\r\n\r\n  const styles = getStyles();\r\n\r\n  return (\r\n    <div style={styles.container}>\r\n      {!imageUrl ? (\r\n        <div\r\n          onDragEnter={() => setIsDragging(true)}\r\n          onDragOver={(e) => e.preventDefault()}\r\n          onDragLeave={() => setIsDragging(false)}\r\n          onDrop={handleDrop}\r\n          onClick={() => document.getElementById('file-input')?.click()}\r\n          style={styles.dropZone}\r\n        >\r\n          <p>Glissez-déposez votre diagramme UML ou cliquez pour sélectionner</p>\r\n          <input\r\n            id=\"file-input\"\r\n            type=\"file\"\r\n            accept=\"image/*\"\r\n            onChange={handleFileChange}\r\n            style={styles.fileInput}\r\n          />\r\n          <button style={styles.selectButton}>Sélectionner un fichier</button>\r\n        </div>\r\n      ) : (\r\n        <div style={styles.resultsContainer}>\r\n          {(isAnalyzing || isConverting) ? (\r\n            <LoadingSpinner darkMode={darkMode} text={analysisProgress} />\r\n          ) : (\r\n            <AnalysisResults \r\n              darkMode={darkMode}\r\n              imageUrl={imageUrl}\r\n              extractedText={extractedText}\r\n              onViewAnnotatedImage={onViewAnnotatedImage}\r\n              onNavigateToUMLExtractor={onNavigateToUMLExtractor}\r\n              resetAnalysis={resetAnalysis}\r\n              onUpdateExtractedText={handleUpdateExtractedText}\r\n            />\r\n          )}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ImageUploader;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,SAASC,UAAU,QAAQ,8BAA8B;AACzD,SAASC,WAAW,QAAQ,+BAA+B;;AAE3D;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,YAAY,GAAIC,IAAU,IAAsB;EACpD,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtC,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;IAE/BD,MAAM,CAACE,MAAM,GAAG,MAAM;MACpB,IAAI,OAAOF,MAAM,CAACG,MAAM,KAAK,QAAQ,EAAE;QACrCL,OAAO,CAACE,MAAM,CAACG,MAAM,CAAC;MACxB,CAAC,MAAM;QACLJ,MAAM,CAAC,IAAIK,KAAK,CAAC,wCAAwC,CAAC,CAAC;MAC7D;IACF,CAAC;IAEDJ,MAAM,CAACK,OAAO,GAAG,MAAM;MACrBN,MAAM,CAAC,IAAIK,KAAK,CAAC,sCAAsC,CAAC,CAAC;IAC3D,CAAC;IAEDJ,MAAM,CAACM,aAAa,CAACV,IAAI,CAAC;EAC5B,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,MAAMW,eAAe,GAAGA,CAACC,SAAiB,EAAEC,QAAQ,GAAG,GAAG,EAAEC,SAAS,GAAG,GAAG,EAAEC,OAAO,GAAG,GAAG,KAAsB;EAC9G,OAAO,IAAId,OAAO,CAAEC,OAAO,IAAK;IAC9B,MAAMc,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;IACvBD,GAAG,CAACV,MAAM,GAAG,MAAM;MACjB,MAAMY,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;MAC/C,MAAMC,GAAG,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAE;;MAEpC;MACA,IAAI;QAAEC,KAAK;QAAEC;MAAO,CAAC,GAAGR,GAAG;MAC3B,IAAIO,KAAK,GAAGC,MAAM,EAAE;QAClB,IAAID,KAAK,GAAGV,QAAQ,EAAE;UACpBW,MAAM,GAAIA,MAAM,GAAGX,QAAQ,GAAIU,KAAK;UACpCA,KAAK,GAAGV,QAAQ;QAClB;MACF,CAAC,MAAM;QACL,IAAIW,MAAM,GAAGV,SAAS,EAAE;UACtBS,KAAK,GAAIA,KAAK,GAAGT,SAAS,GAAIU,MAAM;UACpCA,MAAM,GAAGV,SAAS;QACpB;MACF;MAEAI,MAAM,CAACK,KAAK,GAAGA,KAAK;MACpBL,MAAM,CAACM,MAAM,GAAGA,MAAM;;MAEtB;MACAH,GAAG,CAACI,SAAS,CAACT,GAAG,EAAE,CAAC,EAAE,CAAC,EAAEO,KAAK,EAAEC,MAAM,CAAC;;MAEvC;MACA,MAAME,YAAY,GAAGR,MAAM,CAACS,SAAS,CAAC,YAAY,EAAEZ,OAAO,CAAC;MAC5Db,OAAO,CAACwB,YAAY,CAAC;IACvB,CAAC;IACDV,GAAG,CAACY,GAAG,GAAGhB,SAAS;EACrB,CAAC,CAAC;AACJ,CAAC;AAED,MAAMiB,WAAW,GAAIC,GAAW,IAAc;EAC5C,OAAOA,GAAG,CAACC,UAAU,CAAC,OAAO,CAAC;AAChC,CAAC;AAcD,MAAMC,aAA2C,GAAGA,CAAC;EACnDC,QAAQ;EACRC,kBAAkB;EAClBC,aAAa;EACbC,kBAAkB;EAClBC,YAAY;EACZC,oBAAoB;EACpBC,wBAAwB;EACxBC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGrD,QAAQ,CAAgB6C,aAAa,IAAI,IAAI,CAAC;EAC9E,MAAM,CAACS,OAAO,EAAEC,UAAU,CAAC,GAAGvD,QAAQ,CAAgB+C,YAAY,IAAI,IAAI,CAAC;EAC3E,MAAM,CAACS,WAAW,EAAEC,cAAc,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC0D,UAAU,EAAEC,aAAa,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC4D,aAAa,EAAEC,gBAAgB,CAAC,GAAG7D,QAAQ,CAAS8C,kBAAkB,IAAI,EAAE,CAAC;EACpF,MAAM,CAACgB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/D,QAAQ,CAAS,EAAE,CAAC;EACpE,MAAM,CAACgE,QAAQ,EAAEC,WAAW,CAAC,GAAGjE,QAAQ,CAAS,EAAE,CAAC;EACpD,MAAM,CAACkE,YAAY,EAAEC,eAAe,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;EAEvD,MAAM;IAAEoE;EAAE,CAAC,GAAG9D,WAAW,CAAC,CAAC;EAE3B,MAAM;IAAE+D;EAAe,CAAC,GAAGhE,UAAU,CAAC,CAAC;;EAEvC;EACAJ,SAAS,CAAC,MAAM;IACd,IAAI,CAAC6D,gBAAgB,EAAE;MACrBC,mBAAmB,CAACK,CAAC,CAAC,gCAAgC,CAAC,CAAC;IAC1D;EACF,CAAC,EAAE,CAACA,CAAC,EAAEN,gBAAgB,CAAC,CAAC;;EAEzB;EACA,MAAMQ,eAAe,GAAGpE,MAAM,CAAU,KAAK,CAAC;EAC9C,MAAMqE,iBAAiB,GAAGrE,MAAM,CAAS,EAAE,CAAC;;EAE5C;EACA,MAAMsE,kBAAkB,GAAGtE,MAAM,CAAgB,IAAI,CAAC;EACtD,MAAMuE,gBAAgB,GAAGvE,MAAM,CAAgB,IAAI,CAAC;;EAEpD;EACA,MAAMwE,gBAAgB,GAAGA,CAAA,KAAM;IAC7BJ,eAAe,CAACK,OAAO,GAAG,KAAK;IAC/BJ,iBAAiB,CAACI,OAAO,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;IACjDC,OAAO,CAACC,GAAG,CAAC,iCAAiCT,iBAAiB,CAACI,OAAO,EAAE,CAAC;EAC3E,CAAC;;EAED;EACA1E,SAAS,CAAC,MAAM;IACd,IAAI4C,aAAa,KAAKoC,SAAS,EAAE;MAC/B5B,WAAW,CAACR,aAAa,CAAC;MAC1B,IAAIA,aAAa,IAAIN,WAAW,CAACM,aAAa,CAAC,EAAE;QAC/C2B,kBAAkB,CAACG,OAAO,GAAG9B,aAAa;MAC5C;IACF;IACA,IAAIC,kBAAkB,KAAKmC,SAAS,EAAE;MACpCpB,gBAAgB,CAACf,kBAAkB,CAAC;IACtC;IACA,IAAIC,YAAY,KAAKkC,SAAS,EAAE;MAC9B1B,UAAU,CAACR,YAAY,CAAC;IAC1B;EACF,CAAC,EAAE,CAACF,aAAa,EAAEC,kBAAkB,EAAEC,YAAY,CAAC,CAAC;;EAErD;EACA,MAAMmC,mBAAmB,GAAG,MAAOxE,IAAU,IAAwD;IACnGyD,eAAe,CAAC,IAAI,CAAC;IACrBJ,mBAAmB,CAACK,CAAC,CAAC,mCAAmC,CAAC,CAAC;IAE3D,IAAI;MACF,MAAM9C,SAAS,GAAG,MAAMb,YAAY,CAACC,IAAI,CAAC;MAC1C,MAAMyE,SAAS,GAAG,MAAM9D,eAAe,CAACC,SAAS,CAAC;MAElDkD,kBAAkB,CAACG,OAAO,GAAGrD,SAAS;MACtCmD,gBAAgB,CAACE,OAAO,GAAGQ,SAAS;MAEpCJ,OAAO,CAACC,GAAG,CAAC,8BAA8BI,IAAI,CAACC,KAAK,CAAC/D,SAAS,CAACgE,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC;MACnFP,OAAO,CAACC,GAAG,CAAC,yBAAyBI,IAAI,CAACC,KAAK,CAACF,SAAS,CAACG,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC;MAE9E,OAAO;QAAEC,SAAS,EAAEjE,SAAS;QAAE6D;MAAU,CAAC;IAC5C,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdT,OAAO,CAACS,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAC/D,MAAMA,KAAK;IACb,CAAC,SAAS;MACRrB,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAMsB,gBAAgB,GAAG,MAAOC,CAAsC,IAAK;IAAA,IAAAC,eAAA;IACzE,MAAMjF,IAAI,IAAAiF,eAAA,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,cAAAF,eAAA,uBAAdA,eAAA,CAAiB,CAAC,CAAC;IAChC,IAAIjF,IAAI,EAAE;MACR,MAAMoF,YAAY,CAACpF,IAAI,CAAC;IAC1B;EACF,CAAC;EAED,MAAMqF,UAAU,GAAIL,CAAkC,IAAK;IACzDA,CAAC,CAACM,cAAc,CAAC,CAAC;IAClBrC,aAAa,CAAC,KAAK,CAAC;IACpB,IAAI+B,CAAC,CAACO,YAAY,CAACJ,KAAK,IAAIH,CAAC,CAACO,YAAY,CAACJ,KAAK,CAAC,CAAC,CAAC,EAAE;MACnDC,YAAY,CAACJ,CAAC,CAACO,YAAY,CAACJ,KAAK,CAAC,CAAC,CAAC,CAAC;IACvC;EACF,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOpF,IAAU,IAAK;IACzC;IACAgE,gBAAgB,CAAC,CAAC;IAElBT,WAAW,CAACvD,IAAI,CAACwF,IAAI,CAAC;IACtBrC,gBAAgB,CAAC,EAAE,CAAC;IACpBN,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF;MACA,MAAM;QAAEgC;MAAU,CAAC,GAAG,MAAML,mBAAmB,CAACxE,IAAI,CAAC;MACrD2C,WAAW,CAACkC,SAAS,CAAC;;MAEtB;MACAY,YAAY,CAACzF,IAAI,CAAC;IACpB,CAAC,CAAC,OAAO8E,KAAK,EAAE;MACdT,OAAO,CAACS,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7DY,KAAK,CAAChC,CAAC,CAAC,gCAAgC,CAAC,CAAC;IAC5C;EACF,CAAC;;EAED;EACA,MAAMiC,gBAAgB,GAAG,MAAAA,CAAOjD,QAAgB,EAAEkD,IAAY,EAAEhD,OAAe,KAAK;IAClF,IAAIgB,eAAe,CAACK,OAAO,EAAE;MAC3BI,OAAO,CAACC,GAAG,CAAC,wDAAwDT,iBAAiB,CAACI,OAAO,EAAE,CAAC;MAChG;IACF;IAEA,IAAI;MACF;MACAL,eAAe,CAACK,OAAO,GAAG,IAAI;;MAE9B;MACA,MAAMQ,SAAS,GAAGV,gBAAgB,CAACE,OAAO;MAC1C,IAAI,CAACQ,SAAS,EAAE;QACd,MAAM,IAAIjE,KAAK,CAACkD,CAAC,CAAC,+BAA+B,CAAC,CAAC;MACrD;MAEA,MAAMmC,KAAK,GAAGvC,QAAQ,GAClB,GAAGA,QAAQ,MAAM,IAAIY,IAAI,CAAC,CAAC,CAAC4B,kBAAkB,CAAC,OAAO,CAAC,EAAE,GACzD,mBAAmB,IAAI5B,IAAI,CAAC,CAAC,CAAC4B,kBAAkB,CAAC,OAAO,CAAC,EAAE;MAE/D,MAAMC,iBAAiB,GAAGzC,QAAQ,GAC9B,yCAAyC0C,kBAAkB,CAAC1C,QAAQ,CAAC,EAAE,GACvE,2CAA2C;MAE/Ce,OAAO,CAACC,GAAG,CAAC,2CAA2CT,iBAAiB,CAACI,OAAO,EAAE,CAAC;MAEnF,MAAMN,cAAc,CAAC;QACnBkC,KAAK,EAAEA,KAAK;QACZI,gBAAgB,EAAEF,iBAAiB;QAAE;QACrCA,iBAAiB,EAAEA,iBAAiB;QACpC7C,aAAa,EAAE0C,IAAI;QACnBnB,SAAS,EAAEA,SAAS;QAAE;QACtByB,MAAM,EAAE;MACV,CAAC,CAAC;MAEF7B,OAAO,CAACC,GAAG,CAAC,wCAAwCT,iBAAiB,CAACI,OAAO,EAAE,CAAC;IAClF,CAAC,CAAC,OAAOa,KAAK,EAAE;MACdT,OAAO,CAACS,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAC9D;MACAlB,eAAe,CAACK,OAAO,GAAG,KAAK;IACjC;EACF,CAAC;;EAED;EACA1E,SAAS,CAAC,MAAM;IACd,IAAI,CAACqD,OAAO,IAAI,CAACF,QAAQ,IAAIkB,eAAe,CAACK,OAAO,EAAE;IAEtD,IAAIkC,SAAS,GAAG,IAAI;IAEpB,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B/C,mBAAmB,CAACK,CAAC,CAAC,iCAAiC,CAAC,CAAC;MAEzD,IAAI;QACF,MAAM2C,QAAQ,GAAG,MAAMC,KAAK,CAAC1D,OAAO,CAAC;QACrC,IAAI,CAACyD,QAAQ,CAACE,EAAE,EAAE;UAChB,MAAM,IAAI/F,KAAK,CAACkD,CAAC,CAAC,+BAA+B,CAAC,CAAC;QACrD;QAEA,MAAMkC,IAAI,GAAG,MAAMS,QAAQ,CAACT,IAAI,CAAC,CAAC;QAElC,IAAI,CAACO,SAAS,EAAE;QAEhBhD,gBAAgB,CAACyC,IAAI,CAAC;QACtBvC,mBAAmB,CAAC,kBAAkB,CAAC;QAEvC,IAAInB,kBAAkB,EAAE;UACtBA,kBAAkB,CAACQ,QAAQ,EAAEkD,IAAI,EAAEhD,OAAO,CAAC;QAC7C;;QAEA;QACA,MAAM+C,gBAAgB,CAACjD,QAAQ,EAAEkD,IAAI,EAAEhD,OAAO,CAAC;MACjD,CAAC,CAAC,OAAOkC,KAAK,EAAE;QACd,IAAIqB,SAAS,EAAE;UACb9B,OAAO,CAACS,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;UACjEzB,mBAAmB,CAAC,8CAA8C,CAAC;QACrE;MACF;IACF,CAAC;IAED+C,YAAY,CAAC,CAAC;IAEd,OAAO,MAAM;MACXD,SAAS,GAAG,KAAK;IACnB,CAAC;EACH,CAAC,EAAE,CAACvD,OAAO,EAAEF,QAAQ,EAAER,kBAAkB,CAAC,CAAC;EAE3C,MAAMuD,YAAY,GAAG,MAAOzF,IAAU,IAAK;IACzC+C,cAAc,CAAC,IAAI,CAAC;IACpBM,mBAAmB,CAAC,4DAA4D,CAAC;IAEjF,MAAMmD,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAE1G,IAAI,CAAC;IAE7B,IAAI;MACF,MAAM2G,SAAS,GAAGC,UAAU,CAAC,MAAM;QACjC,IAAI9D,WAAW,EAAE;UACfO,mBAAmB,CAAC,kEAAkE,CAAC;QACzF;MACF,CAAC,EAAE,IAAI,CAAC;MAER,MAAMgD,QAAQ,GAAG,MAAMC,KAAK,CAAC,+BAA+B,EAAE;QAC5DO,MAAM,EAAE,MAAM;QACdC,IAAI,EAAEN;MACR,CAAC,CAAC;MAEFO,YAAY,CAACJ,SAAS,CAAC;MAEvB,IAAI,CAACN,QAAQ,CAACE,EAAE,EAAE;QAChB,IAAI;UACF,MAAMS,SAAS,GAAG,MAAMX,QAAQ,CAACY,IAAI,CAAC,CAAC;UACvC,MAAM,IAAIzG,KAAK,CAAC,mBAAmBwG,SAAS,CAACE,MAAM,IAAIb,QAAQ,CAACc,MAAM,EAAE,CAAC;QAC3E,CAAC,CAAC,OAAOC,SAAS,EAAE;UAClB,MAAM,IAAI5G,KAAK,CAAC,gBAAgB6F,QAAQ,CAACc,MAAM,EAAE,CAAC;QACpD;MACF;MAEA,MAAME,WAAW,GAAG,6CAA6C;MACjExE,UAAU,CAACwE,WAAW,CAAC;IAEzB,CAAC,CAAC,OAAOvC,KAAK,EAAE;MACdT,OAAO,CAACS,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxDzB,mBAAmB,CAAC,qCAAqC,CAAC;MAC1DqC,KAAK,CAAC,wCAAwCZ,KAAK,YAAYtE,KAAK,GAAGsE,KAAK,CAACwC,OAAO,GAAGC,MAAM,CAACzC,KAAK,CAAC,EAAE,CAAC;IACzG,CAAC,SAAS;MACR/B,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAMyE,aAAa,GAAGA,CAAA,KAAM;IAC1B;IACA7E,WAAW,CAAC,IAAI,CAAC;IACjBE,UAAU,CAAC,IAAI,CAAC;IAChBM,gBAAgB,CAAC,EAAE,CAAC;IACpBI,WAAW,CAAC,EAAE,CAAC;IACfF,mBAAmB,CAAC,0BAA0B,CAAC;;IAE/C;IACAO,eAAe,CAACK,OAAO,GAAG,KAAK;IAC/BJ,iBAAiB,CAACI,OAAO,GAAG,EAAE;IAC9BH,kBAAkB,CAACG,OAAO,GAAG,IAAI;IACjCF,gBAAgB,CAACE,OAAO,GAAG,IAAI;IAE/BI,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;IAEvC,IAAIpC,kBAAkB,EAAE;MACtBA,kBAAkB,CAAC,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC;IAClC;EACF,CAAC;EAED,MAAMuF,yBAAyB,GAAIC,OAAe,IAAK;IACrDrD,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;IAClED,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEoD,OAAO,CAACC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC;;IAEhE;IACAxE,gBAAgB,CAACuE,OAAO,CAAC;;IAEzB;IACA,IAAIlF,qBAAqB,EAAE;MACzBA,qBAAqB,CAACkF,OAAO,CAAC;IAChC;;IAEA;IACAd,UAAU,CAAC,MAAM;MACfvC,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;MACtEnB,gBAAgB,CAACyE,QAAQ,IAAI;QAC3B,IAAIA,QAAQ,KAAKF,OAAO,EAAE;UACxB;UACA,OAAOA,OAAO,GAAG,GAAG,CAAC,CAAC;QACxB;QACA,OAAOA,OAAO;MAChB,CAAC,CAAC;IACJ,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;EAED,MAAMG,SAAS,GAAGA,CAAA,MAAO;IACvBC,SAAS,EAAE;MACTjH,QAAQ,EAAE,OAAO;MACjBkH,MAAM,EAAE,QAAQ;MAChBC,OAAO,EAAE,MAAM;MACfC,KAAK,EAAEhG,QAAQ,GAAG,SAAS,GAAG;IAChC,CAAC;IACDiG,QAAQ,EAAE;MACRC,MAAM,EAAE,cAAclG,QAAQ,GAAG,SAAS,GAAG,SAAS,EAAE;MACxDmG,YAAY,EAAE,KAAK;MACnBJ,OAAO,EAAE,MAAM;MACfK,SAAS,EAAE,QAAiB;MAC5BC,eAAe,EAAEtF,UAAU,GACtBf,QAAQ,GAAG,wBAAwB,GAAG,wBAAwB,GAC9DA,QAAQ,GAAG,SAAS,GAAG,SAAU;MACtC8F,MAAM,EAAE,QAAQ;MAChBQ,MAAM,EAAE,SAAS;MACjBC,UAAU,EAAE;IACd,CAAC;IACDC,SAAS,EAAE;MACTC,OAAO,EAAE;IACX,CAAC;IACDC,YAAY,EAAE;MACZL,eAAe,EAAErG,QAAQ,GAAG,SAAS,GAAG,SAAS;MACjDgG,KAAK,EAAE,OAAO;MACdD,OAAO,EAAE,WAAW;MACpBI,YAAY,EAAE,KAAK;MACnBD,MAAM,EAAE,MAAM;MACdI,MAAM,EAAE,SAAS;MACjBK,SAAS,EAAE,MAAM;MACjBC,QAAQ,EAAE;IACZ,CAAC;IACDC,gBAAgB,EAAE;MAChBR,eAAe,EAAErG,QAAQ,GAAG,MAAM,GAAG,MAAM;MAC3CmG,YAAY,EAAE,KAAK;MACnBJ,OAAO,EAAE,MAAM;MACfY,SAAS,EAAE,MAAM;MACjBG,SAAS,EAAE9G,QAAQ,GAAG,4BAA4B,GAAG;IACvD;EACF,CAAC,CAAC;EAEF,MAAM+G,MAAM,GAAGnB,SAAS,CAAC,CAAC;EAE1B,oBACE/H,OAAA;IAAKmJ,KAAK,EAAED,MAAM,CAAClB,SAAU;IAAAoB,QAAA,EAC1B,CAACxG,QAAQ,gBACR5C,OAAA;MACEqJ,WAAW,EAAEA,CAAA,KAAMlG,aAAa,CAAC,IAAI,CAAE;MACvCmG,UAAU,EAAGpE,CAAC,IAAKA,CAAC,CAACM,cAAc,CAAC,CAAE;MACtC+D,WAAW,EAAEA,CAAA,KAAMpG,aAAa,CAAC,KAAK,CAAE;MACxCqG,MAAM,EAAEjE,UAAW;MACnBkE,OAAO,EAAEA,CAAA;QAAA,IAAAC,qBAAA;QAAA,QAAAA,qBAAA,GAAMrI,QAAQ,CAACsI,cAAc,CAAC,YAAY,CAAC,cAAAD,qBAAA,uBAArCA,qBAAA,CAAuCE,KAAK,CAAC,CAAC;MAAA,CAAC;MAC9DT,KAAK,EAAED,MAAM,CAACd,QAAS;MAAAgB,QAAA,gBAEvBpJ,OAAA;QAAAoJ,QAAA,EAAG;MAAgE;QAAA5F,QAAA,EAAAqG,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACvE/J,OAAA;QACEgK,EAAE,EAAC,YAAY;QACfC,IAAI,EAAC,MAAM;QACXC,MAAM,EAAC,SAAS;QAChBC,QAAQ,EAAElF,gBAAiB;QAC3BkE,KAAK,EAAED,MAAM,CAACP;MAAU;QAAAnF,QAAA,EAAAqG,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,eACF/J,OAAA;QAAQmJ,KAAK,EAAED,MAAM,CAACL,YAAa;QAAAO,QAAA,EAAC;MAAuB;QAAA5F,QAAA,EAAAqG,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAvG,QAAA,EAAAqG,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjE,CAAC,gBAEN/J,OAAA;MAAKmJ,KAAK,EAAED,MAAM,CAACF,gBAAiB;MAAAI,QAAA,EAChCpG,WAAW,IAAIU,YAAY,gBAC3B1D,OAAA,CAACL,cAAc;QAACwC,QAAQ,EAAEA,QAAS;QAAC2D,IAAI,EAAExC;MAAiB;QAAAE,QAAA,EAAAqG,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAE9D/J,OAAA,CAACJ,eAAe;QACduC,QAAQ,EAAEA,QAAS;QACnBS,QAAQ,EAAEA,QAAS;QACnBQ,aAAa,EAAEA,aAAc;QAC7BZ,oBAAoB,EAAEA,oBAAqB;QAC3CC,wBAAwB,EAAEA,wBAAyB;QACnDiF,aAAa,EAAEA,aAAc;QAC7BhF,qBAAqB,EAAEiF;MAA0B;QAAAnE,QAAA,EAAAqG,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD;IACF;MAAAvG,QAAA,EAAAqG,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EACN;IAAAvG,QAAA,EAAAqG,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACpH,EAAA,CA3XIT,aAA2C;EAAA,QAmBjCpC,WAAW,EAEED,UAAU;AAAA;AAAAuK,EAAA,GArBjClI,aAA2C;AA6XjD,eAAeA,aAAa;AAAC,IAAAkI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}