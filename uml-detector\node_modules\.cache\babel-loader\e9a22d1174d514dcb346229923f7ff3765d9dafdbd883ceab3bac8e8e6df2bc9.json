{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\FixTorchUMLDGM\\\\uml-detector\\\\src\\\\components\\\\imageUp\\\\EntityPopup.tsx\",\n  _s = $RefreshSig$();\n// EntityPopup.tsx\nimport React from 'react';\nimport { getEntityPopupStyles } from './EntityPopupStyles';\nimport HistoryAnalysisSection from './HistoryAnalysisSection';\nimport { useLanguage } from '../../context/LanguageContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EntityPopup = ({\n  darkMode,\n  entityName,\n  position,\n  onClose,\n  onModify,\n  memoryAttributes = [],\n  memoryMethods = [],\n  extractedAttributes = [],\n  extractedMethods = [],\n  currentDiagramText\n}) => {\n  _s();\n  const {\n    t\n  } = useLanguage();\n  // État pour les checkboxes\n  const [selectedAttributes, setSelectedAttributes] = React.useState([]);\n  const [selectedMethods, setSelectedMethods] = React.useState([]);\n\n  // États pour l'analyse historique - Résolution de conflits supprimée\n\n  // Données de classe actuelle pour l'analyse historique\n  const currentClassData = React.useMemo(() => ({\n    name: entityName,\n    attributes: [...memoryAttributes, ...extractedAttributes],\n    methods: [...memoryMethods, ...extractedMethods]\n  }), [entityName, memoryAttributes, extractedAttributes, memoryMethods, extractedMethods]);\n\n  // Filtrer pour n'afficher que les attributs et méthodes qui ne sont pas dans le texte extrait\n  // Modifier la logique de filtrage pour être insensible à la casse\n  const uniqueAttributes = memoryAttributes.filter(attr => {\n    // Extraire le nom de l'attribut sans le type ou la visibilité\n    const attrName = attr.replace(/^[+-]?\\s*/, '').split(':')[0].trim().toLowerCase();\n    return !extractedAttributes.some(extractedAttr => {\n      // Extraire le nom de l'attribut extrait\n      const extractedAttrName = extractedAttr.replace(/^[+-]?\\s*/, '').split(':')[0].trim().toLowerCase();\n      return extractedAttrName === attrName;\n    });\n  });\n  const uniqueMethods = memoryMethods.filter(method => {\n    // Extraire le nom de la méthode sans les paramètres\n    const methodName = method.replace(/^[+-]?\\s*/, '').split('(')[0].trim().toLowerCase();\n    return !extractedMethods.some(extractedMethod => {\n      // Extraire le nom de la méthode extraite\n      const extractedMethodName = extractedMethod.replace(/^[+-]?\\s*/, '').split('(')[0].trim().toLowerCase();\n      return extractedMethodName === methodName;\n    });\n  });\n\n  // DEBUG: Afficher toutes les données reçues par EntityPopup\n  console.log(\"🎯 DEBUG EntityPopup - Données reçues pour\", entityName);\n  console.log(\"📥 memoryAttributes reçus:\", memoryAttributes);\n  console.log(\"📥 memoryMethods reçus:\", memoryMethods);\n  console.log(\"📥 extractedAttributes reçus:\", extractedAttributes);\n  console.log(\"📥 extractedMethods reçus:\", extractedMethods);\n  console.log(\"📊 uniqueAttributes calculés:\", uniqueAttributes);\n  console.log(\"📊 uniqueMethods calculés:\", uniqueMethods);\n\n  // Gestionnaires pour les checkboxes\n  const handleAttributeToggle = attribute => {\n    setSelectedAttributes(prev => prev.includes(attribute) ? prev.filter(attr => attr !== attribute) : [...prev, attribute]);\n  };\n  const handleMethodToggle = method => {\n    setSelectedMethods(prev => prev.includes(method) ? prev.filter(m => m !== method) : [...prev, method]);\n  };\n\n  // Fonction pour gérer l'import depuis l'historique\n  const handleHistoryImport = importedData => {\n    // Import direct sans résolution de conflits\n    // Ajouter les éléments importés aux sélections actuelles\n    const newSelectedAttributes = [...selectedAttributes];\n    const newSelectedMethods = [...selectedMethods];\n\n    // Ajouter les attributs importés (éviter les doublons)\n    importedData.attributes.forEach(attr => {\n      if (!newSelectedAttributes.includes(attr)) {\n        newSelectedAttributes.push(attr);\n      }\n    });\n\n    // Ajouter les méthodes importées (éviter les doublons)\n    importedData.methods.forEach(method => {\n      if (!newSelectedMethods.includes(method)) {\n        newSelectedMethods.push(method);\n      }\n    });\n\n    // Mettre à jour les sélections\n    setSelectedAttributes(newSelectedAttributes);\n    setSelectedMethods(newSelectedMethods);\n  };\n\n  // Fonction pour gérer le clic sur le bouton Modifier\n  const handleModifyClick = () => {\n    onModify(selectedAttributes, selectedMethods);\n  };\n\n  // Gestionnaires d'événements pour les effets hover\n  const handleMouseEnter = e => {\n    if (e.target === e.currentTarget) {\n      e.target.style.backgroundColor = darkMode ? '#475569' : '#f3f4f6';\n    }\n  };\n  const handleMouseLeave = e => {\n    if (e.target === e.currentTarget) {\n      e.target.style.backgroundColor = darkMode ? '#334155' : '#f8fafc';\n    }\n  };\n  const handleModifyHover = (e, isEnter) => {\n    e.target.style.backgroundColor = isEnter ? '#2563eb' : '#3b82f6';\n    e.target.style.transform = isEnter ? 'translateY(-1px)' : 'translateY(0)';\n  };\n  const handleCancelHover = (e, isEnter) => {\n    e.target.style.backgroundColor = isEnter ? darkMode ? '#374151' : '#f9fafb' : 'transparent';\n  };\n\n  // Gérer le clic sur l'overlay pour fermer le popup\n  const handleOverlayClick = e => {\n    // Fermer seulement si on clique sur l'overlay, pas sur le popup\n    if (e.target === e.currentTarget) {\n      onClose();\n    }\n  };\n\n  // Empêcher la propagation du clic depuis le popup vers l'overlay\n  const handlePopupClick = e => {\n    e.stopPropagation();\n  };\n\n  // Obtenir les styles\n  const styles = getEntityPopupStyles(darkMode);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: styles.overlay,\n    onClick: handleOverlayClick,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.popup,\n      onClick: handlePopupClick,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.header,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: styles.title,\n            children: entityName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.subtitle,\n            children: t('entity.subtitle')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          style: styles.closeButton,\n          onClick: onClose,\n          onMouseEnter: handleMouseEnter,\n          onMouseLeave: handleMouseLeave,\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.content,\n        children: [/*#__PURE__*/_jsxDEV(HistoryAnalysisSection, {\n          darkMode: darkMode,\n          targetClassName: entityName,\n          currentClassData: currentClassData,\n          onImport: handleHistoryImport,\n          currentDiagramText: currentDiagramText\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.tableContainer,\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            style: styles.table,\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              style: styles.tableHeader,\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  style: styles.tableHeaderCell,\n                  children: t('entity.attributes').toUpperCase()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  style: styles.tableHeaderCell,\n                  children: t('entity.methods').toUpperCase()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 203,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              style: styles.tableBody,\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                style: styles.tableRow,\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  style: styles.tableCell,\n                  children: uniqueAttributes.length > 0 ? uniqueAttributes.map((attr, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: styles.checkboxContainer,\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"checkbox\",\n                      id: `attr-${index}`,\n                      checked: selectedAttributes.includes(attr),\n                      onChange: () => handleAttributeToggle(attr),\n                      style: styles.checkbox\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 212,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                      htmlFor: `attr-${index}`,\n                      style: styles.checkboxLabel,\n                      children: attr\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 219,\n                      columnNumber: 27\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 211,\n                    columnNumber: 25\n                  }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: styles.emptyState,\n                    children: t('entity.noAdditionalAttributes')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 228,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 208,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  style: styles.tableCell,\n                  children: uniqueMethods.length > 0 ? uniqueMethods.map((method, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: styles.checkboxContainer,\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"checkbox\",\n                      id: `method-${index}`,\n                      checked: selectedMethods.includes(method),\n                      onChange: () => handleMethodToggle(method),\n                      style: styles.checkbox\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 237,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                      htmlFor: `method-${index}`,\n                      style: styles.checkboxLabel,\n                      children: method\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 244,\n                      columnNumber: 27\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 236,\n                    columnNumber: 25\n                  }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: styles.emptyState,\n                    children: t('entity.noAdditionalMethods')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 253,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.buttonContainer,\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            style: styles.modifyButton,\n            onClick: handleModifyClick,\n            onMouseEnter: e => handleModifyHover(e, true),\n            onMouseLeave: e => handleModifyHover(e, false),\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u270F\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 15\n            }, this), t('entity.modifyEntity')]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            style: styles.cancelButton,\n            onClick: onClose,\n            onMouseEnter: e => handleCancelHover(e, true),\n            onMouseLeave: e => handleCancelHover(e, false),\n            children: t('entity.cancel')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 173,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 172,\n    columnNumber: 5\n  }, this);\n};\n_s(EntityPopup, \"xWsEdQ8d7ptX3dPNuzw2inLsIZM=\", false, function () {\n  return [useLanguage];\n});\n_c = EntityPopup;\nexport default EntityPopup;\nvar _c;\n$RefreshReg$(_c, \"EntityPopup\");", "map": {"version": 3, "names": ["React", "getEntityPopupStyles", "HistoryAnalysisSection", "useLanguage", "jsxDEV", "_jsxDEV", "EntityPopup", "darkMode", "entityName", "position", "onClose", "onModify", "memoryAttributes", "memoryMethods", "extractedAttributes", "extractedMethods", "currentDiagramText", "_s", "t", "selectedAttributes", "setSelectedAttributes", "useState", "selectedMethods", "setSelectedMethods", "currentClassData", "useMemo", "name", "attributes", "methods", "uniqueAttributes", "filter", "attr", "attrName", "replace", "split", "trim", "toLowerCase", "some", "extractedAttr", "extractedAttrName", "uniqueMethods", "method", "methodName", "extractedMethod", "extractedMethodName", "console", "log", "handleAttributeToggle", "attribute", "prev", "includes", "handleMethodToggle", "m", "handleHistoryImport", "importedData", "newSelectedAttributes", "newSelectedMethods", "for<PERSON>ach", "push", "handleModifyClick", "handleMouseEnter", "e", "target", "currentTarget", "style", "backgroundColor", "handleMouseLeave", "handleModifyHover", "isEnter", "transform", "handleCancelHover", "handleOverlayClick", "handlePopupClick", "stopPropagation", "styles", "overlay", "onClick", "children", "popup", "header", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "subtitle", "closeButton", "onMouseEnter", "onMouseLeave", "content", "targetClassName", "onImport", "tableContainer", "table", "tableHeader", "tableHeaderCell", "toUpperCase", "tableBody", "tableRow", "tableCell", "length", "map", "index", "checkboxContainer", "type", "id", "checked", "onChange", "checkbox", "htmlFor", "checkboxLabel", "emptyState", "buttonContainer", "modifyButton", "cancelButton", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/FixTorchUMLDGM/uml-detector/src/components/imageUp/EntityPopup.tsx"], "sourcesContent": ["// EntityPopup.tsx\r\nimport React from 'react';\r\nimport { getEntityPopupStyles } from './EntityPopupStyles';\r\nimport HistoryAnalysisSection from './HistoryAnalysisSection';\r\n\r\nimport { ClassData } from '../../services/HistoryAnalysisService';\r\nimport { useLanguage } from '../../context/LanguageContext';\r\n\r\ninterface EntityPopupProps {\r\n  darkMode: boolean;\r\n  entityName: string;\r\n  position: { x: number; y: number };\r\n  onClose: () => void;\r\n  onModify: (selectedAttributes: string[], selectedMethods: string[]) => void;\r\n  memoryAttributes: string[]; // Attributs de la classe depuis memoire.txt\r\n  memoryMethods: string[];    // Méthodes de la classe depuis memoire.txt\r\n  extractedAttributes: string[]; // Attributs extraits du texte actuel\r\n  extractedMethods: string[];    // Méthodes extraites du texte actuel\r\n  currentDiagramText?: string; // Texte du diagramme actuel pour exclure de l'analyse historique\r\n}\r\n\r\nconst EntityPopup: React.FC<EntityPopupProps> = ({\r\n  darkMode,\r\n  entityName,\r\n  position,\r\n  onClose,\r\n  onModify,\r\n  memoryAttributes = [],\r\n  memoryMethods = [],\r\n  extractedAttributes = [],\r\n  extractedMethods = [],\r\n  currentDiagramText\r\n}) => {\r\n  const { t } = useLanguage();\r\n  // État pour les checkboxes\r\n  const [selectedAttributes, setSelectedAttributes] = React.useState<string[]>([]);\r\n  const [selectedMethods, setSelectedMethods] = React.useState<string[]>([]);\r\n\r\n  // États pour l'analyse historique - Résolution de conflits supprimée\r\n\r\n  // Données de classe actuelle pour l'analyse historique\r\n  const currentClassData: ClassData = React.useMemo(() => ({\r\n    name: entityName,\r\n    attributes: [...memoryAttributes, ...extractedAttributes],\r\n    methods: [...memoryMethods, ...extractedMethods]\r\n  }), [entityName, memoryAttributes, extractedAttributes, memoryMethods, extractedMethods]);\r\n\r\n  // Filtrer pour n'afficher que les attributs et méthodes qui ne sont pas dans le texte extrait\r\n  // Modifier la logique de filtrage pour être insensible à la casse\r\n  const uniqueAttributes = memoryAttributes.filter(attr => {\r\n    // Extraire le nom de l'attribut sans le type ou la visibilité\r\n    const attrName = attr.replace(/^[+-]?\\s*/, '').split(':')[0].trim().toLowerCase();\r\n    \r\n    return !extractedAttributes.some(extractedAttr => {\r\n      // Extraire le nom de l'attribut extrait\r\n      const extractedAttrName = extractedAttr.replace(/^[+-]?\\s*/, '').split(':')[0].trim().toLowerCase();\r\n      return extractedAttrName === attrName;\r\n    });\r\n  });\r\n\r\n  const uniqueMethods = memoryMethods.filter(method => {\r\n    // Extraire le nom de la méthode sans les paramètres\r\n    const methodName = method.replace(/^[+-]?\\s*/, '').split('(')[0].trim().toLowerCase();\r\n\r\n    return !extractedMethods.some(extractedMethod => {\r\n      // Extraire le nom de la méthode extraite\r\n      const extractedMethodName = extractedMethod.replace(/^[+-]?\\s*/, '').split('(')[0].trim().toLowerCase();\r\n      return extractedMethodName === methodName;\r\n    });\r\n  });\r\n\r\n  // DEBUG: Afficher toutes les données reçues par EntityPopup\r\n  console.log(\"🎯 DEBUG EntityPopup - Données reçues pour\", entityName);\r\n  console.log(\"📥 memoryAttributes reçus:\", memoryAttributes);\r\n  console.log(\"📥 memoryMethods reçus:\", memoryMethods);\r\n  console.log(\"📥 extractedAttributes reçus:\", extractedAttributes);\r\n  console.log(\"📥 extractedMethods reçus:\", extractedMethods);\r\n  console.log(\"📊 uniqueAttributes calculés:\", uniqueAttributes);\r\n  console.log(\"📊 uniqueMethods calculés:\", uniqueMethods);\r\n\r\n  // Gestionnaires pour les checkboxes\r\n  const handleAttributeToggle = (attribute: string) => {\r\n    setSelectedAttributes(prev => \r\n      prev.includes(attribute) \r\n        ? prev.filter(attr => attr !== attribute) \r\n        : [...prev, attribute]\r\n    );\r\n  };\r\n\r\n  const handleMethodToggle = (method: string) => {\r\n    setSelectedMethods(prev =>\r\n      prev.includes(method)\r\n        ? prev.filter(m => m !== method)\r\n        : [...prev, method]\r\n    );\r\n  };\r\n\r\n  // Fonction pour gérer l'import depuis l'historique\r\n  const handleHistoryImport = (importedData: ClassData) => {\r\n    // Import direct sans résolution de conflits\r\n    // Ajouter les éléments importés aux sélections actuelles\r\n    const newSelectedAttributes = [...selectedAttributes];\r\n    const newSelectedMethods = [...selectedMethods];\r\n\r\n    // Ajouter les attributs importés (éviter les doublons)\r\n    importedData.attributes.forEach(attr => {\r\n      if (!newSelectedAttributes.includes(attr)) {\r\n        newSelectedAttributes.push(attr);\r\n      }\r\n    });\r\n\r\n    // Ajouter les méthodes importées (éviter les doublons)\r\n    importedData.methods.forEach(method => {\r\n      if (!newSelectedMethods.includes(method)) {\r\n        newSelectedMethods.push(method);\r\n      }\r\n    });\r\n\r\n    // Mettre à jour les sélections\r\n    setSelectedAttributes(newSelectedAttributes);\r\n    setSelectedMethods(newSelectedMethods);\r\n  };\r\n\r\n\r\n  \r\n  // Fonction pour gérer le clic sur le bouton Modifier\r\n  const handleModifyClick = () => {\r\n    onModify(selectedAttributes, selectedMethods);\r\n  };\r\n\r\n  // Gestionnaires d'événements pour les effets hover\r\n  const handleMouseEnter = (e: React.MouseEvent) => {\r\n    if (e.target === e.currentTarget) {\r\n      (e.target as HTMLButtonElement).style.backgroundColor = darkMode ? '#475569' : '#f3f4f6';\r\n    }\r\n  };\r\n\r\n  const handleMouseLeave = (e: React.MouseEvent) => {\r\n    if (e.target === e.currentTarget) {\r\n      (e.target as HTMLButtonElement).style.backgroundColor = darkMode ? '#334155' : '#f8fafc';\r\n    }\r\n  };\r\n\r\n  const handleModifyHover = (e: React.MouseEvent, isEnter: boolean) => {\r\n    (e.target as HTMLButtonElement).style.backgroundColor = isEnter ? '#2563eb' : '#3b82f6';\r\n    (e.target as HTMLButtonElement).style.transform = isEnter ? 'translateY(-1px)' : 'translateY(0)';\r\n  };\r\n\r\n  const handleCancelHover = (e: React.MouseEvent, isEnter: boolean) => {\r\n    (e.target as HTMLButtonElement).style.backgroundColor = isEnter \r\n      ? (darkMode ? '#374151' : '#f9fafb') \r\n      : 'transparent';\r\n  };\r\n\r\n  // Gérer le clic sur l'overlay pour fermer le popup\r\n  const handleOverlayClick = (e: React.MouseEvent) => {\r\n    // Fermer seulement si on clique sur l'overlay, pas sur le popup\r\n    if (e.target === e.currentTarget) {\r\n      onClose();\r\n    }\r\n  };\r\n\r\n  // Empêcher la propagation du clic depuis le popup vers l'overlay\r\n  const handlePopupClick = (e: React.MouseEvent) => {\r\n    e.stopPropagation();\r\n  };\r\n\r\n  // Obtenir les styles\r\n  const styles = getEntityPopupStyles(darkMode);\r\n\r\n  return (\r\n    <div style={styles.overlay} onClick={handleOverlayClick}>\r\n      <div style={styles.popup} onClick={handlePopupClick}>\r\n        <div style={styles.header}>\r\n          <div>\r\n            <h3 style={styles.title}>{entityName}</h3>\r\n            <div style={styles.subtitle}>{t('entity.subtitle')}</div>\r\n          </div>\r\n          <button \r\n            style={styles.closeButton} \r\n            onClick={onClose}\r\n            onMouseEnter={handleMouseEnter}\r\n            onMouseLeave={handleMouseLeave}\r\n          >\r\n            ×\r\n          </button>\r\n        </div>\r\n\r\n        <div style={styles.content}>\r\n          {/* Section d'analyse historique */}\r\n          <HistoryAnalysisSection\r\n            darkMode={darkMode}\r\n            targetClassName={entityName}\r\n            currentClassData={currentClassData}\r\n            onImport={handleHistoryImport}\r\n            currentDiagramText={currentDiagramText}\r\n          />\r\n          <div style={styles.tableContainer}>\r\n            <table style={styles.table}>\r\n              <thead style={styles.tableHeader}>\r\n                <tr>\r\n                  <th style={styles.tableHeaderCell}>{t('entity.attributes').toUpperCase()}</th>\r\n                  <th style={styles.tableHeaderCell}>{t('entity.methods').toUpperCase()}</th>\r\n                </tr>\r\n              </thead>\r\n              <tbody style={styles.tableBody}>\r\n                <tr style={styles.tableRow}>\r\n                  <td style={styles.tableCell}>\r\n                    {uniqueAttributes.length > 0 ? (\r\n                      uniqueAttributes.map((attr, index) => (\r\n                        <div key={index} style={styles.checkboxContainer}>\r\n                          <input \r\n                            type=\"checkbox\" \r\n                            id={`attr-${index}`}\r\n                            checked={selectedAttributes.includes(attr)}\r\n                            onChange={() => handleAttributeToggle(attr)}\r\n                            style={styles.checkbox}\r\n                          />\r\n                          <label \r\n                            htmlFor={`attr-${index}`}\r\n                            style={styles.checkboxLabel}\r\n                          >\r\n                            {attr}\r\n                          </label>\r\n                        </div>\r\n                      ))\r\n                    ) : (\r\n                      <div style={styles.emptyState}>\r\n                        {t('entity.noAdditionalAttributes')}\r\n                      </div>\r\n                    )}\r\n                  </td>\r\n                  <td style={styles.tableCell}>\r\n                    {uniqueMethods.length > 0 ? (\r\n                      uniqueMethods.map((method, index) => (\r\n                        <div key={index} style={styles.checkboxContainer}>\r\n                          <input \r\n                            type=\"checkbox\" \r\n                            id={`method-${index}`}\r\n                            checked={selectedMethods.includes(method)}\r\n                            onChange={() => handleMethodToggle(method)}\r\n                            style={styles.checkbox}\r\n                          />\r\n                          <label \r\n                            htmlFor={`method-${index}`}\r\n                            style={styles.checkboxLabel}\r\n                          >\r\n                            {method}\r\n                          </label>\r\n                        </div>\r\n                      ))\r\n                    ) : (\r\n                      <div style={styles.emptyState}>\r\n                        {t('entity.noAdditionalMethods')}\r\n                      </div>\r\n                    )}\r\n                  </td>\r\n                </tr>\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n          \r\n          <div style={styles.buttonContainer}>\r\n            <button \r\n              style={styles.modifyButton} \r\n              onClick={handleModifyClick}\r\n              onMouseEnter={(e) => handleModifyHover(e, true)}\r\n              onMouseLeave={(e) => handleModifyHover(e, false)}\r\n            >\r\n              <span>✏️</span>\r\n              {t('entity.modifyEntity')}\r\n            </button>\r\n            <button\r\n              style={styles.cancelButton}\r\n              onClick={onClose}\r\n              onMouseEnter={(e) => handleCancelHover(e, true)}\r\n              onMouseLeave={(e) => handleCancelHover(e, false)}\r\n            >\r\n              {t('entity.cancel')}\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default EntityPopup;"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,oBAAoB,QAAQ,qBAAqB;AAC1D,OAAOC,sBAAsB,MAAM,0BAA0B;AAG7D,SAASC,WAAW,QAAQ,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAe5D,MAAMC,WAAuC,GAAGA,CAAC;EAC/CC,QAAQ;EACRC,UAAU;EACVC,QAAQ;EACRC,OAAO;EACPC,QAAQ;EACRC,gBAAgB,GAAG,EAAE;EACrBC,aAAa,GAAG,EAAE;EAClBC,mBAAmB,GAAG,EAAE;EACxBC,gBAAgB,GAAG,EAAE;EACrBC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM;IAAEC;EAAE,CAAC,GAAGf,WAAW,CAAC,CAAC;EAC3B;EACA,MAAM,CAACgB,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGpB,KAAK,CAACqB,QAAQ,CAAW,EAAE,CAAC;EAChF,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGvB,KAAK,CAACqB,QAAQ,CAAW,EAAE,CAAC;;EAE1E;;EAEA;EACA,MAAMG,gBAA2B,GAAGxB,KAAK,CAACyB,OAAO,CAAC,OAAO;IACvDC,IAAI,EAAElB,UAAU;IAChBmB,UAAU,EAAE,CAAC,GAAGf,gBAAgB,EAAE,GAAGE,mBAAmB,CAAC;IACzDc,OAAO,EAAE,CAAC,GAAGf,aAAa,EAAE,GAAGE,gBAAgB;EACjD,CAAC,CAAC,EAAE,CAACP,UAAU,EAAEI,gBAAgB,EAAEE,mBAAmB,EAAED,aAAa,EAAEE,gBAAgB,CAAC,CAAC;;EAEzF;EACA;EACA,MAAMc,gBAAgB,GAAGjB,gBAAgB,CAACkB,MAAM,CAACC,IAAI,IAAI;IACvD;IACA,MAAMC,QAAQ,GAAGD,IAAI,CAACE,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;IAEjF,OAAO,CAACtB,mBAAmB,CAACuB,IAAI,CAACC,aAAa,IAAI;MAChD;MACA,MAAMC,iBAAiB,GAAGD,aAAa,CAACL,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACnG,OAAOG,iBAAiB,KAAKP,QAAQ;IACvC,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF,MAAMQ,aAAa,GAAG3B,aAAa,CAACiB,MAAM,CAACW,MAAM,IAAI;IACnD;IACA,MAAMC,UAAU,GAAGD,MAAM,CAACR,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;IAErF,OAAO,CAACrB,gBAAgB,CAACsB,IAAI,CAACM,eAAe,IAAI;MAC/C;MACA,MAAMC,mBAAmB,GAAGD,eAAe,CAACV,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACvG,OAAOQ,mBAAmB,KAAKF,UAAU;IAC3C,CAAC,CAAC;EACJ,CAAC,CAAC;;EAEF;EACAG,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEtC,UAAU,CAAC;EACrEqC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAElC,gBAAgB,CAAC;EAC3DiC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEjC,aAAa,CAAC;EACrDgC,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEhC,mBAAmB,CAAC;EACjE+B,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE/B,gBAAgB,CAAC;EAC3D8B,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEjB,gBAAgB,CAAC;EAC9DgB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEN,aAAa,CAAC;;EAExD;EACA,MAAMO,qBAAqB,GAAIC,SAAiB,IAAK;IACnD5B,qBAAqB,CAAC6B,IAAI,IACxBA,IAAI,CAACC,QAAQ,CAACF,SAAS,CAAC,GACpBC,IAAI,CAACnB,MAAM,CAACC,IAAI,IAAIA,IAAI,KAAKiB,SAAS,CAAC,GACvC,CAAC,GAAGC,IAAI,EAAED,SAAS,CACzB,CAAC;EACH,CAAC;EAED,MAAMG,kBAAkB,GAAIV,MAAc,IAAK;IAC7ClB,kBAAkB,CAAC0B,IAAI,IACrBA,IAAI,CAACC,QAAQ,CAACT,MAAM,CAAC,GACjBQ,IAAI,CAACnB,MAAM,CAACsB,CAAC,IAAIA,CAAC,KAAKX,MAAM,CAAC,GAC9B,CAAC,GAAGQ,IAAI,EAAER,MAAM,CACtB,CAAC;EACH,CAAC;;EAED;EACA,MAAMY,mBAAmB,GAAIC,YAAuB,IAAK;IACvD;IACA;IACA,MAAMC,qBAAqB,GAAG,CAAC,GAAGpC,kBAAkB,CAAC;IACrD,MAAMqC,kBAAkB,GAAG,CAAC,GAAGlC,eAAe,CAAC;;IAE/C;IACAgC,YAAY,CAAC3B,UAAU,CAAC8B,OAAO,CAAC1B,IAAI,IAAI;MACtC,IAAI,CAACwB,qBAAqB,CAACL,QAAQ,CAACnB,IAAI,CAAC,EAAE;QACzCwB,qBAAqB,CAACG,IAAI,CAAC3B,IAAI,CAAC;MAClC;IACF,CAAC,CAAC;;IAEF;IACAuB,YAAY,CAAC1B,OAAO,CAAC6B,OAAO,CAAChB,MAAM,IAAI;MACrC,IAAI,CAACe,kBAAkB,CAACN,QAAQ,CAACT,MAAM,CAAC,EAAE;QACxCe,kBAAkB,CAACE,IAAI,CAACjB,MAAM,CAAC;MACjC;IACF,CAAC,CAAC;;IAEF;IACArB,qBAAqB,CAACmC,qBAAqB,CAAC;IAC5ChC,kBAAkB,CAACiC,kBAAkB,CAAC;EACxC,CAAC;;EAID;EACA,MAAMG,iBAAiB,GAAGA,CAAA,KAAM;IAC9BhD,QAAQ,CAACQ,kBAAkB,EAAEG,eAAe,CAAC;EAC/C,CAAC;;EAED;EACA,MAAMsC,gBAAgB,GAAIC,CAAmB,IAAK;IAChD,IAAIA,CAAC,CAACC,MAAM,KAAKD,CAAC,CAACE,aAAa,EAAE;MAC/BF,CAAC,CAACC,MAAM,CAAuBE,KAAK,CAACC,eAAe,GAAG1D,QAAQ,GAAG,SAAS,GAAG,SAAS;IAC1F;EACF,CAAC;EAED,MAAM2D,gBAAgB,GAAIL,CAAmB,IAAK;IAChD,IAAIA,CAAC,CAACC,MAAM,KAAKD,CAAC,CAACE,aAAa,EAAE;MAC/BF,CAAC,CAACC,MAAM,CAAuBE,KAAK,CAACC,eAAe,GAAG1D,QAAQ,GAAG,SAAS,GAAG,SAAS;IAC1F;EACF,CAAC;EAED,MAAM4D,iBAAiB,GAAGA,CAACN,CAAmB,EAAEO,OAAgB,KAAK;IAClEP,CAAC,CAACC,MAAM,CAAuBE,KAAK,CAACC,eAAe,GAAGG,OAAO,GAAG,SAAS,GAAG,SAAS;IACtFP,CAAC,CAACC,MAAM,CAAuBE,KAAK,CAACK,SAAS,GAAGD,OAAO,GAAG,kBAAkB,GAAG,eAAe;EAClG,CAAC;EAED,MAAME,iBAAiB,GAAGA,CAACT,CAAmB,EAAEO,OAAgB,KAAK;IAClEP,CAAC,CAACC,MAAM,CAAuBE,KAAK,CAACC,eAAe,GAAGG,OAAO,GAC1D7D,QAAQ,GAAG,SAAS,GAAG,SAAS,GACjC,aAAa;EACnB,CAAC;;EAED;EACA,MAAMgE,kBAAkB,GAAIV,CAAmB,IAAK;IAClD;IACA,IAAIA,CAAC,CAACC,MAAM,KAAKD,CAAC,CAACE,aAAa,EAAE;MAChCrD,OAAO,CAAC,CAAC;IACX;EACF,CAAC;;EAED;EACA,MAAM8D,gBAAgB,GAAIX,CAAmB,IAAK;IAChDA,CAAC,CAACY,eAAe,CAAC,CAAC;EACrB,CAAC;;EAED;EACA,MAAMC,MAAM,GAAGzE,oBAAoB,CAACM,QAAQ,CAAC;EAE7C,oBACEF,OAAA;IAAK2D,KAAK,EAAEU,MAAM,CAACC,OAAQ;IAACC,OAAO,EAAEL,kBAAmB;IAAAM,QAAA,eACtDxE,OAAA;MAAK2D,KAAK,EAAEU,MAAM,CAACI,KAAM;MAACF,OAAO,EAAEJ,gBAAiB;MAAAK,QAAA,gBAClDxE,OAAA;QAAK2D,KAAK,EAAEU,MAAM,CAACK,MAAO;QAAAF,QAAA,gBACxBxE,OAAA;UAAAwE,QAAA,gBACExE,OAAA;YAAI2D,KAAK,EAAEU,MAAM,CAACM,KAAM;YAAAH,QAAA,EAAErE;UAAU;YAAAyE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC1C/E,OAAA;YAAK2D,KAAK,EAAEU,MAAM,CAACW,QAAS;YAAAR,QAAA,EAAE3D,CAAC,CAAC,iBAAiB;UAAC;YAAA+D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC,eACN/E,OAAA;UACE2D,KAAK,EAAEU,MAAM,CAACY,WAAY;UAC1BV,OAAO,EAAElE,OAAQ;UACjB6E,YAAY,EAAE3B,gBAAiB;UAC/B4B,YAAY,EAAEtB,gBAAiB;UAAAW,QAAA,EAChC;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEN/E,OAAA;QAAK2D,KAAK,EAAEU,MAAM,CAACe,OAAQ;QAAAZ,QAAA,gBAEzBxE,OAAA,CAACH,sBAAsB;UACrBK,QAAQ,EAAEA,QAAS;UACnBmF,eAAe,EAAElF,UAAW;UAC5BgB,gBAAgB,EAAEA,gBAAiB;UACnCmE,QAAQ,EAAEtC,mBAAoB;UAC9BrC,kBAAkB,EAAEA;QAAmB;UAAAiE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC,eACF/E,OAAA;UAAK2D,KAAK,EAAEU,MAAM,CAACkB,cAAe;UAAAf,QAAA,eAChCxE,OAAA;YAAO2D,KAAK,EAAEU,MAAM,CAACmB,KAAM;YAAAhB,QAAA,gBACzBxE,OAAA;cAAO2D,KAAK,EAAEU,MAAM,CAACoB,WAAY;cAAAjB,QAAA,eAC/BxE,OAAA;gBAAAwE,QAAA,gBACExE,OAAA;kBAAI2D,KAAK,EAAEU,MAAM,CAACqB,eAAgB;kBAAAlB,QAAA,EAAE3D,CAAC,CAAC,mBAAmB,CAAC,CAAC8E,WAAW,CAAC;gBAAC;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC9E/E,OAAA;kBAAI2D,KAAK,EAAEU,MAAM,CAACqB,eAAgB;kBAAAlB,QAAA,EAAE3D,CAAC,CAAC,gBAAgB,CAAC,CAAC8E,WAAW,CAAC;gBAAC;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACR/E,OAAA;cAAO2D,KAAK,EAAEU,MAAM,CAACuB,SAAU;cAAApB,QAAA,eAC7BxE,OAAA;gBAAI2D,KAAK,EAAEU,MAAM,CAACwB,QAAS;gBAAArB,QAAA,gBACzBxE,OAAA;kBAAI2D,KAAK,EAAEU,MAAM,CAACyB,SAAU;kBAAAtB,QAAA,EACzBhD,gBAAgB,CAACuE,MAAM,GAAG,CAAC,GAC1BvE,gBAAgB,CAACwE,GAAG,CAAC,CAACtE,IAAI,EAAEuE,KAAK,kBAC/BjG,OAAA;oBAAiB2D,KAAK,EAAEU,MAAM,CAAC6B,iBAAkB;oBAAA1B,QAAA,gBAC/CxE,OAAA;sBACEmG,IAAI,EAAC,UAAU;sBACfC,EAAE,EAAE,QAAQH,KAAK,EAAG;sBACpBI,OAAO,EAAEvF,kBAAkB,CAAC+B,QAAQ,CAACnB,IAAI,CAAE;sBAC3C4E,QAAQ,EAAEA,CAAA,KAAM5D,qBAAqB,CAAChB,IAAI,CAAE;sBAC5CiC,KAAK,EAAEU,MAAM,CAACkC;oBAAS;sBAAA3B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxB,CAAC,eACF/E,OAAA;sBACEwG,OAAO,EAAE,QAAQP,KAAK,EAAG;sBACzBtC,KAAK,EAAEU,MAAM,CAACoC,aAAc;sBAAAjC,QAAA,EAE3B9C;oBAAI;sBAAAkD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC;kBAAA,GAbAkB,KAAK;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAcV,CACN,CAAC,gBAEF/E,OAAA;oBAAK2D,KAAK,EAAEU,MAAM,CAACqC,UAAW;oBAAAlC,QAAA,EAC3B3D,CAAC,CAAC,+BAA+B;kBAAC;oBAAA+D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACL/E,OAAA;kBAAI2D,KAAK,EAAEU,MAAM,CAACyB,SAAU;kBAAAtB,QAAA,EACzBrC,aAAa,CAAC4D,MAAM,GAAG,CAAC,GACvB5D,aAAa,CAAC6D,GAAG,CAAC,CAAC5D,MAAM,EAAE6D,KAAK,kBAC9BjG,OAAA;oBAAiB2D,KAAK,EAAEU,MAAM,CAAC6B,iBAAkB;oBAAA1B,QAAA,gBAC/CxE,OAAA;sBACEmG,IAAI,EAAC,UAAU;sBACfC,EAAE,EAAE,UAAUH,KAAK,EAAG;sBACtBI,OAAO,EAAEpF,eAAe,CAAC4B,QAAQ,CAACT,MAAM,CAAE;sBAC1CkE,QAAQ,EAAEA,CAAA,KAAMxD,kBAAkB,CAACV,MAAM,CAAE;sBAC3CuB,KAAK,EAAEU,MAAM,CAACkC;oBAAS;sBAAA3B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxB,CAAC,eACF/E,OAAA;sBACEwG,OAAO,EAAE,UAAUP,KAAK,EAAG;sBAC3BtC,KAAK,EAAEU,MAAM,CAACoC,aAAc;sBAAAjC,QAAA,EAE3BpC;oBAAM;sBAAAwC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC;kBAAA,GAbAkB,KAAK;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAcV,CACN,CAAC,gBAEF/E,OAAA;oBAAK2D,KAAK,EAAEU,MAAM,CAACqC,UAAW;oBAAAlC,QAAA,EAC3B3D,CAAC,CAAC,4BAA4B;kBAAC;oBAAA+D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEN/E,OAAA;UAAK2D,KAAK,EAAEU,MAAM,CAACsC,eAAgB;UAAAnC,QAAA,gBACjCxE,OAAA;YACE2D,KAAK,EAAEU,MAAM,CAACuC,YAAa;YAC3BrC,OAAO,EAAEjB,iBAAkB;YAC3B4B,YAAY,EAAG1B,CAAC,IAAKM,iBAAiB,CAACN,CAAC,EAAE,IAAI,CAAE;YAChD2B,YAAY,EAAG3B,CAAC,IAAKM,iBAAiB,CAACN,CAAC,EAAE,KAAK,CAAE;YAAAgB,QAAA,gBAEjDxE,OAAA;cAAAwE,QAAA,EAAM;YAAE;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EACdlE,CAAC,CAAC,qBAAqB,CAAC;UAAA;YAAA+D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACT/E,OAAA;YACE2D,KAAK,EAAEU,MAAM,CAACwC,YAAa;YAC3BtC,OAAO,EAAElE,OAAQ;YACjB6E,YAAY,EAAG1B,CAAC,IAAKS,iBAAiB,CAACT,CAAC,EAAE,IAAI,CAAE;YAChD2B,YAAY,EAAG3B,CAAC,IAAKS,iBAAiB,CAACT,CAAC,EAAE,KAAK,CAAE;YAAAgB,QAAA,EAEhD3D,CAAC,CAAC,eAAe;UAAC;YAAA+D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAGH,CAAC;AAEV,CAAC;AAACnE,EAAA,CA1QIX,WAAuC;EAAA,QAY7BH,WAAW;AAAA;AAAAgH,EAAA,GAZrB7G,WAAuC;AA4Q7C,eAAeA,WAAW;AAAC,IAAA6G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}