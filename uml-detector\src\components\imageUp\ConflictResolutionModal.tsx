// ConflictResolutionModal.tsx - Modal pour résoudre les conflits lors de l'import
import React, { useState } from 'react';
import { ClassData } from '../../services/HistoryAnalysisService';
import { AlertTriangle, Check, X, ArrowRight } from 'lucide-react';
import { useLanguage } from '../../context/LanguageContext';

interface ConflictItem {
  type: 'attribute' | 'method';
  name: string;
  current: string;
  imported: string[];
  resolution: 'keep' | 'replace' | 'merge';
}

interface ConflictResolutionModalProps {
  darkMode: boolean;
  isOpen: boolean;
  currentClass: ClassData;
  importedClasses: ClassData[];
  conflicts: { attributes: string[], methods: string[] };
  onResolve: (resolvedData: ClassData) => void;
  onCancel: () => void;
}

const ConflictResolutionModal: React.FC<ConflictResolutionModalProps> = ({
  darkMode,
  isOpen,
  currentClass,
  importedClasses,
  conflicts,
  onResolve,
  onCancel
}) => {
  const { t } = useLanguage();
  const [conflictItems, setConflictItems] = useState<ConflictItem[]>(() => {
    const items: ConflictItem[] = [];
    
    // Traiter les conflits d'attributs
    conflicts.attributes.forEach(attrName => {
      const current = currentClass.attributes.find(attr => 
        attr.toLowerCase().includes(attrName.toLowerCase())
      ) || attrName;
      
      const imported = importedClasses.flatMap(cls => 
        cls.attributes.filter(attr => 
          attr.toLowerCase().includes(attrName.toLowerCase())
        )
      );
      
      items.push({
        type: 'attribute',
        name: attrName,
        current,
        imported,
        resolution: 'merge'
      });
    });
    
    // Traiter les conflits de méthodes
    conflicts.methods.forEach(methodName => {
      const current = currentClass.methods.find(method => 
        method.toLowerCase().includes(methodName.toLowerCase())
      ) || methodName;
      
      const imported = importedClasses.flatMap(cls => 
        cls.methods.filter(method => 
          method.toLowerCase().includes(methodName.toLowerCase())
        )
      );
      
      items.push({
        type: 'method',
        name: methodName,
        current,
        imported,
        resolution: 'merge'
      });
    });
    
    return items;
  });

  const handleResolutionChange = (index: number, resolution: 'keep' | 'replace' | 'merge') => {
    const newItems = [...conflictItems];
    newItems[index].resolution = resolution;
    setConflictItems(newItems);
  };

  const handleResolve = () => {
    // Appliquer les résolutions de conflits
    const resolvedAttributes = [...currentClass.attributes];
    const resolvedMethods = [...currentClass.methods];
    
    conflictItems.forEach(item => {
      if (item.type === 'attribute') {
        const currentIndex = resolvedAttributes.findIndex(attr => 
          attr.toLowerCase().includes(item.name.toLowerCase())
        );
        
        switch (item.resolution) {
          case 'keep':
            // Ne rien faire, garder l'actuel
            break;
          case 'replace':
            if (currentIndex !== -1 && item.imported.length > 0) {
              resolvedAttributes[currentIndex] = item.imported[0];
            }
            break;
          case 'merge':
            // Ajouter tous les variants importés qui ne sont pas déjà présents
            item.imported.forEach(importedAttr => {
              if (!resolvedAttributes.some(existing => 
                existing.toLowerCase() === importedAttr.toLowerCase()
              )) {
                resolvedAttributes.push(importedAttr);
              }
            });
            break;
        }
      } else {
        const currentIndex = resolvedMethods.findIndex(method => 
          method.toLowerCase().includes(item.name.toLowerCase())
        );
        
        switch (item.resolution) {
          case 'keep':
            // Ne rien faire, garder l'actuel
            break;
          case 'replace':
            if (currentIndex !== -1 && item.imported.length > 0) {
              resolvedMethods[currentIndex] = item.imported[0];
            }
            break;
          case 'merge':
            // Ajouter tous les variants importés qui ne sont pas déjà présents
            item.imported.forEach(importedMethod => {
              if (!resolvedMethods.some(existing => 
                existing.toLowerCase() === importedMethod.toLowerCase()
              )) {
                resolvedMethods.push(importedMethod);
              }
            });
            break;
        }
      }
    });
    
    // Ajouter les éléments non-conflictuels des classes importées
    importedClasses.forEach(importedClass => {
      importedClass.attributes.forEach(attr => {
        const isConflict = conflicts.attributes.some(conflictAttr => 
          attr.toLowerCase().includes(conflictAttr.toLowerCase())
        );
        
        if (!isConflict && !resolvedAttributes.some(existing => 
          existing.toLowerCase() === attr.toLowerCase()
        )) {
          resolvedAttributes.push(attr);
        }
      });
      
      importedClass.methods.forEach(method => {
        const isConflict = conflicts.methods.some(conflictMethod => 
          method.toLowerCase().includes(conflictMethod.toLowerCase())
        );
        
        if (!isConflict && !resolvedMethods.some(existing => 
          existing.toLowerCase() === method.toLowerCase()
        )) {
          resolvedMethods.push(method);
        }
      });
    });
    
    const resolvedClass: ClassData = {
      name: currentClass.name,
      attributes: resolvedAttributes,
      methods: resolvedMethods
    };
    
    onResolve(resolvedClass);
  };

  const getModalStyles = () => ({
    overlay: {
      position: 'fixed' as const,
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      display: isOpen ? 'flex' : 'none',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1100,
      backdropFilter: 'blur(8px)',
      WebkitBackdropFilter: 'blur(8px)',
    },
    modal: {
      backgroundColor: darkMode ? '#0a0f1c' : '#ffffff',
      borderRadius: '16px',
      boxShadow: darkMode 
        ? '0 25px 50px -12px rgba(0, 0, 0, 0.8)' 
        : '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
      padding: '0',
      minWidth: '600px',
      maxWidth: '800px',
      maxHeight: '80vh',
      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)'}`,
      display: 'flex',
      flexDirection: 'column' as const,
    },
    header: {
      padding: '24px 28px 20px',
      borderBottom: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.15)' : 'rgba(59, 130, 246, 0.08)'}`,
      display: 'flex',
      alignItems: 'center',
      gap: '12px',
    },
    title: {
      fontSize: '20px',
      fontWeight: '700',
      color: darkMode ? '#f8fafc' : '#0f172a',
      margin: 0,
    },
    content: {
      padding: '24px 28px',
      overflowY: 'auto' as const,
      flex: 1,
    },
    conflictItem: {
      marginBottom: '24px',
      padding: '16px',
      backgroundColor: darkMode ? 'rgba(30, 41, 59, 0.5)' : 'rgba(248, 250, 252, 0.8)',
      borderRadius: '12px',
      border: `1px solid ${darkMode ? 'rgba(245, 158, 11, 0.3)' : 'rgba(245, 158, 11, 0.2)'}`,
    },
    conflictHeader: {
      display: 'flex',
      alignItems: 'center',
      gap: '8px',
      marginBottom: '12px',
    },
    conflictType: {
      fontSize: '14px',
      fontWeight: '600',
      color: darkMode ? '#fbbf24' : '#d97706',
      textTransform: 'uppercase' as const,
      letterSpacing: '0.05em',
    },
    conflictName: {
      fontSize: '16px',
      fontWeight: '600',
      color: darkMode ? '#e2e8f0' : '#374151',
    },
    comparisonContainer: {
      display: 'grid',
      gridTemplateColumns: '1fr auto 1fr',
      gap: '16px',
      alignItems: 'center',
      marginBottom: '16px',
    },
    versionBox: {
      padding: '12px',
      backgroundColor: darkMode ? 'rgba(15, 23, 42, 0.8)' : 'rgba(255, 255, 255, 0.9)',
      borderRadius: '8px',
      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)'}`,
    },
    versionLabel: {
      fontSize: '12px',
      fontWeight: '600',
      color: darkMode ? '#94a3b8' : '#64748b',
      marginBottom: '8px',
      textTransform: 'uppercase' as const,
    },
    versionContent: {
      fontSize: '14px',
      color: darkMode ? '#cbd5e1' : '#4b5563',
      fontFamily: 'monospace',
    },
    resolutionOptions: {
      display: 'flex',
      gap: '12px',
    },
    resolutionButton: {
      padding: '8px 16px',
      borderRadius: '8px',
      border: 'none',
      fontSize: '14px',
      fontWeight: '600',
      cursor: 'pointer',
      transition: 'all 0.2s ease',
      display: 'flex',
      alignItems: 'center',
      gap: '6px',
    },
    footer: {
      padding: '20px 28px',
      borderTop: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.15)' : 'rgba(59, 130, 246, 0.08)'}`,
      display: 'flex',
      justifyContent: 'flex-end',
      gap: '12px',
    },
    button: {
      padding: '12px 24px',
      borderRadius: '8px',
      fontSize: '14px',
      fontWeight: '600',
      cursor: 'pointer',
      transition: 'all 0.2s ease',
      border: 'none',
    },
    cancelButton: {
      backgroundColor: 'transparent',
      color: darkMode ? '#94a3b8' : '#64748b',
      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)'}`,
    },
    resolveButton: {
      backgroundColor: '#3b82f6',
      color: '#ffffff',
    }
  });

  const getResolutionButtonStyle = (resolution: string, currentResolution: string) => {
    const isSelected = resolution === currentResolution;
    return {
      ...getModalStyles().resolutionButton,
      backgroundColor: isSelected 
        ? '#3b82f6' 
        : darkMode ? 'rgba(30, 41, 59, 0.8)' : 'rgba(248, 250, 252, 0.8)',
      color: isSelected 
        ? '#ffffff' 
        : darkMode ? '#cbd5e1' : '#4b5563',
      border: `1px solid ${isSelected 
        ? '#3b82f6' 
        : darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)'}`
    };
  };

  const styles = getModalStyles();

  if (!isOpen) return null;

  return (
    <div style={styles.overlay} onClick={onCancel}>
      <div style={styles.modal} onClick={(e) => e.stopPropagation()}>
        <div style={styles.header}>
          <AlertTriangle size={24} color="#f59e0b" />
          <h3 style={styles.title}>{t('conflict.title')}</h3>
        </div>
        
        <div style={styles.content}>
          {conflictItems.map((item, index) => (
            <div key={`${item.type}-${item.name}`} style={styles.conflictItem}>
              <div style={styles.conflictHeader}>
                <span style={styles.conflictType}>{item.type === 'attribute' ? t('conflict.attribute') : t('conflict.method')}</span>
                <span style={styles.conflictName}>{item.name}</span>
              </div>
              
              <div style={styles.comparisonContainer}>
                <div style={styles.versionBox}>
                  <div style={styles.versionLabel}>Version Actuelle</div>
                  <div style={styles.versionContent}>{item.current}</div>
                </div>
                
                <ArrowRight size={20} color={darkMode ? '#64748b' : '#9ca3af'} />
                
                <div style={styles.versionBox}>
                  <div style={styles.versionLabel}>Versions Importées</div>
                  {item.imported.map((imported, idx) => (
                    <div key={idx} style={styles.versionContent}>{imported}</div>
                  ))}
                </div>
              </div>
              
              <div style={styles.resolutionOptions}>
                <button
                  style={getResolutionButtonStyle('keep', item.resolution)}
                  onClick={() => handleResolutionChange(index, 'keep')}
                >
                  <X size={16} />
                  Garder l'actuel
                </button>
                <button
                  style={getResolutionButtonStyle('replace', item.resolution)}
                  onClick={() => handleResolutionChange(index, 'replace')}
                >
                  <ArrowRight size={16} />
                  Remplacer
                </button>
                <button
                  style={getResolutionButtonStyle('merge', item.resolution)}
                  onClick={() => handleResolutionChange(index, 'merge')}
                >
                  <Check size={16} />
                  Fusionner
                </button>
              </div>
            </div>
          ))}
        </div>
        
        <div style={styles.footer}>
          <button style={{...styles.button, ...styles.cancelButton}} onClick={onCancel}>
            Annuler
          </button>
          <button style={{...styles.button, ...styles.resolveButton}} onClick={handleResolve}>
            Appliquer les Résolutions
          </button>
        </div>
      </div>
    </div>
  );
};

export default ConflictResolutionModal;
