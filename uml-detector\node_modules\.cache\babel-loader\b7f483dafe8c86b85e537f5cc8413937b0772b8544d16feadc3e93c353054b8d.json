{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\FixTorchUMLDGM\\\\uml-detector\\\\src\\\\context\\\\LanguageContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\n\n// Types pour les langues supportées\n\n// Interface pour le contexte de langue\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// Traductions\nconst translations = {\n  fr: {\n    // Header\n    'app.title': 'AI UML Class Analyzer',\n    'button.login': 'Connexion',\n    // Tabs\n    'tab.upload': 'Import d\\'image',\n    'tab.relations': 'Analyse de relations',\n    'tab.text': 'Extraction de diagramme',\n    'tab.documentation': 'Documentation',\n    // Content headers\n    'content.upload.title': 'Import de diagramme UML',\n    'content.upload.subtitle': 'Téléchargez et analysez vos diagrammes de classes UML avec notre IA avancée',\n    'content.relations.title': 'Analyse de relations UML',\n    'content.relations.subtitle': 'Visualisez les relations détectées entre les classes de votre diagramme',\n    'content.text.title': 'Extraction de diagramme',\n    'content.text.subtitle': 'Extrayez et exportez des diagrammes UML',\n    'content.documentation.title': 'Documentation & Ressources',\n    'content.documentation.subtitle': 'Découvrez les fonctionnalités de UML Class Analyzer',\n    // Footer\n    'footer.text': '© 2025 UML Class Analyzer | Édition Enterprise v2.5.3',\n    // Language button\n    'language.current': 'FR',\n    'language.switch': 'Passer en anglais',\n    // ImageUploader\n    'imageUploader.dropZone': 'Glissez-déposez votre diagramme UML ou cliquez pour sélectionner',\n    'imageUploader.selectFile': 'Sélectionner un fichier',\n    'imageUploader.analyzing': 'Analyse en cours...',\n    'imageUploader.progress.waiting': 'En attente de l\\'image...',\n    'imageUploader.progress.converting': 'Conversion et optimisation de l\\'image...',\n    'imageUploader.progress.detecting': 'Détection des classes et relations avec notre modèle IA...',\n    'imageUploader.progress.analyzing': 'Analyse en cours ... Cette étape peut prendre quelques instants.',\n    'imageUploader.progress.fetching': 'Récupération des résultats...',\n    'imageUploader.progress.completed': 'Analyse terminée',\n    'imageUploader.progress.error': 'Erreur pendant l\\'analyse de l\\'image',\n    'imageUploader.progress.fetchError': 'Erreur lors de la récupération des résultats',\n    'imageUploader.error.processing': 'Erreur lors du traitement de l\\'image',\n    'imageUploader.error.connection': 'Erreur de connexion ou analyse',\n    'imageUploader.error.fetchText': 'Impossible de récupérer le texte extrait',\n    'imageUploader.error.thumbnail': 'Miniature non disponible',\n    'imageUploader.button.newAnalysis': 'Nouvelle analyse',\n    'imageUploader.button.viewRelations': 'Voir les relations détectées',\n    'imageUploader.button.extractDiagram': 'Extraire le diagramme',\n    // DetectionArrow\n    'detectionArrow.title': 'Détection de Relations UML',\n    'detectionArrow.loading': 'Chargement de l\\'image annotée...',\n    'detectionArrow.error': 'Impossible de charger l\\'image annotée. Veuillez vérifier que le serveur est en cours d\\'exécution.',\n    'detectionArrow.noImage': 'Aucune image annotée n\\'est disponible. Veuillez d\\'abord télécharger et analyser une image de diagramme UML.',\n    'detectionArrow.imageAlt': 'Diagramme UML annoté avec relations détectées',\n    'detectionArrow.analysisTitle': 'Analyse des Relations',\n    'detectionArrow.explanation': 'L\\'image ci-dessus montre les relations détectées entre les différentes classes de votre diagramme UML. Notre système d\\'IA a identifié les connexions et les a marquées selon leur type.',\n    'detectionArrow.legend.class': 'Classe',\n    'detectionArrow.legend.relation': 'Relation',\n    'detectionArrow.legend.aggregation': 'Agrégation',\n    'detectionArrow.legend.composition': 'Composition',\n    'detectionArrow.legend.generalization': 'Généralisation',\n    'detectionArrow.legend.association': 'Association',\n    'detectionArrow.tip.title': 'Astuce',\n    'detectionArrow.tip.text': 'Pour une analyse plus détaillée, consultez l\\'onglet \"Extraction de texte UML\" qui contient les informations textuelles complètes de toutes les classes détectées.',\n    // UMLDiagrameExtractor\n    'umlExtractor.title': 'Extracteur de Diagramme UML',\n    'umlExtractor.format.diagram': 'Code de diagramme',\n    'umlExtractor.format.java': 'Code Java',\n    'umlExtractor.format.python': 'Code Python',\n    'umlExtractor.placeholder.diagram': 'Entrez votre code diagramme ici...',\n    'umlExtractor.placeholder.java': 'Entrez votre code Java ici...',\n    'umlExtractor.placeholder.python': 'Entrez votre code Python ici...',\n    'umlExtractor.preview.empty': 'Entrez votre code {format} pour le visualiser ici',\n    'umlExtractor.error.syntax': 'Erreur de syntaxe:',\n    'umlExtractor.button.copy': 'Copier',\n    'umlExtractor.button.export': 'Exporter',\n    'umlExtractor.button.fullscreen': 'Plein écran',\n    'umlExtractor.button.zoomIn': 'Zoom +',\n    'umlExtractor.button.zoomOut': 'Zoom -',\n    'umlExtractor.button.resetZoom': 'Réinitialiser zoom',\n    'umlExtractor.copied': 'Copié !',\n    'umlExtractor.editor.title': 'Éditeur de code',\n    'umlExtractor.export.svg': 'Exporter SVG',\n    'umlExtractor.export.png': 'Exporter PNG',\n    'umlExtractor.modal.confirmTitle': 'Confirmer la suppression',\n    'umlExtractor.modal.confirmMessage': 'Êtes-vous sûr de vouloir effacer tout le contenu de l\\'éditeur ? Cette action est irréversible.',\n    'umlExtractor.modal.cancel': 'Annuler',\n    'umlExtractor.modal.clearContent': 'Effacer le contenu',\n    // Authentication\n    'auth.login.title': 'Connexion à votre compte',\n    'auth.signup.title': 'Créer un compte',\n    'auth.field.name': 'Nom',\n    'auth.field.email': 'Email',\n    'auth.field.password': 'Mot de passe',\n    'auth.field.confirmPassword': 'Confirmer le mot de passe',\n    'auth.placeholder.name': 'Votre nom',\n    'auth.placeholder.email': '<EMAIL>',\n    'auth.placeholder.password': 'Créer un mot de passe',\n    'auth.placeholder.confirmPassword': 'Confirmez votre mot de passe',\n    'auth.rememberMe': 'Se souvenir de moi',\n    'auth.forgotPassword': 'Mot de passe oublié ?',\n    'auth.acceptTerms': 'J\\'accepte les conditions d\\'utilisation',\n    'auth.button.login': 'Se connecter',\n    'auth.button.signup': 'S\\'inscrire',\n    'auth.button.createAccount': 'Créer un compte',\n    'auth.button.loggingIn': 'Connexion...',\n    'auth.button.creatingAccount': 'Création du compte...',\n    'auth.button.continueWithGoogle': 'Continuer avec Google',\n    'auth.button.logout': 'Se déconnecter',\n    'auth.or': 'OU',\n    'auth.noAccount': 'Vous n\\'avez pas de compte ?',\n    'auth.haveAccount': 'Vous avez déjà un compte ?',\n    'auth.error.emailRequired': 'L\\'email est requis',\n    'auth.error.emailInvalid': 'Veuillez entrer un email valide',\n    'auth.error.passwordRequired': 'Le mot de passe est requis',\n    'auth.error.passwordTooShort': 'Le mot de passe doit contenir au moins 6 caractères',\n    'auth.error.nameRequired': 'Le nom est requis',\n    'auth.error.confirmPasswordRequired': 'Veuillez confirmer votre mot de passe',\n    'auth.error.passwordMismatch': 'Les mots de passe ne correspondent pas',\n    'auth.error.authFailed': 'Échec de l\\'authentification',\n    'auth.error.invalidCredentials': 'Email ou mot de passe invalide',\n    'auth.error.emailInUse': 'Cet email est déjà utilisé',\n    'auth.error.weakPassword': 'Le mot de passe est trop faible',\n    'auth.error.tooManyRequests': 'Trop de tentatives échouées. Réessayez plus tard.',\n    'auth.error.googleLoginFailed': 'Connexion Google échouée. Veuillez réessayer.',\n    // HistorySidebar\n    'history.title': 'Historique UML',\n    'history.newProject': 'Nouveau projet',\n    'history.empty': 'Aucun historique',\n    'history.loading': 'Chargement...',\n    'history.error': 'Erreur lors du chargement de l\\'historique',\n    'history.deleteConfirm': 'Êtes-vous sûr de vouloir supprimer cet élément ?',\n    'history.deleteAllConfirm': 'Êtes-vous sûr de vouloir supprimer tout votre historique ? Cette action est irréversible.',\n    'history.delete': 'Supprimer',\n    'history.deleteAll': 'Supprimer tout l\\'historique',\n    'history.cancel': 'Annuler',\n    'history.loginPrompt': 'Connectez-vous pour voir votre historique',\n    'history.searchPlaceholder': 'Rechercher...',\n    'history.noResults': 'Aucun résultat trouvé',\n    'history.thumbnail': 'Miniature',\n    'history.reload': 'Recharger',\n    'history.shared': 'Partagé',\n    // History Analysis Section\n    'history.import.buttonDiagrams': 'Importer ({count} diagramme{plural} sélectionné{plural})',\n    'history.import.buttonElements': 'Importer ({count} élément{plural} sélectionné{plural})',\n    'history.preview.button': 'Voir aperçu',\n    'history.preview.title': 'Aperçu de la classe \"{className}\"',\n    'history.preview.classContent': 'Contenu de la classe',\n    'history.preview.relatedClasses': 'Classes liées',\n    'history.preview.noRelatedClasses': 'Aucune classe liée détectée',\n    'history.granular.selectElements': 'Sélectionner les éléments à importer',\n    'history.granular.checkAll': 'Cocher tous',\n    'history.granular.attributes': 'Attributs',\n    'history.granular.methods': 'Méthodes',\n    // Analysis Results\n    'analysis.error.updateFailed': 'Échec de la mise à jour du texte. Veuillez réessayer.',\n    'analysis.diagramAlt': 'Diagramme UML',\n    'analysis.tooltip.viewAnnotated': 'Cliquez pour voir l\\'image annotée',\n    'analysis.tooltip.convertDiagram': 'Si votre diagramme est manuscrit et vous souhaitez le convertir vers un diagramme imprimé, cliquez ici',\n    'analysis.imageAnalyzed': 'Image analysée',\n    'analysis.extractedText': 'Texte extrait',\n    'analysis.copied': 'Copié!',\n    'analysis.newProject': 'Nouveau Projet',\n    // Conflict Resolution Modal\n    'conflict.title': 'Résolution des Conflits',\n    'conflict.attribute': 'Attribut',\n    'conflict.method': 'Méthode',\n    'conflict.currentVersion': 'Version Actuelle',\n    'conflict.importedVersions': 'Versions Importées',\n    'conflict.keepCurrent': 'Garder l\\'actuel',\n    'conflict.replace': 'Remplacer',\n    'conflict.merge': 'Fusionner',\n    'conflict.cancel': 'Annuler',\n    'conflict.applyResolutions': 'Appliquer les Résolutions',\n    // Entity Popup\n    'entity.subtitle': 'UML IA propose plusieurs choix possibles pour cette entité.',\n    'entity.attributes': 'Attributs',\n    'entity.methods': 'Méthodes',\n    'entity.noAdditionalAttributes': 'Aucun attribut supplémentaire',\n    'entity.noAdditionalMethods': 'Aucune méthode supplémentaire',\n    'entity.modifyEntity': 'Modifier l\\'entité',\n    'entity.cancel': 'Annuler',\n    // History Analysis Section\n    'historyAnalysis.title': 'Analyse Historique',\n    'historyAnalysis.noResults': 'Aucun diagramme historique trouvé pour la classe \"{className}\"',\n    'historyAnalysis.sortBy': 'Trier par:',\n    'historyAnalysis.conflictsDetected': 'Conflits détectés: {attributes} attribut(s), {methods} méthode(s)',\n    'historyAnalysis.access.owner': 'Votre diagramme',\n    'historyAnalysis.access.shared': 'Diagramme partagé',\n    'historyAnalysis.access.public': 'Diagramme public',\n    'historyAnalysis.access.restricted': 'Accès restreint',\n    // Documentation\n    'doc.description': 'UML Class Analyzer est une application web avancée qui utilise l\\'intelligence artificielle pour analyser automatiquement les diagrammes de classes UML. Notre solution combine la puissance des modèles pour offrir une précision exceptionnelle dans la détection et l\\'extraction d\\'éléments UML.',\n    'doc.features.aiDetection.title': 'Détection IA Avancée',\n    'doc.features.aiDetection.description': 'Utilise des modèles entraînés spécifiquement pour reconnaître les éléments UML avec une précision de plus de 95%.',\n    'doc.features.fastProcessing.title': 'Traitement Rapide',\n    'doc.features.fastProcessing.description': 'Analyse complète d\\'un diagramme UML en moins de 30 secondes grâce à notre infrastructure optimisée.',\n    'doc.features.multiFormat.title': 'Multi-format',\n    'doc.features.multiFormat.description': 'Support complet des formats PNG, JPG, JPEG et PDF avec export pour le moment vers Java et Mermaid.',\n    'doc.featuresTitle': 'Fonctionnalités principales',\n    'doc.features.uploadAnalyze.title': 'Upload & Analyse',\n    'doc.features.uploadAnalyze.description': 'Téléchargez vos diagrammes UML (PNG, JPG, PDF) et obtenez une analyse complète en quelques secondes avec notre IA spécialisée.',\n    'doc.features.relationDetection.title': 'Détection de Relations',\n    'doc.features.relationDetection.description': 'Identification automatique des relations UML : héritage, composition, agrégation, association avec visualisation annotée.',\n    'doc.features.textExtraction.title': 'Extraction de Texte',\n    'doc.features.textExtraction.description': 'Extraction intelligente des classes, attributs et méthodes avec organisation automatique et export multi-format.',\n    'doc.features.codeGeneration.title': 'Génération de Code',\n    'doc.features.codeGeneration.description': 'Conversion automatique vers Java et diagrammes Mermaid avec structure de classes complète.',\n    'doc.features.historyManagement.title': 'Historique & Gestion',\n    'doc.features.historyManagement.description': 'Sauvegarde automatique des analyses, historique complet avec authentification Firebase et gestion des projets.',\n    'doc.features.modernInterface.title': 'Interface Moderne',\n    'doc.features.modernInterface.description': 'Interface utilisateur intuitive avec mode sombre/clair, design responsive et expérience utilisateur optimisée.',\n    // Guide d'utilisation\n    'doc.guideTitle': 'Guide d\\'utilisation étape par étape',\n    'doc.guide.step1.title': 'Téléchargement du diagramme',\n    'doc.guide.step1.description': 'Cliquez sur \"Sélectionner un fichier\" ou glissez-déposez votre diagramme UML. Formats supportés : PNG, JPG, JPEG, PDF. Taille maximale : 50MB.',\n    'doc.guide.step2.title': 'Analyse automatique',\n    'doc.guide.step2.description': 'Notre IA analyse votre diagramme en utilisant plusieurs modèles pour de bons résultats. Le processus prend généralement 15-30 secondes selon la complexité.',\n    'doc.guide.step3.title': 'Visualisation des relations',\n    'doc.guide.step3.description': 'Consultez l\\'onglet \"Analyse de relations\" pour voir votre diagramme annoté avec toutes les relations détectées (héritage, composition, agrégation, etc.).',\n    'doc.guide.step4.title': 'Export et génération de code',\n    'doc.guide.step4.description': 'Dans l\\'onglet \"Extraction de diagramme\", exportez vos résultats en Java ou Mermaid. Personnalisez le code généré selon vos besoins.',\n    // Technologies\n    'doc.techTitle': 'Stack technologique',\n    'doc.tech.frontend.title': 'Frontend',\n    'doc.tech.frontend.description': 'React 18, TypeScript, CSS-in-JS',\n    'doc.tech.backend.title': 'Backend',\n    'doc.tech.backend.description': 'FastAPI, Python, Uvicorn',\n    'doc.tech.ai.title': 'IA & Vision',\n    'doc.tech.ai.description': 'Confidentiel',\n    'doc.tech.database.title': 'Base de données',\n    'doc.tech.database.description': 'Firebase Firestore, Authentication',\n    'doc.tech.visualization.title': 'Visualisation',\n    'doc.tech.visualization.description': 'Mermaid.js, SVG, Canvas API',\n    // API\n    'doc.apiTitle': 'Documentation API',\n    'doc.api.endpointsTitle': 'Endpoints principaux',\n    'doc.api.detect.description': 'Endpoint principal pour l\\'analyse de diagrammes UML. Accepte les fichiers image et retourne l\\'analyse complète avec détection de relations.',\n    'doc.api.annotated.description': 'Récupère l\\'image annotée avec les relations détectées visualisées.',\n    'doc.api.results.description': 'Télécharge le fichier texte contenant l\\'extraction complète des classes, attributs et méthodes.',\n    // FAQ\n    'doc.faqTitle': 'Questions fréquemment posées',\n    'doc.faq.formats.question': '🖼️ Quels formats d\\'image sont supportés ?',\n    'doc.faq.formats.answer': 'Notre application supporte les formats PNG, JPG, JPEG et PDF. Pour de meilleurs résultats, utilisez des images de haute résolution (minimum 800x600) et bien éclairées. La taille maximale autorisée est de 50MB.',\n    'doc.faq.offline.question': '🌐 L\\'application fonctionne-t-elle hors ligne ?',\n    'doc.faq.offline.answer': 'Non, notre application nécessite une connexion Internet active car elle utilise des modèles d\\'IA hébergés sur nos serveurs pour analyser vos diagrammes. L\\'interface peut être mise en cache mais l\\'analyse nécessite une connexion.',\n    'doc.faq.codeGeneration.question': '💻 Puis-je générer du code à partir de mon diagramme UML ?',\n    'doc.faq.codeGeneration.answer': 'Oui ! Notre fonctionnalité de génération de code vous permet de transformer vos diagrammes UML en code Java ou en diagrammes Mermaid. Le code généré inclut les classes, attributs, méthodes et relations détectées.',\n    'doc.faq.accuracy.question': '🎯 Comment améliorer la précision de détection ?',\n    'doc.faq.accuracy.answer': 'Pour optimiser les résultats : utilisez des diagrammes bien contrastés, assurez-vous que les textes sont lisibles (police minimum 12pt), évitez les images floues, utilisez des couleurs distinctes pour les différents éléments, et vérifiez que les lignes de relation sont clairement visibles.',\n    'doc.faq.security.question': '🔒 Mes données sont-elles sécurisées ?',\n    'doc.faq.security.answer': 'Oui, nous utilisons Firebase Authentication pour la sécurité des comptes et Firestore pour le stockage sécurisé. Les images sont traitées temporairement et peuvent être supprimées après analyse. L\\'historique est lié à votre compte utilisateur.',\n    'doc.faq.speed.question': '⚡ Quelle est la vitesse de traitement ?',\n    'doc.faq.speed.answer': 'Le traitement prend généralement 15-30 secondes selon la complexité du diagramme. Les diagrammes simples (2-5 classes) sont traités en 10-15 secondes, tandis que les diagrammes complexes (10+ classes) peuvent prendre jusqu\\'à 45 secondes.',\n    // Contact\n    'doc.contact.title': 'Besoin d\\'aide supplémentaire ?',\n    'doc.contact.description': 'Merci d\\'utiliser notre application UML Class Analyzer. Cette documentation vous guide dans l\\'utilisation de toutes les fonctionnalités disponibles.',\n    // Documentation\n    'doc.title': 'Documentation UML Class Analyzer',\n    'doc.subtitle': 'Guide complet pour l\\'utilisation de notre plateforme d\\'analyse UML alimentée par l\\'IA',\n    'doc.version': 'v2.5.3 Enterprise',\n    'doc.overview': 'Vue d\\'ensemble',\n    'doc.features': 'Fonctionnalités',\n    'doc.guide': 'Guide d\\'utilisation',\n    'doc.technologies': 'Technologies',\n    'doc.api': 'API',\n    'doc.faq': 'FAQ',\n    'doc.projectOverview': 'Vue d\\'ensemble du projet',\n    'doc.appTitle': 'UML Class Analyzer - Édition Enterprise v2.5.3',\n    'doc.gettingStarted': 'Commencer',\n    'doc.tutorials': 'Tutoriels',\n    'doc.support': 'Support',\n    'doc.lastUpdate': 'Dernière mise à jour',\n    // Common buttons and actions\n    'common.ok': 'OK',\n    'common.cancel': 'Annuler',\n    'common.save': 'Sauvegarder',\n    'common.delete': 'Supprimer',\n    'common.edit': 'Modifier',\n    'common.close': 'Fermer',\n    'common.back': 'Retour',\n    'common.next': 'Suivant',\n    'common.previous': 'Précédent',\n    'common.loading': 'Chargement...',\n    'common.error': 'Erreur',\n    'common.success': 'Succès',\n    'common.warning': 'Attention',\n    'common.info': 'Information'\n  },\n  en: {\n    // Header\n    'app.title': 'AI UML Class Analyzer',\n    'button.login': 'Login',\n    // Tabs\n    'tab.upload': 'Image Import',\n    'tab.relations': 'Relations Analysis',\n    'tab.text': 'Diagram Extraction',\n    'tab.documentation': 'Documentation',\n    // Content headers\n    'content.upload.title': 'UML Diagram Import',\n    'content.upload.subtitle': 'Upload and analyze your UML class diagrams with our advanced AI',\n    'content.relations.title': 'UML Relations Analysis',\n    'content.relations.subtitle': 'Visualize detected relationships between classes in your diagram',\n    'content.text.title': 'Diagram Extraction',\n    'content.text.subtitle': 'Extract and export UML diagrams',\n    'content.documentation.title': 'Documentation & Resources',\n    'content.documentation.subtitle': 'Discover UML Class Analyzer features',\n    // Footer\n    'footer.text': '© 2025 UML Class Analyzer | Enterprise Edition v2.5.3',\n    // Language button\n    'language.current': 'EN',\n    'language.switch': 'Switch to French',\n    // ImageUploader\n    'imageUploader.dropZone': 'Drag and drop your UML diagram or click to select',\n    'imageUploader.selectFile': 'Select a file',\n    'imageUploader.analyzing': 'Analyzing...',\n    'imageUploader.progress.waiting': 'Waiting for image...',\n    'imageUploader.progress.converting': 'Converting and optimizing image...',\n    'imageUploader.progress.detecting': 'Detecting classes and relationships with our AI model...',\n    'imageUploader.progress.analyzing': 'Analysis in progress... This step may take a few moments.',\n    'imageUploader.progress.fetching': 'Fetching results...',\n    'imageUploader.progress.completed': 'Analysis completed',\n    'imageUploader.progress.error': 'Error during image analysis',\n    'imageUploader.progress.fetchError': 'Error fetching results',\n    'imageUploader.error.processing': 'Error processing image',\n    'imageUploader.error.connection': 'Connection or analysis error',\n    'imageUploader.error.fetchText': 'Unable to fetch extracted text',\n    'imageUploader.error.thumbnail': 'Thumbnail not available',\n    'imageUploader.button.newAnalysis': 'New Analysis',\n    'imageUploader.button.viewRelations': 'View Detected Relations',\n    'imageUploader.button.extractDiagram': 'Extract Diagram',\n    // DetectionArrow\n    'detectionArrow.title': 'UML Relations Detection',\n    'detectionArrow.loading': 'Loading annotated image...',\n    'detectionArrow.error': 'Unable to load annotated image. Please check that the server is running.',\n    'detectionArrow.noImage': 'No annotated image is available. Please first upload and analyze a UML diagram image.',\n    'detectionArrow.imageAlt': 'UML diagram annotated with detected relationships',\n    'detectionArrow.analysisTitle': 'Relations Analysis',\n    'detectionArrow.explanation': 'The image above shows the detected relationships between the different classes in your UML diagram. Our AI system has identified the connections and marked them according to their type.',\n    'detectionArrow.legend.class': 'Class',\n    'detectionArrow.legend.relation': 'Relation',\n    'detectionArrow.legend.aggregation': 'Aggregation',\n    'detectionArrow.legend.composition': 'Composition',\n    'detectionArrow.legend.generalization': 'Generalization',\n    'detectionArrow.legend.association': 'Association',\n    'detectionArrow.tip.title': 'Tip',\n    'detectionArrow.tip.text': 'For more detailed analysis, check the \"UML Text Extraction\" tab which contains complete textual information of all detected classes.',\n    // UMLDiagrameExtractor\n    'umlExtractor.title': 'UML Diagram Extractor',\n    'umlExtractor.format.diagram': 'Diagram Code',\n    'umlExtractor.format.java': 'Java Code',\n    'umlExtractor.format.python': 'Python Code',\n    'umlExtractor.placeholder.diagram': 'Enter your diagram code here...',\n    'umlExtractor.placeholder.java': 'Enter your Java code here...',\n    'umlExtractor.placeholder.python': 'Enter your Python code here...',\n    'umlExtractor.preview.empty': 'Enter your {format} code to visualize it here',\n    'umlExtractor.error.syntax': 'Syntax error:',\n    'umlExtractor.button.copy': 'Copy',\n    'umlExtractor.button.export': 'Export',\n    'umlExtractor.button.fullscreen': 'Fullscreen',\n    'umlExtractor.button.zoomIn': 'Zoom In',\n    'umlExtractor.button.zoomOut': 'Zoom Out',\n    'umlExtractor.button.resetZoom': 'Reset Zoom',\n    'umlExtractor.copied': 'Copied!',\n    'umlExtractor.editor.title': 'Code Editor',\n    'umlExtractor.export.svg': 'Export SVG',\n    'umlExtractor.export.png': 'Export PNG',\n    'umlExtractor.modal.confirmTitle': 'Confirm Deletion',\n    'umlExtractor.modal.confirmMessage': 'Are you sure you want to clear all editor content? This action is irreversible.',\n    'umlExtractor.modal.cancel': 'Cancel',\n    'umlExtractor.modal.clearContent': 'Clear Content',\n    // Authentication\n    'auth.login.title': 'Log in to your account',\n    'auth.signup.title': 'Create an account',\n    'auth.field.name': 'Name',\n    'auth.field.email': 'Email',\n    'auth.field.password': 'Password',\n    'auth.field.confirmPassword': 'Confirm Password',\n    'auth.placeholder.name': 'Your name',\n    'auth.placeholder.email': '<EMAIL>',\n    'auth.placeholder.password': 'Create a password',\n    'auth.placeholder.confirmPassword': 'Confirm your password',\n    'auth.rememberMe': 'Remember me',\n    'auth.forgotPassword': 'Forgot password?',\n    'auth.acceptTerms': 'I accept the Terms of Service',\n    'auth.button.login': 'Log in',\n    'auth.button.signup': 'Sign up',\n    'auth.button.createAccount': 'Create account',\n    'auth.button.loggingIn': 'Logging in...',\n    'auth.button.creatingAccount': 'Creating account...',\n    'auth.button.continueWithGoogle': 'Continue with Google',\n    'auth.button.logout': 'Log out',\n    'auth.or': 'OR',\n    'auth.noAccount': 'Don\\'t have an account?',\n    'auth.haveAccount': 'Already have an account?',\n    'auth.error.emailRequired': 'Email is required',\n    'auth.error.emailInvalid': 'Please enter a valid email',\n    'auth.error.passwordRequired': 'Password is required',\n    'auth.error.passwordTooShort': 'Password must be at least 6 characters',\n    'auth.error.nameRequired': 'Name is required',\n    'auth.error.confirmPasswordRequired': 'Please confirm your password',\n    'auth.error.passwordMismatch': 'Passwords do not match',\n    'auth.error.authFailed': 'Authentication failed',\n    'auth.error.invalidCredentials': 'Invalid email or password',\n    'auth.error.emailInUse': 'Email is already in use',\n    'auth.error.weakPassword': 'Password is too weak',\n    'auth.error.tooManyRequests': 'Too many failed attempts. Try again later.',\n    'auth.error.googleLoginFailed': 'Google login failed. Please try again.',\n    // HistorySidebar\n    'history.title': 'UML History',\n    'history.newProject': 'New Project',\n    'history.empty': 'No history',\n    'history.loading': 'Loading...',\n    'history.error': 'Error loading history',\n    'history.deleteConfirm': 'Are you sure you want to delete this item?',\n    'history.deleteAllConfirm': 'Are you sure you want to delete all your history? This action is irreversible.',\n    'history.delete': 'Delete',\n    'history.deleteAll': 'Delete all history',\n    'history.cancel': 'Cancel',\n    'history.loginPrompt': 'Log in to see your history',\n    'history.searchPlaceholder': 'Search...',\n    'history.noResults': 'No results found',\n    'history.thumbnail': 'Thumbnail',\n    'history.reload': 'Reload',\n    // History Analysis Section\n    'history.import.buttonDiagrams': 'Import ({count} diagram{plural} selected)',\n    'history.import.buttonElements': 'Import ({count} element{plural} selected)',\n    'history.preview.button': 'View preview',\n    'history.preview.title': 'Preview of class \"{className}\"',\n    'history.preview.classContent': 'Class content',\n    'history.preview.relatedClasses': 'Related classes',\n    'history.preview.noRelatedClasses': 'No related classes detected',\n    'history.granular.selectElements': 'Select elements to import',\n    'history.granular.checkAll': 'Check all',\n    'history.granular.attributes': 'Attributes',\n    'history.granular.methods': 'Methods',\n    // Analysis Results\n    'analysis.error.updateFailed': 'Failed to update text. Please try again.',\n    'analysis.diagramAlt': 'UML Diagram',\n    'analysis.tooltip.viewAnnotated': 'Click to view annotated image',\n    'analysis.tooltip.convertDiagram': 'If your diagram is handwritten and you want to convert it to a printed diagram, click here',\n    'analysis.imageAnalyzed': 'Analyzed Image',\n    'analysis.extractedText': 'Extracted Text',\n    'analysis.copied': 'Copied!',\n    'analysis.newProject': 'New Project',\n    // Conflict Resolution Modal\n    'conflict.title': 'Conflict Resolution',\n    'conflict.attribute': 'Attribute',\n    'conflict.method': 'Method',\n    'conflict.currentVersion': 'Current Version',\n    'conflict.importedVersions': 'Imported Versions',\n    'conflict.keepCurrent': 'Keep Current',\n    'conflict.replace': 'Replace',\n    'conflict.merge': 'Merge',\n    'conflict.cancel': 'Cancel',\n    'conflict.applyResolutions': 'Apply Resolutions',\n    // Entity Popup\n    'entity.subtitle': 'UML AI suggests several possible choices for this entity.',\n    'entity.attributes': 'Attributes',\n    'entity.methods': 'Methods',\n    'entity.noAdditionalAttributes': 'No additional attributes',\n    'entity.noAdditionalMethods': 'No additional methods',\n    'entity.modifyEntity': 'Modify Entity',\n    'entity.cancel': 'Cancel',\n    // History Analysis Section\n    'historyAnalysis.title': 'Historical Analysis',\n    'historyAnalysis.noResults': 'No historical diagrams found for class \"{className}\"',\n    'historyAnalysis.sortBy': 'Sort by:',\n    'historyAnalysis.conflictsDetected': 'Conflicts detected: {attributes} attribute(s), {methods} method(s)',\n    'historyAnalysis.access.owner': 'Your diagram',\n    'historyAnalysis.access.shared': 'Shared diagram',\n    'historyAnalysis.access.public': 'Public diagram',\n    'historyAnalysis.access.restricted': 'Restricted access',\n    // Documentation\n    'doc.description': 'UML Class Analyzer is an advanced web application that uses artificial intelligence to automatically analyze UML class diagrams. Our solution combines the power of models to offer exceptional precision in UML element detection and extraction.',\n    'doc.features.aiDetection.title': 'Advanced AI Detection',\n    'doc.features.aiDetection.description': 'Uses models specifically trained to recognize UML elements with over 95% accuracy.',\n    'doc.features.fastProcessing.title': 'Fast Processing',\n    'doc.features.fastProcessing.description': 'Complete analysis of a UML diagram in less than 30 seconds thanks to our optimized infrastructure.',\n    'doc.features.multiFormat.title': 'Multi-format',\n    'doc.features.multiFormat.description': 'Complete support for PNG, JPG, JPEG and PDF formats with export currently to Java and Mermaid.',\n    'doc.featuresTitle': 'Main Features',\n    'doc.features.uploadAnalyze.title': 'Upload & Analyze',\n    'doc.features.uploadAnalyze.description': 'Upload your UML diagrams (PNG, JPG, PDF) and get complete analysis in seconds with our specialized AI.',\n    'doc.features.relationDetection.title': 'Relation Detection',\n    'doc.features.relationDetection.description': 'Automatic identification of UML relations: inheritance, composition, aggregation, association with annotated visualization.',\n    'doc.features.textExtraction.title': 'Text Extraction',\n    'doc.features.textExtraction.description': 'Intelligent extraction of classes, attributes and methods with automatic organization and multi-format export.',\n    'doc.features.codeGeneration.title': 'Code Generation',\n    'doc.features.codeGeneration.description': 'Automatic conversion to Java and Mermaid diagrams with complete class structure.',\n    'doc.features.historyManagement.title': 'History & Management',\n    'doc.features.historyManagement.description': 'Automatic analysis backup, complete history with Firebase authentication and project management.',\n    'doc.features.modernInterface.title': 'Modern Interface',\n    'doc.features.modernInterface.description': 'Intuitive user interface with dark/light mode, responsive design and optimized user experience.',\n    // Usage Guide\n    'doc.guideTitle': 'Step-by-step Usage Guide',\n    'doc.guide.step1.title': 'Diagram Upload',\n    'doc.guide.step1.description': 'Click \"Select a file\" or drag and drop your UML diagram. Supported formats: PNG, JPG, JPEG, PDF. Maximum size: 50MB.',\n    'doc.guide.step2.title': 'Automatic Analysis',\n    'doc.guide.step2.description': 'Our AI analyzes your diagram using multiple models for good results. The process typically takes 15-30 seconds depending on complexity.',\n    'doc.guide.step3.title': 'Relationship Visualization',\n    'doc.guide.step3.description': 'Check the \"Relationship Analysis\" tab to see your annotated diagram with all detected relationships (inheritance, composition, aggregation, etc.).',\n    'doc.guide.step4.title': 'Export and Code Generation',\n    'doc.guide.step4.description': 'In the \"Diagram Extraction\" tab, export your results to Java or Mermaid. Customize the generated code according to your needs.',\n    // Technologies\n    'doc.techTitle': 'Technology Stack',\n    'doc.tech.frontend.title': 'Frontend',\n    'doc.tech.frontend.description': 'React 18, TypeScript, CSS-in-JS',\n    'doc.tech.backend.title': 'Backend',\n    'doc.tech.backend.description': 'FastAPI, Python, Uvicorn',\n    'doc.tech.ai.title': 'AI & Vision',\n    'doc.tech.ai.description': 'Confidential',\n    'doc.tech.database.title': 'Database',\n    'doc.tech.database.description': 'Firebase Firestore, Authentication',\n    'doc.tech.visualization.title': 'Visualization',\n    'doc.tech.visualization.description': 'Mermaid.js, SVG, Canvas API',\n    // API\n    'doc.apiTitle': 'API Documentation',\n    'doc.api.endpointsTitle': 'Main Endpoints',\n    'doc.api.detect.description': 'Main endpoint for UML diagram analysis. Accepts image files and returns complete analysis with relationship detection.',\n    'doc.api.annotated.description': 'Retrieves the annotated image with detected relationships visualized.',\n    'doc.api.results.description': 'Downloads the text file containing complete extraction of classes, attributes and methods.',\n    // FAQ\n    'doc.faqTitle': 'Frequently Asked Questions',\n    'doc.faq.formats.question': '🖼️ What image formats are supported?',\n    'doc.faq.formats.answer': 'Our application supports PNG, JPG, JPEG and PDF formats. For best results, use high resolution images (minimum 800x600) and well-lit. Maximum allowed size is 50MB.',\n    'doc.faq.offline.question': '🌐 Does the application work offline?',\n    'doc.faq.offline.answer': 'No, our application requires an active Internet connection as it uses AI models hosted on our servers to analyze your diagrams. The interface can be cached but analysis requires a connection.',\n    'doc.faq.codeGeneration.question': '💻 Can I generate code from my UML diagram?',\n    'doc.faq.codeGeneration.answer': 'Yes! Our code generation feature allows you to transform your UML diagrams into Java code or Mermaid diagrams. The generated code includes detected classes, attributes, methods and relationships.',\n    'doc.faq.accuracy.question': '🎯 How to improve detection accuracy?',\n    'doc.faq.accuracy.answer': 'To optimize results: use well-contrasted diagrams, ensure texts are readable (minimum 12pt font), avoid blurry images, use distinct colors for different elements, and verify that relationship lines are clearly visible.',\n    'doc.faq.security.question': '🔒 Is my data secure?',\n    'doc.faq.security.answer': 'Yes, we use Firebase Authentication for account security and Firestore for secure storage. Images are processed temporarily and can be deleted after analysis. History is linked to your user account.',\n    'doc.faq.speed.question': '⚡ What is the processing speed?',\n    'doc.faq.speed.answer': 'Processing typically takes 15-30 seconds depending on diagram complexity. Simple diagrams (2-5 classes) are processed in 10-15 seconds, while complex diagrams (10+ classes) can take up to 45 seconds.',\n    // Contact\n    'doc.contact.title': 'Need additional help?',\n    'doc.contact.description': 'Thank you for using our UML Class Analyzer application. This documentation guides you through using all available features.',\n    // Documentation\n    'doc.title': 'UML Class Analyzer Documentation',\n    'doc.subtitle': 'Complete guide for using our AI-powered UML analysis platform',\n    'doc.version': 'v2.5.3 Enterprise',\n    'doc.overview': 'Overview',\n    'doc.features': 'Features',\n    'doc.guide': 'User Guide',\n    'doc.technologies': 'Technologies',\n    'doc.api': 'API',\n    'doc.faq': 'FAQ',\n    'doc.projectOverview': 'Project Overview',\n    'doc.appTitle': 'UML Class Analyzer - Enterprise Edition v2.5.3',\n    'doc.gettingStarted': 'Getting Started',\n    'doc.tutorials': 'Tutorials',\n    'doc.support': 'Support',\n    'doc.lastUpdate': 'Last Update',\n    // Common buttons and actions\n    'common.ok': 'OK',\n    'common.cancel': 'Cancel',\n    'common.save': 'Save',\n    'common.delete': 'Delete',\n    'common.edit': 'Edit',\n    'common.close': 'Close',\n    'common.back': 'Back',\n    'common.next': 'Next',\n    'common.previous': 'Previous',\n    'common.loading': 'Loading...',\n    'common.error': 'Error',\n    'common.success': 'Success',\n    'common.warning': 'Warning',\n    'common.info': 'Information'\n  }\n};\n\n// Création du contexte\nconst LanguageContext = /*#__PURE__*/createContext(undefined);\n\n// Provider du contexte\n\nexport const LanguageProvider = ({\n  children\n}) => {\n  _s();\n  const [language, setLanguageState] = useState('fr');\n\n  // Charger la langue depuis le localStorage au démarrage\n  useEffect(() => {\n    const savedLanguage = localStorage.getItem('app-language');\n    if (savedLanguage && (savedLanguage === 'fr' || savedLanguage === 'en')) {\n      setLanguageState(savedLanguage);\n    }\n  }, []);\n\n  // Sauvegarder la langue dans le localStorage\n  const setLanguage = lang => {\n    setLanguageState(lang);\n    localStorage.setItem('app-language', lang);\n  };\n\n  // Fonction de traduction\n  const t = key => {\n    return translations[language][key] || key;\n  };\n  const value = {\n    language,\n    setLanguage,\n    t\n  };\n  return /*#__PURE__*/_jsxDEV(LanguageContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 671,\n    columnNumber: 5\n  }, this);\n};\n\n// Hook pour utiliser le contexte\n_s(LanguageProvider, \"zJ9zF9Hb+G/qZ3+OQ94tpM7AV7o=\");\n_c = LanguageProvider;\nexport const useLanguage = () => {\n  _s2();\n  const context = useContext(LanguageContext);\n  if (!context) {\n    throw new Error('useLanguage must be used within a LanguageProvider');\n  }\n  return context;\n};\n_s2(useLanguage, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"LanguageProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "jsxDEV", "_jsxDEV", "translations", "fr", "en", "LanguageContext", "undefined", "LanguageProvider", "children", "_s", "language", "setLanguageState", "savedLanguage", "localStorage", "getItem", "setLanguage", "lang", "setItem", "t", "key", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useLanguage", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["C:/Users/<USER>/FixTorchUMLDGM/uml-detector/src/context/LanguageContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\n\n// Types pour les langues supportées\nexport type Language = 'fr' | 'en';\n\n// Interface pour le contexte de langue\ninterface LanguageContextType {\n  language: Language;\n  setLanguage: (lang: Language) => void;\n  t: (key: string) => string;\n}\n\n// Traductions\nconst translations = {\n  fr: {\n    // Header\n    'app.title': 'AI UML Class Analyzer',\n    'button.login': 'Connexion',\n\n    // Tabs\n    'tab.upload': 'Import d\\'image',\n    'tab.relations': 'Analyse de relations',\n    'tab.text': 'Extraction de diagramme',\n    'tab.documentation': 'Documentation',\n\n    // Content headers\n    'content.upload.title': 'Import de diagramme UML',\n    'content.upload.subtitle': 'Téléchargez et analysez vos diagrammes de classes UML avec notre IA avancée',\n    'content.relations.title': 'Analyse de relations UML',\n    'content.relations.subtitle': 'Visualisez les relations détectées entre les classes de votre diagramme',\n    'content.text.title': 'Extraction de diagramme',\n    'content.text.subtitle': 'Extrayez et exportez des diagrammes UML',\n    'content.documentation.title': 'Documentation & Ressources',\n    'content.documentation.subtitle': 'Découvrez les fonctionnalités de UML Class Analyzer',\n\n    // Footer\n    'footer.text': '© 2025 UML Class Analyzer | Édition Enterprise v2.5.3',\n\n    // Language button\n    'language.current': 'FR',\n    'language.switch': 'Passer en anglais',\n\n    // ImageUploader\n    'imageUploader.dropZone': 'Glissez-déposez votre diagramme UML ou cliquez pour sélectionner',\n    'imageUploader.selectFile': 'Sélectionner un fichier',\n    'imageUploader.analyzing': 'Analyse en cours...',\n    'imageUploader.progress.waiting': 'En attente de l\\'image...',\n    'imageUploader.progress.converting': 'Conversion et optimisation de l\\'image...',\n    'imageUploader.progress.detecting': 'Détection des classes et relations avec notre modèle IA...',\n    'imageUploader.progress.analyzing': 'Analyse en cours ... Cette étape peut prendre quelques instants.',\n    'imageUploader.progress.fetching': 'Récupération des résultats...',\n    'imageUploader.progress.completed': 'Analyse terminée',\n    'imageUploader.progress.error': 'Erreur pendant l\\'analyse de l\\'image',\n    'imageUploader.progress.fetchError': 'Erreur lors de la récupération des résultats',\n    'imageUploader.error.processing': 'Erreur lors du traitement de l\\'image',\n    'imageUploader.error.connection': 'Erreur de connexion ou analyse',\n    'imageUploader.error.fetchText': 'Impossible de récupérer le texte extrait',\n    'imageUploader.error.thumbnail': 'Miniature non disponible',\n    'imageUploader.button.newAnalysis': 'Nouvelle analyse',\n    'imageUploader.button.viewRelations': 'Voir les relations détectées',\n    'imageUploader.button.extractDiagram': 'Extraire le diagramme',\n\n    // DetectionArrow\n    'detectionArrow.title': 'Détection de Relations UML',\n    'detectionArrow.loading': 'Chargement de l\\'image annotée...',\n    'detectionArrow.error': 'Impossible de charger l\\'image annotée. Veuillez vérifier que le serveur est en cours d\\'exécution.',\n    'detectionArrow.noImage': 'Aucune image annotée n\\'est disponible. Veuillez d\\'abord télécharger et analyser une image de diagramme UML.',\n    'detectionArrow.imageAlt': 'Diagramme UML annoté avec relations détectées',\n    'detectionArrow.analysisTitle': 'Analyse des Relations',\n    'detectionArrow.explanation': 'L\\'image ci-dessus montre les relations détectées entre les différentes classes de votre diagramme UML. Notre système d\\'IA a identifié les connexions et les a marquées selon leur type.',\n    'detectionArrow.legend.class': 'Classe',\n    'detectionArrow.legend.relation': 'Relation',\n    'detectionArrow.legend.aggregation': 'Agrégation',\n    'detectionArrow.legend.composition': 'Composition',\n    'detectionArrow.legend.generalization': 'Généralisation',\n    'detectionArrow.legend.association': 'Association',\n    'detectionArrow.tip.title': 'Astuce',\n    'detectionArrow.tip.text': 'Pour une analyse plus détaillée, consultez l\\'onglet \"Extraction de texte UML\" qui contient les informations textuelles complètes de toutes les classes détectées.',\n\n    // UMLDiagrameExtractor\n    'umlExtractor.title': 'Extracteur de Diagramme UML',\n    'umlExtractor.format.diagram': 'Code de diagramme',\n    'umlExtractor.format.java': 'Code Java',\n    'umlExtractor.format.python': 'Code Python',\n    'umlExtractor.placeholder.diagram': 'Entrez votre code diagramme ici...',\n    'umlExtractor.placeholder.java': 'Entrez votre code Java ici...',\n    'umlExtractor.placeholder.python': 'Entrez votre code Python ici...',\n    'umlExtractor.preview.empty': 'Entrez votre code {format} pour le visualiser ici',\n    'umlExtractor.error.syntax': 'Erreur de syntaxe:',\n    'umlExtractor.button.copy': 'Copier',\n    'umlExtractor.button.export': 'Exporter',\n    'umlExtractor.button.fullscreen': 'Plein écran',\n    'umlExtractor.button.zoomIn': 'Zoom +',\n    'umlExtractor.button.zoomOut': 'Zoom -',\n    'umlExtractor.button.resetZoom': 'Réinitialiser zoom',\n    'umlExtractor.copied': 'Copié !',\n    'umlExtractor.editor.title': 'Éditeur de code',\n    'umlExtractor.export.svg': 'Exporter SVG',\n    'umlExtractor.export.png': 'Exporter PNG',\n    'umlExtractor.modal.confirmTitle': 'Confirmer la suppression',\n    'umlExtractor.modal.confirmMessage': 'Êtes-vous sûr de vouloir effacer tout le contenu de l\\'éditeur ? Cette action est irréversible.',\n    'umlExtractor.modal.cancel': 'Annuler',\n    'umlExtractor.modal.clearContent': 'Effacer le contenu',\n\n    // Authentication\n    'auth.login.title': 'Connexion à votre compte',\n    'auth.signup.title': 'Créer un compte',\n    'auth.field.name': 'Nom',\n    'auth.field.email': 'Email',\n    'auth.field.password': 'Mot de passe',\n    'auth.field.confirmPassword': 'Confirmer le mot de passe',\n    'auth.placeholder.name': 'Votre nom',\n    'auth.placeholder.email': '<EMAIL>',\n    'auth.placeholder.password': 'Créer un mot de passe',\n    'auth.placeholder.confirmPassword': 'Confirmez votre mot de passe',\n    'auth.rememberMe': 'Se souvenir de moi',\n    'auth.forgotPassword': 'Mot de passe oublié ?',\n    'auth.acceptTerms': 'J\\'accepte les conditions d\\'utilisation',\n    'auth.button.login': 'Se connecter',\n    'auth.button.signup': 'S\\'inscrire',\n    'auth.button.createAccount': 'Créer un compte',\n    'auth.button.loggingIn': 'Connexion...',\n    'auth.button.creatingAccount': 'Création du compte...',\n    'auth.button.continueWithGoogle': 'Continuer avec Google',\n    'auth.button.logout': 'Se déconnecter',\n    'auth.or': 'OU',\n    'auth.noAccount': 'Vous n\\'avez pas de compte ?',\n    'auth.haveAccount': 'Vous avez déjà un compte ?',\n    'auth.error.emailRequired': 'L\\'email est requis',\n    'auth.error.emailInvalid': 'Veuillez entrer un email valide',\n    'auth.error.passwordRequired': 'Le mot de passe est requis',\n    'auth.error.passwordTooShort': 'Le mot de passe doit contenir au moins 6 caractères',\n    'auth.error.nameRequired': 'Le nom est requis',\n    'auth.error.confirmPasswordRequired': 'Veuillez confirmer votre mot de passe',\n    'auth.error.passwordMismatch': 'Les mots de passe ne correspondent pas',\n    'auth.error.authFailed': 'Échec de l\\'authentification',\n    'auth.error.invalidCredentials': 'Email ou mot de passe invalide',\n    'auth.error.emailInUse': 'Cet email est déjà utilisé',\n    'auth.error.weakPassword': 'Le mot de passe est trop faible',\n    'auth.error.tooManyRequests': 'Trop de tentatives échouées. Réessayez plus tard.',\n    'auth.error.googleLoginFailed': 'Connexion Google échouée. Veuillez réessayer.',\n\n    // HistorySidebar\n    'history.title': 'Historique UML',\n    'history.newProject': 'Nouveau projet',\n    'history.empty': 'Aucun historique',\n    'history.loading': 'Chargement...',\n    'history.error': 'Erreur lors du chargement de l\\'historique',\n    'history.deleteConfirm': 'Êtes-vous sûr de vouloir supprimer cet élément ?',\n    'history.deleteAllConfirm': 'Êtes-vous sûr de vouloir supprimer tout votre historique ? Cette action est irréversible.',\n    'history.delete': 'Supprimer',\n    'history.deleteAll': 'Supprimer tout l\\'historique',\n    'history.cancel': 'Annuler',\n    'history.loginPrompt': 'Connectez-vous pour voir votre historique',\n    'history.searchPlaceholder': 'Rechercher...',\n    'history.noResults': 'Aucun résultat trouvé',\n    'history.thumbnail': 'Miniature',\n    'history.reload': 'Recharger',\n    'history.shared': 'Partagé',\n\n    // History Analysis Section\n    'history.import.buttonDiagrams': 'Importer ({count} diagramme{plural} sélectionné{plural})',\n    'history.import.buttonElements': 'Importer ({count} élément{plural} sélectionné{plural})',\n    'history.preview.button': 'Voir aperçu',\n    'history.preview.title': 'Aperçu de la classe \"{className}\"',\n    'history.preview.classContent': 'Contenu de la classe',\n    'history.preview.relatedClasses': 'Classes liées',\n    'history.preview.noRelatedClasses': 'Aucune classe liée détectée',\n    'history.granular.selectElements': 'Sélectionner les éléments à importer',\n    'history.granular.checkAll': 'Cocher tous',\n    'history.granular.attributes': 'Attributs',\n    'history.granular.methods': 'Méthodes',\n\n    // Analysis Results\n    'analysis.error.updateFailed': 'Échec de la mise à jour du texte. Veuillez réessayer.',\n    'analysis.diagramAlt': 'Diagramme UML',\n    'analysis.tooltip.viewAnnotated': 'Cliquez pour voir l\\'image annotée',\n    'analysis.tooltip.convertDiagram': 'Si votre diagramme est manuscrit et vous souhaitez le convertir vers un diagramme imprimé, cliquez ici',\n    'analysis.imageAnalyzed': 'Image analysée',\n    'analysis.extractedText': 'Texte extrait',\n    'analysis.copied': 'Copié!',\n    'analysis.newProject': 'Nouveau Projet',\n\n    // Conflict Resolution Modal\n    'conflict.title': 'Résolution des Conflits',\n    'conflict.attribute': 'Attribut',\n    'conflict.method': 'Méthode',\n    'conflict.currentVersion': 'Version Actuelle',\n    'conflict.importedVersions': 'Versions Importées',\n    'conflict.keepCurrent': 'Garder l\\'actuel',\n    'conflict.replace': 'Remplacer',\n    'conflict.merge': 'Fusionner',\n    'conflict.cancel': 'Annuler',\n    'conflict.applyResolutions': 'Appliquer les Résolutions',\n\n    // Entity Popup\n    'entity.subtitle': 'UML IA propose plusieurs choix possibles pour cette entité.',\n    'entity.attributes': 'Attributs',\n    'entity.methods': 'Méthodes',\n    'entity.noAdditionalAttributes': 'Aucun attribut supplémentaire',\n    'entity.noAdditionalMethods': 'Aucune méthode supplémentaire',\n    'entity.modifyEntity': 'Modifier l\\'entité',\n    'entity.cancel': 'Annuler',\n\n    // History Analysis Section\n    'historyAnalysis.title': 'Analyse Historique',\n    'historyAnalysis.noResults': 'Aucun diagramme historique trouvé pour la classe \"{className}\"',\n    'historyAnalysis.sortBy': 'Trier par:',\n    'historyAnalysis.conflictsDetected': 'Conflits détectés: {attributes} attribut(s), {methods} méthode(s)',\n    'historyAnalysis.access.owner': 'Votre diagramme',\n    'historyAnalysis.access.shared': 'Diagramme partagé',\n    'historyAnalysis.access.public': 'Diagramme public',\n    'historyAnalysis.access.restricted': 'Accès restreint',\n\n    // Documentation\n    'doc.description': 'UML Class Analyzer est une application web avancée qui utilise l\\'intelligence artificielle pour analyser automatiquement les diagrammes de classes UML. Notre solution combine la puissance des modèles pour offrir une précision exceptionnelle dans la détection et l\\'extraction d\\'éléments UML.',\n    'doc.features.aiDetection.title': 'Détection IA Avancée',\n    'doc.features.aiDetection.description': 'Utilise des modèles entraînés spécifiquement pour reconnaître les éléments UML avec une précision de plus de 95%.',\n    'doc.features.fastProcessing.title': 'Traitement Rapide',\n    'doc.features.fastProcessing.description': 'Analyse complète d\\'un diagramme UML en moins de 30 secondes grâce à notre infrastructure optimisée.',\n    'doc.features.multiFormat.title': 'Multi-format',\n    'doc.features.multiFormat.description': 'Support complet des formats PNG, JPG, JPEG et PDF avec export pour le moment vers Java et Mermaid.',\n    'doc.featuresTitle': 'Fonctionnalités principales',\n    'doc.features.uploadAnalyze.title': 'Upload & Analyse',\n    'doc.features.uploadAnalyze.description': 'Téléchargez vos diagrammes UML (PNG, JPG, PDF) et obtenez une analyse complète en quelques secondes avec notre IA spécialisée.',\n    'doc.features.relationDetection.title': 'Détection de Relations',\n    'doc.features.relationDetection.description': 'Identification automatique des relations UML : héritage, composition, agrégation, association avec visualisation annotée.',\n    'doc.features.textExtraction.title': 'Extraction de Texte',\n    'doc.features.textExtraction.description': 'Extraction intelligente des classes, attributs et méthodes avec organisation automatique et export multi-format.',\n    'doc.features.codeGeneration.title': 'Génération de Code',\n    'doc.features.codeGeneration.description': 'Conversion automatique vers Java et diagrammes Mermaid avec structure de classes complète.',\n    'doc.features.historyManagement.title': 'Historique & Gestion',\n    'doc.features.historyManagement.description': 'Sauvegarde automatique des analyses, historique complet avec authentification Firebase et gestion des projets.',\n    'doc.features.modernInterface.title': 'Interface Moderne',\n    'doc.features.modernInterface.description': 'Interface utilisateur intuitive avec mode sombre/clair, design responsive et expérience utilisateur optimisée.',\n\n    // Guide d'utilisation\n    'doc.guideTitle': 'Guide d\\'utilisation étape par étape',\n    'doc.guide.step1.title': 'Téléchargement du diagramme',\n    'doc.guide.step1.description': 'Cliquez sur \"Sélectionner un fichier\" ou glissez-déposez votre diagramme UML. Formats supportés : PNG, JPG, JPEG, PDF. Taille maximale : 50MB.',\n    'doc.guide.step2.title': 'Analyse automatique',\n    'doc.guide.step2.description': 'Notre IA analyse votre diagramme en utilisant plusieurs modèles pour de bons résultats. Le processus prend généralement 15-30 secondes selon la complexité.',\n    'doc.guide.step3.title': 'Visualisation des relations',\n    'doc.guide.step3.description': 'Consultez l\\'onglet \"Analyse de relations\" pour voir votre diagramme annoté avec toutes les relations détectées (héritage, composition, agrégation, etc.).',\n    'doc.guide.step4.title': 'Export et génération de code',\n    'doc.guide.step4.description': 'Dans l\\'onglet \"Extraction de diagramme\", exportez vos résultats en Java ou Mermaid. Personnalisez le code généré selon vos besoins.',\n\n    // Technologies\n    'doc.techTitle': 'Stack technologique',\n    'doc.tech.frontend.title': 'Frontend',\n    'doc.tech.frontend.description': 'React 18, TypeScript, CSS-in-JS',\n    'doc.tech.backend.title': 'Backend',\n    'doc.tech.backend.description': 'FastAPI, Python, Uvicorn',\n    'doc.tech.ai.title': 'IA & Vision',\n    'doc.tech.ai.description': 'Confidentiel',\n    'doc.tech.database.title': 'Base de données',\n    'doc.tech.database.description': 'Firebase Firestore, Authentication',\n    'doc.tech.visualization.title': 'Visualisation',\n    'doc.tech.visualization.description': 'Mermaid.js, SVG, Canvas API',\n\n    // API\n    'doc.apiTitle': 'Documentation API',\n    'doc.api.endpointsTitle': 'Endpoints principaux',\n    'doc.api.detect.description': 'Endpoint principal pour l\\'analyse de diagrammes UML. Accepte les fichiers image et retourne l\\'analyse complète avec détection de relations.',\n    'doc.api.annotated.description': 'Récupère l\\'image annotée avec les relations détectées visualisées.',\n    'doc.api.results.description': 'Télécharge le fichier texte contenant l\\'extraction complète des classes, attributs et méthodes.',\n\n    // FAQ\n    'doc.faqTitle': 'Questions fréquemment posées',\n    'doc.faq.formats.question': '🖼️ Quels formats d\\'image sont supportés ?',\n    'doc.faq.formats.answer': 'Notre application supporte les formats PNG, JPG, JPEG et PDF. Pour de meilleurs résultats, utilisez des images de haute résolution (minimum 800x600) et bien éclairées. La taille maximale autorisée est de 50MB.',\n    'doc.faq.offline.question': '🌐 L\\'application fonctionne-t-elle hors ligne ?',\n    'doc.faq.offline.answer': 'Non, notre application nécessite une connexion Internet active car elle utilise des modèles d\\'IA hébergés sur nos serveurs pour analyser vos diagrammes. L\\'interface peut être mise en cache mais l\\'analyse nécessite une connexion.',\n    'doc.faq.codeGeneration.question': '💻 Puis-je générer du code à partir de mon diagramme UML ?',\n    'doc.faq.codeGeneration.answer': 'Oui ! Notre fonctionnalité de génération de code vous permet de transformer vos diagrammes UML en code Java ou en diagrammes Mermaid. Le code généré inclut les classes, attributs, méthodes et relations détectées.',\n    'doc.faq.accuracy.question': '🎯 Comment améliorer la précision de détection ?',\n    'doc.faq.accuracy.answer': 'Pour optimiser les résultats : utilisez des diagrammes bien contrastés, assurez-vous que les textes sont lisibles (police minimum 12pt), évitez les images floues, utilisez des couleurs distinctes pour les différents éléments, et vérifiez que les lignes de relation sont clairement visibles.',\n    'doc.faq.security.question': '🔒 Mes données sont-elles sécurisées ?',\n    'doc.faq.security.answer': 'Oui, nous utilisons Firebase Authentication pour la sécurité des comptes et Firestore pour le stockage sécurisé. Les images sont traitées temporairement et peuvent être supprimées après analyse. L\\'historique est lié à votre compte utilisateur.',\n    'doc.faq.speed.question': '⚡ Quelle est la vitesse de traitement ?',\n    'doc.faq.speed.answer': 'Le traitement prend généralement 15-30 secondes selon la complexité du diagramme. Les diagrammes simples (2-5 classes) sont traités en 10-15 secondes, tandis que les diagrammes complexes (10+ classes) peuvent prendre jusqu\\'à 45 secondes.',\n\n    // Contact\n    'doc.contact.title': 'Besoin d\\'aide supplémentaire ?',\n    'doc.contact.description': 'Merci d\\'utiliser notre application UML Class Analyzer. Cette documentation vous guide dans l\\'utilisation de toutes les fonctionnalités disponibles.',\n\n\n\n\n\n    // Documentation\n    'doc.title': 'Documentation UML Class Analyzer',\n    'doc.subtitle': 'Guide complet pour l\\'utilisation de notre plateforme d\\'analyse UML alimentée par l\\'IA',\n    'doc.version': 'v2.5.3 Enterprise',\n    'doc.overview': 'Vue d\\'ensemble',\n    'doc.features': 'Fonctionnalités',\n    'doc.guide': 'Guide d\\'utilisation',\n    'doc.technologies': 'Technologies',\n    'doc.api': 'API',\n    'doc.faq': 'FAQ',\n    'doc.projectOverview': 'Vue d\\'ensemble du projet',\n    'doc.appTitle': 'UML Class Analyzer - Édition Enterprise v2.5.3',\n    'doc.gettingStarted': 'Commencer',\n    'doc.tutorials': 'Tutoriels',\n    'doc.support': 'Support',\n    'doc.lastUpdate': 'Dernière mise à jour',\n\n    // Common buttons and actions\n    'common.ok': 'OK',\n    'common.cancel': 'Annuler',\n    'common.save': 'Sauvegarder',\n    'common.delete': 'Supprimer',\n    'common.edit': 'Modifier',\n    'common.close': 'Fermer',\n    'common.back': 'Retour',\n    'common.next': 'Suivant',\n    'common.previous': 'Précédent',\n    'common.loading': 'Chargement...',\n    'common.error': 'Erreur',\n    'common.success': 'Succès',\n    'common.warning': 'Attention',\n    'common.info': 'Information',\n  },\n  en: {\n    // Header\n    'app.title': 'AI UML Class Analyzer',\n    'button.login': 'Login',\n\n    // Tabs\n    'tab.upload': 'Image Import',\n    'tab.relations': 'Relations Analysis',\n    'tab.text': 'Diagram Extraction',\n    'tab.documentation': 'Documentation',\n\n    // Content headers\n    'content.upload.title': 'UML Diagram Import',\n    'content.upload.subtitle': 'Upload and analyze your UML class diagrams with our advanced AI',\n    'content.relations.title': 'UML Relations Analysis',\n    'content.relations.subtitle': 'Visualize detected relationships between classes in your diagram',\n    'content.text.title': 'Diagram Extraction',\n    'content.text.subtitle': 'Extract and export UML diagrams',\n    'content.documentation.title': 'Documentation & Resources',\n    'content.documentation.subtitle': 'Discover UML Class Analyzer features',\n\n    // Footer\n    'footer.text': '© 2025 UML Class Analyzer | Enterprise Edition v2.5.3',\n\n    // Language button\n    'language.current': 'EN',\n    'language.switch': 'Switch to French',\n\n    // ImageUploader\n    'imageUploader.dropZone': 'Drag and drop your UML diagram or click to select',\n    'imageUploader.selectFile': 'Select a file',\n    'imageUploader.analyzing': 'Analyzing...',\n    'imageUploader.progress.waiting': 'Waiting for image...',\n    'imageUploader.progress.converting': 'Converting and optimizing image...',\n    'imageUploader.progress.detecting': 'Detecting classes and relationships with our AI model...',\n    'imageUploader.progress.analyzing': 'Analysis in progress... This step may take a few moments.',\n    'imageUploader.progress.fetching': 'Fetching results...',\n    'imageUploader.progress.completed': 'Analysis completed',\n    'imageUploader.progress.error': 'Error during image analysis',\n    'imageUploader.progress.fetchError': 'Error fetching results',\n    'imageUploader.error.processing': 'Error processing image',\n    'imageUploader.error.connection': 'Connection or analysis error',\n    'imageUploader.error.fetchText': 'Unable to fetch extracted text',\n    'imageUploader.error.thumbnail': 'Thumbnail not available',\n    'imageUploader.button.newAnalysis': 'New Analysis',\n    'imageUploader.button.viewRelations': 'View Detected Relations',\n    'imageUploader.button.extractDiagram': 'Extract Diagram',\n\n    // DetectionArrow\n    'detectionArrow.title': 'UML Relations Detection',\n    'detectionArrow.loading': 'Loading annotated image...',\n    'detectionArrow.error': 'Unable to load annotated image. Please check that the server is running.',\n    'detectionArrow.noImage': 'No annotated image is available. Please first upload and analyze a UML diagram image.',\n    'detectionArrow.imageAlt': 'UML diagram annotated with detected relationships',\n    'detectionArrow.analysisTitle': 'Relations Analysis',\n    'detectionArrow.explanation': 'The image above shows the detected relationships between the different classes in your UML diagram. Our AI system has identified the connections and marked them according to their type.',\n    'detectionArrow.legend.class': 'Class',\n    'detectionArrow.legend.relation': 'Relation',\n    'detectionArrow.legend.aggregation': 'Aggregation',\n    'detectionArrow.legend.composition': 'Composition',\n    'detectionArrow.legend.generalization': 'Generalization',\n    'detectionArrow.legend.association': 'Association',\n    'detectionArrow.tip.title': 'Tip',\n    'detectionArrow.tip.text': 'For more detailed analysis, check the \"UML Text Extraction\" tab which contains complete textual information of all detected classes.',\n\n    // UMLDiagrameExtractor\n    'umlExtractor.title': 'UML Diagram Extractor',\n    'umlExtractor.format.diagram': 'Diagram Code',\n    'umlExtractor.format.java': 'Java Code',\n    'umlExtractor.format.python': 'Python Code',\n    'umlExtractor.placeholder.diagram': 'Enter your diagram code here...',\n    'umlExtractor.placeholder.java': 'Enter your Java code here...',\n    'umlExtractor.placeholder.python': 'Enter your Python code here...',\n    'umlExtractor.preview.empty': 'Enter your {format} code to visualize it here',\n    'umlExtractor.error.syntax': 'Syntax error:',\n    'umlExtractor.button.copy': 'Copy',\n    'umlExtractor.button.export': 'Export',\n    'umlExtractor.button.fullscreen': 'Fullscreen',\n    'umlExtractor.button.zoomIn': 'Zoom In',\n    'umlExtractor.button.zoomOut': 'Zoom Out',\n    'umlExtractor.button.resetZoom': 'Reset Zoom',\n    'umlExtractor.copied': 'Copied!',\n    'umlExtractor.editor.title': 'Code Editor',\n    'umlExtractor.export.svg': 'Export SVG',\n    'umlExtractor.export.png': 'Export PNG',\n    'umlExtractor.modal.confirmTitle': 'Confirm Deletion',\n    'umlExtractor.modal.confirmMessage': 'Are you sure you want to clear all editor content? This action is irreversible.',\n    'umlExtractor.modal.cancel': 'Cancel',\n    'umlExtractor.modal.clearContent': 'Clear Content',\n\n    // Authentication\n    'auth.login.title': 'Log in to your account',\n    'auth.signup.title': 'Create an account',\n    'auth.field.name': 'Name',\n    'auth.field.email': 'Email',\n    'auth.field.password': 'Password',\n    'auth.field.confirmPassword': 'Confirm Password',\n    'auth.placeholder.name': 'Your name',\n    'auth.placeholder.email': '<EMAIL>',\n    'auth.placeholder.password': 'Create a password',\n    'auth.placeholder.confirmPassword': 'Confirm your password',\n    'auth.rememberMe': 'Remember me',\n    'auth.forgotPassword': 'Forgot password?',\n    'auth.acceptTerms': 'I accept the Terms of Service',\n    'auth.button.login': 'Log in',\n    'auth.button.signup': 'Sign up',\n    'auth.button.createAccount': 'Create account',\n    'auth.button.loggingIn': 'Logging in...',\n    'auth.button.creatingAccount': 'Creating account...',\n    'auth.button.continueWithGoogle': 'Continue with Google',\n    'auth.button.logout': 'Log out',\n    'auth.or': 'OR',\n    'auth.noAccount': 'Don\\'t have an account?',\n    'auth.haveAccount': 'Already have an account?',\n    'auth.error.emailRequired': 'Email is required',\n    'auth.error.emailInvalid': 'Please enter a valid email',\n    'auth.error.passwordRequired': 'Password is required',\n    'auth.error.passwordTooShort': 'Password must be at least 6 characters',\n    'auth.error.nameRequired': 'Name is required',\n    'auth.error.confirmPasswordRequired': 'Please confirm your password',\n    'auth.error.passwordMismatch': 'Passwords do not match',\n    'auth.error.authFailed': 'Authentication failed',\n    'auth.error.invalidCredentials': 'Invalid email or password',\n    'auth.error.emailInUse': 'Email is already in use',\n    'auth.error.weakPassword': 'Password is too weak',\n    'auth.error.tooManyRequests': 'Too many failed attempts. Try again later.',\n    'auth.error.googleLoginFailed': 'Google login failed. Please try again.',\n\n    // HistorySidebar\n    'history.title': 'UML History',\n    'history.newProject': 'New Project',\n    'history.empty': 'No history',\n    'history.loading': 'Loading...',\n    'history.error': 'Error loading history',\n    'history.deleteConfirm': 'Are you sure you want to delete this item?',\n    'history.deleteAllConfirm': 'Are you sure you want to delete all your history? This action is irreversible.',\n    'history.delete': 'Delete',\n    'history.deleteAll': 'Delete all history',\n    'history.cancel': 'Cancel',\n    'history.loginPrompt': 'Log in to see your history',\n    'history.searchPlaceholder': 'Search...',\n    'history.noResults': 'No results found',\n    'history.thumbnail': 'Thumbnail',\n    'history.reload': 'Reload',\n\n    // History Analysis Section\n    'history.import.buttonDiagrams': 'Import ({count} diagram{plural} selected)',\n    'history.import.buttonElements': 'Import ({count} element{plural} selected)',\n    'history.preview.button': 'View preview',\n    'history.preview.title': 'Preview of class \"{className}\"',\n    'history.preview.classContent': 'Class content',\n    'history.preview.relatedClasses': 'Related classes',\n    'history.preview.noRelatedClasses': 'No related classes detected',\n    'history.granular.selectElements': 'Select elements to import',\n    'history.granular.checkAll': 'Check all',\n    'history.granular.attributes': 'Attributes',\n    'history.granular.methods': 'Methods',\n\n    // Analysis Results\n    'analysis.error.updateFailed': 'Failed to update text. Please try again.',\n    'analysis.diagramAlt': 'UML Diagram',\n    'analysis.tooltip.viewAnnotated': 'Click to view annotated image',\n    'analysis.tooltip.convertDiagram': 'If your diagram is handwritten and you want to convert it to a printed diagram, click here',\n    'analysis.imageAnalyzed': 'Analyzed Image',\n    'analysis.extractedText': 'Extracted Text',\n    'analysis.copied': 'Copied!',\n    'analysis.newProject': 'New Project',\n\n    // Conflict Resolution Modal\n    'conflict.title': 'Conflict Resolution',\n    'conflict.attribute': 'Attribute',\n    'conflict.method': 'Method',\n    'conflict.currentVersion': 'Current Version',\n    'conflict.importedVersions': 'Imported Versions',\n    'conflict.keepCurrent': 'Keep Current',\n    'conflict.replace': 'Replace',\n    'conflict.merge': 'Merge',\n    'conflict.cancel': 'Cancel',\n    'conflict.applyResolutions': 'Apply Resolutions',\n\n    // Entity Popup\n    'entity.subtitle': 'UML AI suggests several possible choices for this entity.',\n    'entity.attributes': 'Attributes',\n    'entity.methods': 'Methods',\n    'entity.noAdditionalAttributes': 'No additional attributes',\n    'entity.noAdditionalMethods': 'No additional methods',\n    'entity.modifyEntity': 'Modify Entity',\n    'entity.cancel': 'Cancel',\n\n    // History Analysis Section\n    'historyAnalysis.title': 'Historical Analysis',\n    'historyAnalysis.noResults': 'No historical diagrams found for class \"{className}\"',\n    'historyAnalysis.sortBy': 'Sort by:',\n    'historyAnalysis.conflictsDetected': 'Conflicts detected: {attributes} attribute(s), {methods} method(s)',\n    'historyAnalysis.access.owner': 'Your diagram',\n    'historyAnalysis.access.shared': 'Shared diagram',\n    'historyAnalysis.access.public': 'Public diagram',\n    'historyAnalysis.access.restricted': 'Restricted access',\n\n    // Documentation\n    'doc.description': 'UML Class Analyzer is an advanced web application that uses artificial intelligence to automatically analyze UML class diagrams. Our solution combines the power of models to offer exceptional precision in UML element detection and extraction.',\n    'doc.features.aiDetection.title': 'Advanced AI Detection',\n    'doc.features.aiDetection.description': 'Uses models specifically trained to recognize UML elements with over 95% accuracy.',\n    'doc.features.fastProcessing.title': 'Fast Processing',\n    'doc.features.fastProcessing.description': 'Complete analysis of a UML diagram in less than 30 seconds thanks to our optimized infrastructure.',\n    'doc.features.multiFormat.title': 'Multi-format',\n    'doc.features.multiFormat.description': 'Complete support for PNG, JPG, JPEG and PDF formats with export currently to Java and Mermaid.',\n    'doc.featuresTitle': 'Main Features',\n    'doc.features.uploadAnalyze.title': 'Upload & Analyze',\n    'doc.features.uploadAnalyze.description': 'Upload your UML diagrams (PNG, JPG, PDF) and get complete analysis in seconds with our specialized AI.',\n    'doc.features.relationDetection.title': 'Relation Detection',\n    'doc.features.relationDetection.description': 'Automatic identification of UML relations: inheritance, composition, aggregation, association with annotated visualization.',\n    'doc.features.textExtraction.title': 'Text Extraction',\n    'doc.features.textExtraction.description': 'Intelligent extraction of classes, attributes and methods with automatic organization and multi-format export.',\n    'doc.features.codeGeneration.title': 'Code Generation',\n    'doc.features.codeGeneration.description': 'Automatic conversion to Java and Mermaid diagrams with complete class structure.',\n    'doc.features.historyManagement.title': 'History & Management',\n    'doc.features.historyManagement.description': 'Automatic analysis backup, complete history with Firebase authentication and project management.',\n    'doc.features.modernInterface.title': 'Modern Interface',\n    'doc.features.modernInterface.description': 'Intuitive user interface with dark/light mode, responsive design and optimized user experience.',\n\n    // Usage Guide\n    'doc.guideTitle': 'Step-by-step Usage Guide',\n    'doc.guide.step1.title': 'Diagram Upload',\n    'doc.guide.step1.description': 'Click \"Select a file\" or drag and drop your UML diagram. Supported formats: PNG, JPG, JPEG, PDF. Maximum size: 50MB.',\n    'doc.guide.step2.title': 'Automatic Analysis',\n    'doc.guide.step2.description': 'Our AI analyzes your diagram using multiple models for good results. The process typically takes 15-30 seconds depending on complexity.',\n    'doc.guide.step3.title': 'Relationship Visualization',\n    'doc.guide.step3.description': 'Check the \"Relationship Analysis\" tab to see your annotated diagram with all detected relationships (inheritance, composition, aggregation, etc.).',\n    'doc.guide.step4.title': 'Export and Code Generation',\n    'doc.guide.step4.description': 'In the \"Diagram Extraction\" tab, export your results to Java or Mermaid. Customize the generated code according to your needs.',\n\n    // Technologies\n    'doc.techTitle': 'Technology Stack',\n    'doc.tech.frontend.title': 'Frontend',\n    'doc.tech.frontend.description': 'React 18, TypeScript, CSS-in-JS',\n    'doc.tech.backend.title': 'Backend',\n    'doc.tech.backend.description': 'FastAPI, Python, Uvicorn',\n    'doc.tech.ai.title': 'AI & Vision',\n    'doc.tech.ai.description': 'Confidential',\n    'doc.tech.database.title': 'Database',\n    'doc.tech.database.description': 'Firebase Firestore, Authentication',\n    'doc.tech.visualization.title': 'Visualization',\n    'doc.tech.visualization.description': 'Mermaid.js, SVG, Canvas API',\n\n    // API\n    'doc.apiTitle': 'API Documentation',\n    'doc.api.endpointsTitle': 'Main Endpoints',\n    'doc.api.detect.description': 'Main endpoint for UML diagram analysis. Accepts image files and returns complete analysis with relationship detection.',\n    'doc.api.annotated.description': 'Retrieves the annotated image with detected relationships visualized.',\n    'doc.api.results.description': 'Downloads the text file containing complete extraction of classes, attributes and methods.',\n\n    // FAQ\n    'doc.faqTitle': 'Frequently Asked Questions',\n    'doc.faq.formats.question': '🖼️ What image formats are supported?',\n    'doc.faq.formats.answer': 'Our application supports PNG, JPG, JPEG and PDF formats. For best results, use high resolution images (minimum 800x600) and well-lit. Maximum allowed size is 50MB.',\n    'doc.faq.offline.question': '🌐 Does the application work offline?',\n    'doc.faq.offline.answer': 'No, our application requires an active Internet connection as it uses AI models hosted on our servers to analyze your diagrams. The interface can be cached but analysis requires a connection.',\n    'doc.faq.codeGeneration.question': '💻 Can I generate code from my UML diagram?',\n    'doc.faq.codeGeneration.answer': 'Yes! Our code generation feature allows you to transform your UML diagrams into Java code or Mermaid diagrams. The generated code includes detected classes, attributes, methods and relationships.',\n    'doc.faq.accuracy.question': '🎯 How to improve detection accuracy?',\n    'doc.faq.accuracy.answer': 'To optimize results: use well-contrasted diagrams, ensure texts are readable (minimum 12pt font), avoid blurry images, use distinct colors for different elements, and verify that relationship lines are clearly visible.',\n    'doc.faq.security.question': '🔒 Is my data secure?',\n    'doc.faq.security.answer': 'Yes, we use Firebase Authentication for account security and Firestore for secure storage. Images are processed temporarily and can be deleted after analysis. History is linked to your user account.',\n    'doc.faq.speed.question': '⚡ What is the processing speed?',\n    'doc.faq.speed.answer': 'Processing typically takes 15-30 seconds depending on diagram complexity. Simple diagrams (2-5 classes) are processed in 10-15 seconds, while complex diagrams (10+ classes) can take up to 45 seconds.',\n\n    // Contact\n    'doc.contact.title': 'Need additional help?',\n    'doc.contact.description': 'Thank you for using our UML Class Analyzer application. This documentation guides you through using all available features.',\n\n\n\n\n\n    // Documentation\n    'doc.title': 'UML Class Analyzer Documentation',\n    'doc.subtitle': 'Complete guide for using our AI-powered UML analysis platform',\n    'doc.version': 'v2.5.3 Enterprise',\n    'doc.overview': 'Overview',\n    'doc.features': 'Features',\n    'doc.guide': 'User Guide',\n    'doc.technologies': 'Technologies',\n    'doc.api': 'API',\n    'doc.faq': 'FAQ',\n    'doc.projectOverview': 'Project Overview',\n    'doc.appTitle': 'UML Class Analyzer - Enterprise Edition v2.5.3',\n    'doc.gettingStarted': 'Getting Started',\n    'doc.tutorials': 'Tutorials',\n    'doc.support': 'Support',\n    'doc.lastUpdate': 'Last Update',\n\n    // Common buttons and actions\n    'common.ok': 'OK',\n    'common.cancel': 'Cancel',\n    'common.save': 'Save',\n    'common.delete': 'Delete',\n    'common.edit': 'Edit',\n    'common.close': 'Close',\n    'common.back': 'Back',\n    'common.next': 'Next',\n    'common.previous': 'Previous',\n    'common.loading': 'Loading...',\n    'common.error': 'Error',\n    'common.success': 'Success',\n    'common.warning': 'Warning',\n    'common.info': 'Information',\n  }\n};\n\n// Création du contexte\nconst LanguageContext = createContext<LanguageContextType | undefined>(undefined);\n\n// Provider du contexte\ninterface LanguageProviderProps {\n  children: ReactNode;\n}\n\nexport const LanguageProvider: React.FC<LanguageProviderProps> = ({ children }) => {\n  const [language, setLanguageState] = useState<Language>('fr');\n\n  // Charger la langue depuis le localStorage au démarrage\n  useEffect(() => {\n    const savedLanguage = localStorage.getItem('app-language') as Language;\n    if (savedLanguage && (savedLanguage === 'fr' || savedLanguage === 'en')) {\n      setLanguageState(savedLanguage);\n    }\n  }, []);\n\n  // Sauvegarder la langue dans le localStorage\n  const setLanguage = (lang: Language) => {\n    setLanguageState(lang);\n    localStorage.setItem('app-language', lang);\n  };\n\n  // Fonction de traduction\n  const t = (key: string): string => {\n    return translations[language][key as keyof typeof translations[typeof language]] || key;\n  };\n\n  const value: LanguageContextType = {\n    language,\n    setLanguage,\n    t\n  };\n\n  return (\n    <LanguageContext.Provider value={value}>\n      {children}\n    </LanguageContext.Provider>\n  );\n};\n\n// Hook pour utiliser le contexte\nexport const useLanguage = (): LanguageContextType => {\n  const context = useContext(LanguageContext);\n  if (!context) {\n    throw new Error('useLanguage must be used within a LanguageProvider');\n  }\n  return context;\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAmB,OAAO;;AAExF;;AAGA;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAOA;AACA,MAAMC,YAAY,GAAG;EACnBC,EAAE,EAAE;IACF;IACA,WAAW,EAAE,uBAAuB;IACpC,cAAc,EAAE,WAAW;IAE3B;IACA,YAAY,EAAE,iBAAiB;IAC/B,eAAe,EAAE,sBAAsB;IACvC,UAAU,EAAE,yBAAyB;IACrC,mBAAmB,EAAE,eAAe;IAEpC;IACA,sBAAsB,EAAE,yBAAyB;IACjD,yBAAyB,EAAE,6EAA6E;IACxG,yBAAyB,EAAE,0BAA0B;IACrD,4BAA4B,EAAE,yEAAyE;IACvG,oBAAoB,EAAE,yBAAyB;IAC/C,uBAAuB,EAAE,yCAAyC;IAClE,6BAA6B,EAAE,4BAA4B;IAC3D,gCAAgC,EAAE,qDAAqD;IAEvF;IACA,aAAa,EAAE,uDAAuD;IAEtE;IACA,kBAAkB,EAAE,IAAI;IACxB,iBAAiB,EAAE,mBAAmB;IAEtC;IACA,wBAAwB,EAAE,kEAAkE;IAC5F,0BAA0B,EAAE,yBAAyB;IACrD,yBAAyB,EAAE,qBAAqB;IAChD,gCAAgC,EAAE,2BAA2B;IAC7D,mCAAmC,EAAE,2CAA2C;IAChF,kCAAkC,EAAE,4DAA4D;IAChG,kCAAkC,EAAE,kEAAkE;IACtG,iCAAiC,EAAE,+BAA+B;IAClE,kCAAkC,EAAE,kBAAkB;IACtD,8BAA8B,EAAE,uCAAuC;IACvE,mCAAmC,EAAE,8CAA8C;IACnF,gCAAgC,EAAE,uCAAuC;IACzE,gCAAgC,EAAE,gCAAgC;IAClE,+BAA+B,EAAE,0CAA0C;IAC3E,+BAA+B,EAAE,0BAA0B;IAC3D,kCAAkC,EAAE,kBAAkB;IACtD,oCAAoC,EAAE,8BAA8B;IACpE,qCAAqC,EAAE,uBAAuB;IAE9D;IACA,sBAAsB,EAAE,4BAA4B;IACpD,wBAAwB,EAAE,mCAAmC;IAC7D,sBAAsB,EAAE,qGAAqG;IAC7H,wBAAwB,EAAE,+GAA+G;IACzI,yBAAyB,EAAE,+CAA+C;IAC1E,8BAA8B,EAAE,uBAAuB;IACvD,4BAA4B,EAAE,2LAA2L;IACzN,6BAA6B,EAAE,QAAQ;IACvC,gCAAgC,EAAE,UAAU;IAC5C,mCAAmC,EAAE,YAAY;IACjD,mCAAmC,EAAE,aAAa;IAClD,sCAAsC,EAAE,gBAAgB;IACxD,mCAAmC,EAAE,aAAa;IAClD,0BAA0B,EAAE,QAAQ;IACpC,yBAAyB,EAAE,oKAAoK;IAE/L;IACA,oBAAoB,EAAE,6BAA6B;IACnD,6BAA6B,EAAE,mBAAmB;IAClD,0BAA0B,EAAE,WAAW;IACvC,4BAA4B,EAAE,aAAa;IAC3C,kCAAkC,EAAE,oCAAoC;IACxE,+BAA+B,EAAE,+BAA+B;IAChE,iCAAiC,EAAE,iCAAiC;IACpE,4BAA4B,EAAE,mDAAmD;IACjF,2BAA2B,EAAE,oBAAoB;IACjD,0BAA0B,EAAE,QAAQ;IACpC,4BAA4B,EAAE,UAAU;IACxC,gCAAgC,EAAE,aAAa;IAC/C,4BAA4B,EAAE,QAAQ;IACtC,6BAA6B,EAAE,QAAQ;IACvC,+BAA+B,EAAE,oBAAoB;IACrD,qBAAqB,EAAE,SAAS;IAChC,2BAA2B,EAAE,iBAAiB;IAC9C,yBAAyB,EAAE,cAAc;IACzC,yBAAyB,EAAE,cAAc;IACzC,iCAAiC,EAAE,0BAA0B;IAC7D,mCAAmC,EAAE,iGAAiG;IACtI,2BAA2B,EAAE,SAAS;IACtC,iCAAiC,EAAE,oBAAoB;IAEvD;IACA,kBAAkB,EAAE,0BAA0B;IAC9C,mBAAmB,EAAE,iBAAiB;IACtC,iBAAiB,EAAE,KAAK;IACxB,kBAAkB,EAAE,OAAO;IAC3B,qBAAqB,EAAE,cAAc;IACrC,4BAA4B,EAAE,2BAA2B;IACzD,uBAAuB,EAAE,WAAW;IACpC,wBAAwB,EAAE,yBAAyB;IACnD,2BAA2B,EAAE,uBAAuB;IACpD,kCAAkC,EAAE,8BAA8B;IAClE,iBAAiB,EAAE,oBAAoB;IACvC,qBAAqB,EAAE,uBAAuB;IAC9C,kBAAkB,EAAE,0CAA0C;IAC9D,mBAAmB,EAAE,cAAc;IACnC,oBAAoB,EAAE,aAAa;IACnC,2BAA2B,EAAE,iBAAiB;IAC9C,uBAAuB,EAAE,cAAc;IACvC,6BAA6B,EAAE,uBAAuB;IACtD,gCAAgC,EAAE,uBAAuB;IACzD,oBAAoB,EAAE,gBAAgB;IACtC,SAAS,EAAE,IAAI;IACf,gBAAgB,EAAE,8BAA8B;IAChD,kBAAkB,EAAE,4BAA4B;IAChD,0BAA0B,EAAE,qBAAqB;IACjD,yBAAyB,EAAE,iCAAiC;IAC5D,6BAA6B,EAAE,4BAA4B;IAC3D,6BAA6B,EAAE,qDAAqD;IACpF,yBAAyB,EAAE,mBAAmB;IAC9C,oCAAoC,EAAE,uCAAuC;IAC7E,6BAA6B,EAAE,wCAAwC;IACvE,uBAAuB,EAAE,8BAA8B;IACvD,+BAA+B,EAAE,gCAAgC;IACjE,uBAAuB,EAAE,4BAA4B;IACrD,yBAAyB,EAAE,iCAAiC;IAC5D,4BAA4B,EAAE,mDAAmD;IACjF,8BAA8B,EAAE,+CAA+C;IAE/E;IACA,eAAe,EAAE,gBAAgB;IACjC,oBAAoB,EAAE,gBAAgB;IACtC,eAAe,EAAE,kBAAkB;IACnC,iBAAiB,EAAE,eAAe;IAClC,eAAe,EAAE,4CAA4C;IAC7D,uBAAuB,EAAE,kDAAkD;IAC3E,0BAA0B,EAAE,2FAA2F;IACvH,gBAAgB,EAAE,WAAW;IAC7B,mBAAmB,EAAE,8BAA8B;IACnD,gBAAgB,EAAE,SAAS;IAC3B,qBAAqB,EAAE,2CAA2C;IAClE,2BAA2B,EAAE,eAAe;IAC5C,mBAAmB,EAAE,uBAAuB;IAC5C,mBAAmB,EAAE,WAAW;IAChC,gBAAgB,EAAE,WAAW;IAC7B,gBAAgB,EAAE,SAAS;IAE3B;IACA,+BAA+B,EAAE,0DAA0D;IAC3F,+BAA+B,EAAE,wDAAwD;IACzF,wBAAwB,EAAE,aAAa;IACvC,uBAAuB,EAAE,mCAAmC;IAC5D,8BAA8B,EAAE,sBAAsB;IACtD,gCAAgC,EAAE,eAAe;IACjD,kCAAkC,EAAE,6BAA6B;IACjE,iCAAiC,EAAE,sCAAsC;IACzE,2BAA2B,EAAE,aAAa;IAC1C,6BAA6B,EAAE,WAAW;IAC1C,0BAA0B,EAAE,UAAU;IAEtC;IACA,6BAA6B,EAAE,uDAAuD;IACtF,qBAAqB,EAAE,eAAe;IACtC,gCAAgC,EAAE,oCAAoC;IACtE,iCAAiC,EAAE,wGAAwG;IAC3I,wBAAwB,EAAE,gBAAgB;IAC1C,wBAAwB,EAAE,eAAe;IACzC,iBAAiB,EAAE,QAAQ;IAC3B,qBAAqB,EAAE,gBAAgB;IAEvC;IACA,gBAAgB,EAAE,yBAAyB;IAC3C,oBAAoB,EAAE,UAAU;IAChC,iBAAiB,EAAE,SAAS;IAC5B,yBAAyB,EAAE,kBAAkB;IAC7C,2BAA2B,EAAE,oBAAoB;IACjD,sBAAsB,EAAE,kBAAkB;IAC1C,kBAAkB,EAAE,WAAW;IAC/B,gBAAgB,EAAE,WAAW;IAC7B,iBAAiB,EAAE,SAAS;IAC5B,2BAA2B,EAAE,2BAA2B;IAExD;IACA,iBAAiB,EAAE,6DAA6D;IAChF,mBAAmB,EAAE,WAAW;IAChC,gBAAgB,EAAE,UAAU;IAC5B,+BAA+B,EAAE,+BAA+B;IAChE,4BAA4B,EAAE,+BAA+B;IAC7D,qBAAqB,EAAE,oBAAoB;IAC3C,eAAe,EAAE,SAAS;IAE1B;IACA,uBAAuB,EAAE,oBAAoB;IAC7C,2BAA2B,EAAE,gEAAgE;IAC7F,wBAAwB,EAAE,YAAY;IACtC,mCAAmC,EAAE,mEAAmE;IACxG,8BAA8B,EAAE,iBAAiB;IACjD,+BAA+B,EAAE,mBAAmB;IACpD,+BAA+B,EAAE,kBAAkB;IACnD,mCAAmC,EAAE,iBAAiB;IAEtD;IACA,iBAAiB,EAAE,uSAAuS;IAC1T,gCAAgC,EAAE,sBAAsB;IACxD,sCAAsC,EAAE,mHAAmH;IAC3J,mCAAmC,EAAE,mBAAmB;IACxD,yCAAyC,EAAE,sGAAsG;IACjJ,gCAAgC,EAAE,cAAc;IAChD,sCAAsC,EAAE,oGAAoG;IAC5I,mBAAmB,EAAE,6BAA6B;IAClD,kCAAkC,EAAE,kBAAkB;IACtD,wCAAwC,EAAE,gIAAgI;IAC1K,sCAAsC,EAAE,wBAAwB;IAChE,4CAA4C,EAAE,2HAA2H;IACzK,mCAAmC,EAAE,qBAAqB;IAC1D,yCAAyC,EAAE,kHAAkH;IAC7J,mCAAmC,EAAE,oBAAoB;IACzD,yCAAyC,EAAE,4FAA4F;IACvI,sCAAsC,EAAE,sBAAsB;IAC9D,4CAA4C,EAAE,gHAAgH;IAC9J,oCAAoC,EAAE,mBAAmB;IACzD,0CAA0C,EAAE,gHAAgH;IAE5J;IACA,gBAAgB,EAAE,sCAAsC;IACxD,uBAAuB,EAAE,6BAA6B;IACtD,6BAA6B,EAAE,gJAAgJ;IAC/K,uBAAuB,EAAE,qBAAqB;IAC9C,6BAA6B,EAAE,6JAA6J;IAC5L,uBAAuB,EAAE,6BAA6B;IACtD,6BAA6B,EAAE,4JAA4J;IAC3L,uBAAuB,EAAE,8BAA8B;IACvD,6BAA6B,EAAE,sIAAsI;IAErK;IACA,eAAe,EAAE,qBAAqB;IACtC,yBAAyB,EAAE,UAAU;IACrC,+BAA+B,EAAE,iCAAiC;IAClE,wBAAwB,EAAE,SAAS;IACnC,8BAA8B,EAAE,0BAA0B;IAC1D,mBAAmB,EAAE,aAAa;IAClC,yBAAyB,EAAE,cAAc;IACzC,yBAAyB,EAAE,iBAAiB;IAC5C,+BAA+B,EAAE,oCAAoC;IACrE,8BAA8B,EAAE,eAAe;IAC/C,oCAAoC,EAAE,6BAA6B;IAEnE;IACA,cAAc,EAAE,mBAAmB;IACnC,wBAAwB,EAAE,sBAAsB;IAChD,4BAA4B,EAAE,+IAA+I;IAC7K,+BAA+B,EAAE,qEAAqE;IACtG,6BAA6B,EAAE,kGAAkG;IAEjI;IACA,cAAc,EAAE,8BAA8B;IAC9C,0BAA0B,EAAE,6CAA6C;IACzE,wBAAwB,EAAE,mNAAmN;IAC7O,0BAA0B,EAAE,kDAAkD;IAC9E,wBAAwB,EAAE,yOAAyO;IACnQ,iCAAiC,EAAE,4DAA4D;IAC/F,+BAA+B,EAAE,sNAAsN;IACvP,2BAA2B,EAAE,kDAAkD;IAC/E,yBAAyB,EAAE,oSAAoS;IAC/T,2BAA2B,EAAE,wCAAwC;IACrE,yBAAyB,EAAE,sPAAsP;IACjR,wBAAwB,EAAE,yCAAyC;IACnE,sBAAsB,EAAE,gPAAgP;IAExQ;IACA,mBAAmB,EAAE,iCAAiC;IACtD,yBAAyB,EAAE,uJAAuJ;IAMlL;IACA,WAAW,EAAE,kCAAkC;IAC/C,cAAc,EAAE,0FAA0F;IAC1G,aAAa,EAAE,mBAAmB;IAClC,cAAc,EAAE,iBAAiB;IACjC,cAAc,EAAE,iBAAiB;IACjC,WAAW,EAAE,sBAAsB;IACnC,kBAAkB,EAAE,cAAc;IAClC,SAAS,EAAE,KAAK;IAChB,SAAS,EAAE,KAAK;IAChB,qBAAqB,EAAE,2BAA2B;IAClD,cAAc,EAAE,gDAAgD;IAChE,oBAAoB,EAAE,WAAW;IACjC,eAAe,EAAE,WAAW;IAC5B,aAAa,EAAE,SAAS;IACxB,gBAAgB,EAAE,sBAAsB;IAExC;IACA,WAAW,EAAE,IAAI;IACjB,eAAe,EAAE,SAAS;IAC1B,aAAa,EAAE,aAAa;IAC5B,eAAe,EAAE,WAAW;IAC5B,aAAa,EAAE,UAAU;IACzB,cAAc,EAAE,QAAQ;IACxB,aAAa,EAAE,QAAQ;IACvB,aAAa,EAAE,SAAS;IACxB,iBAAiB,EAAE,WAAW;IAC9B,gBAAgB,EAAE,eAAe;IACjC,cAAc,EAAE,QAAQ;IACxB,gBAAgB,EAAE,QAAQ;IAC1B,gBAAgB,EAAE,WAAW;IAC7B,aAAa,EAAE;EACjB,CAAC;EACDC,EAAE,EAAE;IACF;IACA,WAAW,EAAE,uBAAuB;IACpC,cAAc,EAAE,OAAO;IAEvB;IACA,YAAY,EAAE,cAAc;IAC5B,eAAe,EAAE,oBAAoB;IACrC,UAAU,EAAE,oBAAoB;IAChC,mBAAmB,EAAE,eAAe;IAEpC;IACA,sBAAsB,EAAE,oBAAoB;IAC5C,yBAAyB,EAAE,iEAAiE;IAC5F,yBAAyB,EAAE,wBAAwB;IACnD,4BAA4B,EAAE,kEAAkE;IAChG,oBAAoB,EAAE,oBAAoB;IAC1C,uBAAuB,EAAE,iCAAiC;IAC1D,6BAA6B,EAAE,2BAA2B;IAC1D,gCAAgC,EAAE,sCAAsC;IAExE;IACA,aAAa,EAAE,uDAAuD;IAEtE;IACA,kBAAkB,EAAE,IAAI;IACxB,iBAAiB,EAAE,kBAAkB;IAErC;IACA,wBAAwB,EAAE,mDAAmD;IAC7E,0BAA0B,EAAE,eAAe;IAC3C,yBAAyB,EAAE,cAAc;IACzC,gCAAgC,EAAE,sBAAsB;IACxD,mCAAmC,EAAE,oCAAoC;IACzE,kCAAkC,EAAE,0DAA0D;IAC9F,kCAAkC,EAAE,2DAA2D;IAC/F,iCAAiC,EAAE,qBAAqB;IACxD,kCAAkC,EAAE,oBAAoB;IACxD,8BAA8B,EAAE,6BAA6B;IAC7D,mCAAmC,EAAE,wBAAwB;IAC7D,gCAAgC,EAAE,wBAAwB;IAC1D,gCAAgC,EAAE,8BAA8B;IAChE,+BAA+B,EAAE,gCAAgC;IACjE,+BAA+B,EAAE,yBAAyB;IAC1D,kCAAkC,EAAE,cAAc;IAClD,oCAAoC,EAAE,yBAAyB;IAC/D,qCAAqC,EAAE,iBAAiB;IAExD;IACA,sBAAsB,EAAE,yBAAyB;IACjD,wBAAwB,EAAE,4BAA4B;IACtD,sBAAsB,EAAE,0EAA0E;IAClG,wBAAwB,EAAE,uFAAuF;IACjH,yBAAyB,EAAE,mDAAmD;IAC9E,8BAA8B,EAAE,oBAAoB;IACpD,4BAA4B,EAAE,2LAA2L;IACzN,6BAA6B,EAAE,OAAO;IACtC,gCAAgC,EAAE,UAAU;IAC5C,mCAAmC,EAAE,aAAa;IAClD,mCAAmC,EAAE,aAAa;IAClD,sCAAsC,EAAE,gBAAgB;IACxD,mCAAmC,EAAE,aAAa;IAClD,0BAA0B,EAAE,KAAK;IACjC,yBAAyB,EAAE,sIAAsI;IAEjK;IACA,oBAAoB,EAAE,uBAAuB;IAC7C,6BAA6B,EAAE,cAAc;IAC7C,0BAA0B,EAAE,WAAW;IACvC,4BAA4B,EAAE,aAAa;IAC3C,kCAAkC,EAAE,iCAAiC;IACrE,+BAA+B,EAAE,8BAA8B;IAC/D,iCAAiC,EAAE,gCAAgC;IACnE,4BAA4B,EAAE,+CAA+C;IAC7E,2BAA2B,EAAE,eAAe;IAC5C,0BAA0B,EAAE,MAAM;IAClC,4BAA4B,EAAE,QAAQ;IACtC,gCAAgC,EAAE,YAAY;IAC9C,4BAA4B,EAAE,SAAS;IACvC,6BAA6B,EAAE,UAAU;IACzC,+BAA+B,EAAE,YAAY;IAC7C,qBAAqB,EAAE,SAAS;IAChC,2BAA2B,EAAE,aAAa;IAC1C,yBAAyB,EAAE,YAAY;IACvC,yBAAyB,EAAE,YAAY;IACvC,iCAAiC,EAAE,kBAAkB;IACrD,mCAAmC,EAAE,iFAAiF;IACtH,2BAA2B,EAAE,QAAQ;IACrC,iCAAiC,EAAE,eAAe;IAElD;IACA,kBAAkB,EAAE,wBAAwB;IAC5C,mBAAmB,EAAE,mBAAmB;IACxC,iBAAiB,EAAE,MAAM;IACzB,kBAAkB,EAAE,OAAO;IAC3B,qBAAqB,EAAE,UAAU;IACjC,4BAA4B,EAAE,kBAAkB;IAChD,uBAAuB,EAAE,WAAW;IACpC,wBAAwB,EAAE,wBAAwB;IAClD,2BAA2B,EAAE,mBAAmB;IAChD,kCAAkC,EAAE,uBAAuB;IAC3D,iBAAiB,EAAE,aAAa;IAChC,qBAAqB,EAAE,kBAAkB;IACzC,kBAAkB,EAAE,+BAA+B;IACnD,mBAAmB,EAAE,QAAQ;IAC7B,oBAAoB,EAAE,SAAS;IAC/B,2BAA2B,EAAE,gBAAgB;IAC7C,uBAAuB,EAAE,eAAe;IACxC,6BAA6B,EAAE,qBAAqB;IACpD,gCAAgC,EAAE,sBAAsB;IACxD,oBAAoB,EAAE,SAAS;IAC/B,SAAS,EAAE,IAAI;IACf,gBAAgB,EAAE,yBAAyB;IAC3C,kBAAkB,EAAE,0BAA0B;IAC9C,0BAA0B,EAAE,mBAAmB;IAC/C,yBAAyB,EAAE,4BAA4B;IACvD,6BAA6B,EAAE,sBAAsB;IACrD,6BAA6B,EAAE,wCAAwC;IACvE,yBAAyB,EAAE,kBAAkB;IAC7C,oCAAoC,EAAE,8BAA8B;IACpE,6BAA6B,EAAE,wBAAwB;IACvD,uBAAuB,EAAE,uBAAuB;IAChD,+BAA+B,EAAE,2BAA2B;IAC5D,uBAAuB,EAAE,yBAAyB;IAClD,yBAAyB,EAAE,sBAAsB;IACjD,4BAA4B,EAAE,4CAA4C;IAC1E,8BAA8B,EAAE,wCAAwC;IAExE;IACA,eAAe,EAAE,aAAa;IAC9B,oBAAoB,EAAE,aAAa;IACnC,eAAe,EAAE,YAAY;IAC7B,iBAAiB,EAAE,YAAY;IAC/B,eAAe,EAAE,uBAAuB;IACxC,uBAAuB,EAAE,4CAA4C;IACrE,0BAA0B,EAAE,gFAAgF;IAC5G,gBAAgB,EAAE,QAAQ;IAC1B,mBAAmB,EAAE,oBAAoB;IACzC,gBAAgB,EAAE,QAAQ;IAC1B,qBAAqB,EAAE,4BAA4B;IACnD,2BAA2B,EAAE,WAAW;IACxC,mBAAmB,EAAE,kBAAkB;IACvC,mBAAmB,EAAE,WAAW;IAChC,gBAAgB,EAAE,QAAQ;IAE1B;IACA,+BAA+B,EAAE,2CAA2C;IAC5E,+BAA+B,EAAE,2CAA2C;IAC5E,wBAAwB,EAAE,cAAc;IACxC,uBAAuB,EAAE,gCAAgC;IACzD,8BAA8B,EAAE,eAAe;IAC/C,gCAAgC,EAAE,iBAAiB;IACnD,kCAAkC,EAAE,6BAA6B;IACjE,iCAAiC,EAAE,2BAA2B;IAC9D,2BAA2B,EAAE,WAAW;IACxC,6BAA6B,EAAE,YAAY;IAC3C,0BAA0B,EAAE,SAAS;IAErC;IACA,6BAA6B,EAAE,0CAA0C;IACzE,qBAAqB,EAAE,aAAa;IACpC,gCAAgC,EAAE,+BAA+B;IACjE,iCAAiC,EAAE,4FAA4F;IAC/H,wBAAwB,EAAE,gBAAgB;IAC1C,wBAAwB,EAAE,gBAAgB;IAC1C,iBAAiB,EAAE,SAAS;IAC5B,qBAAqB,EAAE,aAAa;IAEpC;IACA,gBAAgB,EAAE,qBAAqB;IACvC,oBAAoB,EAAE,WAAW;IACjC,iBAAiB,EAAE,QAAQ;IAC3B,yBAAyB,EAAE,iBAAiB;IAC5C,2BAA2B,EAAE,mBAAmB;IAChD,sBAAsB,EAAE,cAAc;IACtC,kBAAkB,EAAE,SAAS;IAC7B,gBAAgB,EAAE,OAAO;IACzB,iBAAiB,EAAE,QAAQ;IAC3B,2BAA2B,EAAE,mBAAmB;IAEhD;IACA,iBAAiB,EAAE,2DAA2D;IAC9E,mBAAmB,EAAE,YAAY;IACjC,gBAAgB,EAAE,SAAS;IAC3B,+BAA+B,EAAE,0BAA0B;IAC3D,4BAA4B,EAAE,uBAAuB;IACrD,qBAAqB,EAAE,eAAe;IACtC,eAAe,EAAE,QAAQ;IAEzB;IACA,uBAAuB,EAAE,qBAAqB;IAC9C,2BAA2B,EAAE,sDAAsD;IACnF,wBAAwB,EAAE,UAAU;IACpC,mCAAmC,EAAE,oEAAoE;IACzG,8BAA8B,EAAE,cAAc;IAC9C,+BAA+B,EAAE,gBAAgB;IACjD,+BAA+B,EAAE,gBAAgB;IACjD,mCAAmC,EAAE,mBAAmB;IAExD;IACA,iBAAiB,EAAE,oPAAoP;IACvQ,gCAAgC,EAAE,uBAAuB;IACzD,sCAAsC,EAAE,oFAAoF;IAC5H,mCAAmC,EAAE,iBAAiB;IACtD,yCAAyC,EAAE,oGAAoG;IAC/I,gCAAgC,EAAE,cAAc;IAChD,sCAAsC,EAAE,gGAAgG;IACxI,mBAAmB,EAAE,eAAe;IACpC,kCAAkC,EAAE,kBAAkB;IACtD,wCAAwC,EAAE,wGAAwG;IAClJ,sCAAsC,EAAE,oBAAoB;IAC5D,4CAA4C,EAAE,6HAA6H;IAC3K,mCAAmC,EAAE,iBAAiB;IACtD,yCAAyC,EAAE,gHAAgH;IAC3J,mCAAmC,EAAE,iBAAiB;IACtD,yCAAyC,EAAE,kFAAkF;IAC7H,sCAAsC,EAAE,sBAAsB;IAC9D,4CAA4C,EAAE,kGAAkG;IAChJ,oCAAoC,EAAE,kBAAkB;IACxD,0CAA0C,EAAE,iGAAiG;IAE7I;IACA,gBAAgB,EAAE,0BAA0B;IAC5C,uBAAuB,EAAE,gBAAgB;IACzC,6BAA6B,EAAE,sHAAsH;IACrJ,uBAAuB,EAAE,oBAAoB;IAC7C,6BAA6B,EAAE,yIAAyI;IACxK,uBAAuB,EAAE,4BAA4B;IACrD,6BAA6B,EAAE,oJAAoJ;IACnL,uBAAuB,EAAE,4BAA4B;IACrD,6BAA6B,EAAE,gIAAgI;IAE/J;IACA,eAAe,EAAE,kBAAkB;IACnC,yBAAyB,EAAE,UAAU;IACrC,+BAA+B,EAAE,iCAAiC;IAClE,wBAAwB,EAAE,SAAS;IACnC,8BAA8B,EAAE,0BAA0B;IAC1D,mBAAmB,EAAE,aAAa;IAClC,yBAAyB,EAAE,cAAc;IACzC,yBAAyB,EAAE,UAAU;IACrC,+BAA+B,EAAE,oCAAoC;IACrE,8BAA8B,EAAE,eAAe;IAC/C,oCAAoC,EAAE,6BAA6B;IAEnE;IACA,cAAc,EAAE,mBAAmB;IACnC,wBAAwB,EAAE,gBAAgB;IAC1C,4BAA4B,EAAE,wHAAwH;IACtJ,+BAA+B,EAAE,uEAAuE;IACxG,6BAA6B,EAAE,4FAA4F;IAE3H;IACA,cAAc,EAAE,4BAA4B;IAC5C,0BAA0B,EAAE,uCAAuC;IACnE,wBAAwB,EAAE,qKAAqK;IAC/L,0BAA0B,EAAE,uCAAuC;IACnE,wBAAwB,EAAE,iMAAiM;IAC3N,iCAAiC,EAAE,6CAA6C;IAChF,+BAA+B,EAAE,qMAAqM;IACtO,2BAA2B,EAAE,uCAAuC;IACpE,yBAAyB,EAAE,4NAA4N;IACvP,2BAA2B,EAAE,uBAAuB;IACpD,yBAAyB,EAAE,wMAAwM;IACnO,wBAAwB,EAAE,iCAAiC;IAC3D,sBAAsB,EAAE,yMAAyM;IAEjO;IACA,mBAAmB,EAAE,uBAAuB;IAC5C,yBAAyB,EAAE,6HAA6H;IAMxJ;IACA,WAAW,EAAE,kCAAkC;IAC/C,cAAc,EAAE,+DAA+D;IAC/E,aAAa,EAAE,mBAAmB;IAClC,cAAc,EAAE,UAAU;IAC1B,cAAc,EAAE,UAAU;IAC1B,WAAW,EAAE,YAAY;IACzB,kBAAkB,EAAE,cAAc;IAClC,SAAS,EAAE,KAAK;IAChB,SAAS,EAAE,KAAK;IAChB,qBAAqB,EAAE,kBAAkB;IACzC,cAAc,EAAE,gDAAgD;IAChE,oBAAoB,EAAE,iBAAiB;IACvC,eAAe,EAAE,WAAW;IAC5B,aAAa,EAAE,SAAS;IACxB,gBAAgB,EAAE,aAAa;IAE/B;IACA,WAAW,EAAE,IAAI;IACjB,eAAe,EAAE,QAAQ;IACzB,aAAa,EAAE,MAAM;IACrB,eAAe,EAAE,QAAQ;IACzB,aAAa,EAAE,MAAM;IACrB,cAAc,EAAE,OAAO;IACvB,aAAa,EAAE,MAAM;IACrB,aAAa,EAAE,MAAM;IACrB,iBAAiB,EAAE,UAAU;IAC7B,gBAAgB,EAAE,YAAY;IAC9B,cAAc,EAAE,OAAO;IACvB,gBAAgB,EAAE,SAAS;IAC3B,gBAAgB,EAAE,SAAS;IAC3B,aAAa,EAAE;EACjB;AACF,CAAC;;AAED;AACA,MAAMC,eAAe,gBAAGT,aAAa,CAAkCU,SAAS,CAAC;;AAEjF;;AAKA,OAAO,MAAMC,gBAAiD,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACjF,MAAM,CAACC,QAAQ,EAAEC,gBAAgB,CAAC,GAAGb,QAAQ,CAAW,IAAI,CAAC;;EAE7D;EACAC,SAAS,CAAC,MAAM;IACd,MAAMa,aAAa,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAa;IACtE,IAAIF,aAAa,KAAKA,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,IAAI,CAAC,EAAE;MACvED,gBAAgB,CAACC,aAAa,CAAC;IACjC;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMG,WAAW,GAAIC,IAAc,IAAK;IACtCL,gBAAgB,CAACK,IAAI,CAAC;IACtBH,YAAY,CAACI,OAAO,CAAC,cAAc,EAAED,IAAI,CAAC;EAC5C,CAAC;;EAED;EACA,MAAME,CAAC,GAAIC,GAAW,IAAa;IACjC,OAAOjB,YAAY,CAACQ,QAAQ,CAAC,CAACS,GAAG,CAA+C,IAAIA,GAAG;EACzF,CAAC;EAED,MAAMC,KAA0B,GAAG;IACjCV,QAAQ;IACRK,WAAW;IACXG;EACF,CAAC;EAED,oBACEjB,OAAA,CAACI,eAAe,CAACgB,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAZ,QAAA,EACpCA;EAAQ;IAAAc,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACe,CAAC;AAE/B,CAAC;;AAED;AAAAhB,EAAA,CAnCaF,gBAAiD;AAAAmB,EAAA,GAAjDnB,gBAAiD;AAoC9D,OAAO,MAAMoB,WAAW,GAAGA,CAAA,KAA2B;EAAAC,GAAA;EACpD,MAAMC,OAAO,GAAGhC,UAAU,CAACQ,eAAe,CAAC;EAC3C,IAAI,CAACwB,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,oDAAoD,CAAC;EACvE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,WAAW;AAAA,IAAAD,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}