// HistoryAnalysisSection.tsx - Composant pour l'analyse historique et l'import de classes
import React, { useState, useEffect } from 'react';
import { useHistory } from '../../context/HistoryContext';
import { useAuth } from '../../context/AuthContext';
import { useLanguage } from '../../context/LanguageContext';
import { HistoryAnalysisService, HistoryMatch, SortOption, ClassData } from '../../services/HistoryAnalysisService';
import { ChevronDown, Eye, Clock, Zap, AlertTriangle, Shield, Users, Lock } from 'lucide-react';
import './HistoryAnalysisSection.css';

interface HistoryAnalysisSectionProps {
  darkMode: boolean;
  targetClassName: string;
  currentClassData: ClassData;
  onImport: (importedData: ClassData) => void;
  currentDiagramText?: string; // Texte du diagramme actuel pour l'exclure
}

const HistoryAnalysisSection: React.FC<HistoryAnalysisSectionProps> = ({
  darkMode,
  targetClassName,
  currentClassData,
  onImport,
  currentDiagramText
}) => {
  const { historyItems } = useHistory();
  const { currentUser } = useAuth();
  const { t } = useLanguage();
  const [isExpanded, setIsExpanded] = useState(false);
  const [matches, setMatches] = useState<HistoryMatch[]>([]);
  const [selectedMatches, setSelectedMatches] = useState<Set<string>>(new Set());
  const [selectedAttributes, setSelectedAttributes] = useState<Map<string, Set<string>>>(new Map());
  const [selectedMethods, setSelectedMethods] = useState<Map<string, Set<string>>>(new Map());
  const [sortOption, setSortOption] = useState<SortOption>(HistoryAnalysisService.getSortOptions()[0]);
  const [showPreview, setShowPreview] = useState<string | null>(null);
  const [conflicts, setConflicts] = useState<{ attributes: string[], methods: string[] }>({ attributes: [], methods: [] });
  const [previewData, setPreviewData] = useState<HistoryMatch | null>(null);

  // Rechercher les correspondances lors du changement de classe cible
  useEffect(() => {
    if (!targetClassName || !currentUser) return;
    
    const foundMatches = HistoryAnalysisService.findMatchingDiagrams(
      targetClassName,
      historyItems,
      currentUser.uid,
      currentDiagramText
    );
    
    const sortedMatches = HistoryAnalysisService.sortMatches(foundMatches, sortOption);
    setMatches(sortedMatches);
  }, [targetClassName, historyItems, currentUser, sortOption, currentDiagramText]);

  // Détecter les conflits lors du changement de sélection
  useEffect(() => {
    const selectedMatchObjects = matches.filter(match => selectedMatches.has(match.historyItem.id));
    const selectedClasses = selectedMatchObjects.flatMap(match => match.matchingClasses);
    
    if (selectedClasses.length > 0) {
      const detectedConflicts = HistoryAnalysisService.detectConflicts(currentClassData, selectedClasses);
      setConflicts(detectedConflicts);
    } else {
      setConflicts({ attributes: [], methods: [] });
    }
  }, [selectedMatches, matches, currentClassData]);

  const handleToggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  const handleMatchSelection = (matchId: string, selected: boolean) => {
    const newSelection = new Set(selectedMatches);
    if (selected) {
      newSelection.add(matchId);
    } else {
      newSelection.delete(matchId);
      // Nettoyer les sélections d'attributs/méthodes pour ce match
      const newSelectedAttributes = new Map(selectedAttributes);
      const newSelectedMethods = new Map(selectedMethods);
      newSelectedAttributes.delete(matchId);
      newSelectedMethods.delete(matchId);
      setSelectedAttributes(newSelectedAttributes);
      setSelectedMethods(newSelectedMethods);
    }
    setSelectedMatches(newSelection);
  };

  const handleAttributeSelection = (matchId: string, attribute: string, selected: boolean) => {
    const newSelectedAttributes = new Map(selectedAttributes);
    const currentAttributes = newSelectedAttributes.get(matchId) || new Set();

    if (selected) {
      currentAttributes.add(attribute);
    } else {
      currentAttributes.delete(attribute);
    }

    if (currentAttributes.size > 0) {
      newSelectedAttributes.set(matchId, currentAttributes);
    } else {
      newSelectedAttributes.delete(matchId);
    }

    setSelectedAttributes(newSelectedAttributes);
  };

  const handleMethodSelection = (matchId: string, method: string, selected: boolean) => {
    const newSelectedMethods = new Map(selectedMethods);
    const currentMethods = newSelectedMethods.get(matchId) || new Set();

    if (selected) {
      currentMethods.add(method);
    } else {
      currentMethods.delete(method);
    }

    if (currentMethods.size > 0) {
      newSelectedMethods.set(matchId, currentMethods);
    } else {
      newSelectedMethods.delete(matchId);
    }

    setSelectedMethods(newSelectedMethods);
  };

  // Fonction pour vérifier si tous les éléments d'un match sont sélectionnés
  const isAllSelectedForMatch = (match: HistoryMatch): boolean => {
    const matchId = match.historyItem.id;
    const matchingClass = match.matchingClasses[0];

    if (!matchingClass) return false;

    const selectedAttrs = selectedAttributes.get(matchId) || new Set<string>();
    const selectedMethodsSet = selectedMethods.get(matchId) || new Set<string>();

    const allAttrsSelected = matchingClass.attributes.every(attr => selectedAttrs.has(attr));
    const allMethodsSelected = matchingClass.methods.every(method => selectedMethodsSet.has(method));

    return allAttrsSelected && allMethodsSelected &&
           (matchingClass.attributes.length > 0 || matchingClass.methods.length > 0);
  };

  // Fonction pour cocher/décocher tous les éléments d'un match
  const handleCheckAll = (match: HistoryMatch, checked: boolean) => {
    const matchId = match.historyItem.id;
    const matchingClass = match.matchingClasses[0];

    if (!matchingClass) return;

    if (checked) {
      // Cocher tous les attributs et méthodes
      setSelectedAttributes(prev => {
        const newMap = new Map(prev);
        newMap.set(matchId, new Set(matchingClass.attributes));
        return newMap;
      });

      setSelectedMethods(prev => {
        const newMap = new Map(prev);
        newMap.set(matchId, new Set(matchingClass.methods));
        return newMap;
      });
    } else {
      // Décocher tous les attributs et méthodes
      setSelectedAttributes(prev => {
        const newMap = new Map(prev);
        newMap.set(matchId, new Set<string>());
        return newMap;
      });

      setSelectedMethods(prev => {
        const newMap = new Map(prev);
        newMap.set(matchId, new Set<string>());
        return newMap;
      });
    }
  };

  const handleImport = () => {
    // Construire les données à importer basées sur les sélections granulaires
    const attributesToImport: string[] = [];
    const methodsToImport: string[] = [];

    // Collecter tous les attributs et méthodes sélectionnés
    selectedAttributes.forEach((attributes, matchId) => {
      attributes.forEach(attr => {
        if (!attributesToImport.includes(attr)) {
          attributesToImport.push(attr);
        }
      });
    });

    selectedMethods.forEach((methods, matchId) => {
      methods.forEach(method => {
        if (!methodsToImport.includes(method)) {
          methodsToImport.push(method);
        }
      });
    });

    if (attributesToImport.length > 0 || methodsToImport.length > 0) {
      const importedData: ClassData = {
        name: currentClassData.name,
        attributes: attributesToImport,
        methods: methodsToImport
      };

      onImport(importedData);

      // Reset toutes les sélections
      setSelectedMatches(new Set());
      setSelectedAttributes(new Map());
      setSelectedMethods(new Map());
    }
  };

  const handlePreview = (matchId: string) => {
    if (showPreview === matchId) {
      setShowPreview(null);
      setPreviewData(null);
    } else {
      const match = matches.find(m => m.historyItem.id === matchId);
      setShowPreview(matchId);
      setPreviewData(match || null);
    }
  };

  const getAccessIcon = (match: HistoryMatch) => {
    const accessLevel = HistoryAnalysisService.getAccessLevel(match.historyItem, currentUser?.uid || '');

    switch (accessLevel) {
      case 'owner':
        return <span title={t('historyAnalysis.access.owner')}><Shield size={12} color="#10b981" /></span>;
      case 'shared':
        return <span title={t('historyAnalysis.access.shared')}><Users size={12} color="#f59e0b" /></span>;
      case 'public':
        return <span title={t('historyAnalysis.access.public')}><Eye size={12} color="#3b82f6" /></span>;
      default:
        return <span title={t('historyAnalysis.access.restricted')}><Lock size={12} color="#ef4444" /></span>;
    }
  };

  const getSectionStyles = () => ({
    container: {
      marginBottom: '24px',
      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)'}`,
      borderRadius: '12px',
      background: darkMode 
        ? 'linear-gradient(135deg, rgba(10, 15, 28, 0.8) 0%, rgba(30, 41, 59, 0.6) 100%)' 
        : 'linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(255, 255, 255, 0.9) 100%)',
      backdropFilter: 'blur(10px)',
      WebkitBackdropFilter: 'blur(10px)',
    },
    header: {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: '16px 20px',
      cursor: 'pointer',
      borderBottom: isExpanded ? `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.15)' : 'rgba(59, 130, 246, 0.08)'}` : 'none',
    },
    headerLeft: {
      display: 'flex',
      alignItems: 'center',
      gap: '12px',
    },
    title: {
      fontSize: '16px',
      fontWeight: '600',
      color: darkMode ? '#f8fafc' : '#0f172a',
      margin: 0,
    },
    badge: {
      backgroundColor: matches.length > 0 ? '#3b82f6' : darkMode ? '#64748b' : '#94a3b8',
      color: '#ffffff',
      fontSize: '12px',
      fontWeight: '600',
      padding: '4px 8px',
      borderRadius: '12px',
      minWidth: '20px',
      textAlign: 'center' as const,
    },
    expandIcon: {
      color: darkMode ? '#94a3b8' : '#64748b',
      transition: 'transform 0.2s ease',
      transform: isExpanded ? 'rotate(180deg)' : 'rotate(0deg)',
    },
    content: {
      padding: isExpanded ? '20px' : '0',
      maxHeight: isExpanded ? '400px' : '0',
      overflow: 'hidden' as const,
      transition: 'all 0.3s ease',
    },
    sortContainer: {
      display: 'flex',
      alignItems: 'center',
      gap: '12px',
      marginBottom: '16px',
    },
    sortLabel: {
      fontSize: '14px',
      color: darkMode ? '#cbd5e1' : '#64748b',
      fontWeight: '500',
    },
    sortSelect: {
      backgroundColor: darkMode ? 'rgba(30, 41, 59, 0.8)' : 'rgba(248, 250, 252, 0.8)',
      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)'}`,
      borderRadius: '8px',
      padding: '6px 12px',
      fontSize: '14px',
      color: darkMode ? '#e2e8f0' : '#374151',
      cursor: 'pointer',
    },
    matchesList: {
      maxHeight: '250px',
      overflowY: 'auto' as const,
      marginBottom: '16px',
    },
    matchItem: {
      display: 'flex',
      alignItems: 'flex-start',
      flexDirection: 'column' as const,
      gap: '12px',
      padding: '12px',
      marginBottom: '8px',
      backgroundColor: darkMode ? 'rgba(30, 41, 59, 0.4)' : 'rgba(248, 250, 252, 0.6)',
      borderRadius: '8px',
      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.1)' : 'rgba(59, 130, 246, 0.05)'}`,
      transition: 'all 0.2s ease',
    },
    matchItemHeader: {
      display: 'flex',
      alignItems: 'center',
      gap: '12px',
      width: '100%',
    },
    checkbox: {
      width: '16px',
      height: '16px',
      accentColor: '#3b82f6',
      cursor: 'pointer',
    },
    matchInfo: {
      flex: 1,
    },
    matchTitle: {
      fontSize: '14px',
      fontWeight: '500',
      color: darkMode ? '#e2e8f0' : '#374151',
      marginBottom: '4px',
    },
    matchMeta: {
      fontSize: '12px',
      color: darkMode ? '#94a3b8' : '#64748b',
      display: 'flex',
      alignItems: 'center',
      gap: '8px',
    },
    similarityBadge: {
      backgroundColor: '#10b981',
      color: '#ffffff',
      fontSize: '11px',
      fontWeight: '600',
      padding: '2px 6px',
      borderRadius: '8px',
    },
    actionButtons: {
      display: 'flex',
      gap: '8px',
    },
    actionButton: {
      backgroundColor: 'transparent',
      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.3)' : 'rgba(59, 130, 246, 0.2)'}`,
      borderRadius: '6px',
      padding: '6px',
      cursor: 'pointer',
      color: darkMode ? '#60a5fa' : '#3b82f6',
      transition: 'all 0.2s ease',
    },
    conflictsWarning: {
      backgroundColor: darkMode ? 'rgba(245, 158, 11, 0.1)' : 'rgba(245, 158, 11, 0.05)',
      border: `1px solid ${darkMode ? 'rgba(245, 158, 11, 0.3)' : 'rgba(245, 158, 11, 0.2)'}`,
      borderRadius: '8px',
      padding: '12px',
      marginBottom: '16px',
      display: 'flex',
      alignItems: 'center',
      gap: '8px',
    },
    conflictsText: {
      fontSize: '13px',
      color: darkMode ? '#fbbf24' : '#d97706',
      fontWeight: '500',
    },
    importButton: {
      backgroundColor: selectedMatches.size > 0 ? '#3b82f6' : darkMode ? '#374151' : '#9ca3af',
      color: '#ffffff',
      border: 'none',
      borderRadius: '8px',
      padding: '10px 16px',
      fontSize: '14px',
      fontWeight: '600',
      cursor: selectedMatches.size > 0 ? 'pointer' : 'not-allowed',
      transition: 'all 0.2s ease',
      display: 'flex',
      alignItems: 'center',
      gap: '8px',
    },
    emptyState: {
      textAlign: 'center' as const,
      color: darkMode ? '#64748b' : '#9ca3af',
      fontSize: '14px',
      fontStyle: 'italic',
      padding: '20px',
    },
    previewContainer: {
      backgroundColor: darkMode ? 'rgba(15, 23, 42, 0.8)' : 'rgba(248, 250, 252, 0.9)',
      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)'}`,
      borderRadius: '8px',
      padding: '16px',
      marginTop: '12px',
      animation: 'slideDown 0.3s ease',
    },
    previewHeader: {
      display: 'flex',
      alignItems: 'center',
      gap: '8px',
      marginBottom: '12px',
      fontSize: '14px',
      fontWeight: '600',
      color: darkMode ? '#e2e8f0' : '#374151',
    },
    previewContent: {
      display: 'grid',
      gridTemplateColumns: '1fr 1fr',
      gap: '16px',
    },
    previewSection: {
      fontSize: '13px',
      color: darkMode ? '#cbd5e1' : '#64748b',
    },
    previewLabel: {
      fontWeight: '600',
      marginBottom: '4px',
      color: darkMode ? '#f1f5f9' : '#374151',
    },
    relatedClassesList: {
      display: 'flex',
      flexWrap: 'wrap' as const,
      gap: '4px',
      marginTop: '4px',
    },
    relatedClassTag: {
      backgroundColor: darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)',
      color: darkMode ? '#93c5fd' : '#3b82f6',
      fontSize: '11px',
      padding: '2px 6px',
      borderRadius: '4px',
      fontWeight: '500',
    },

    // Styles pour la sélection granulaire
    granularSelection: {
      marginTop: '12px',
      padding: '12px',
      backgroundColor: darkMode ? 'rgba(59, 130, 246, 0.05)' : 'rgba(59, 130, 246, 0.02)',
      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.1)' : 'rgba(59, 130, 246, 0.08)'}`,
      borderRadius: '8px',
    },

    granularHeader: {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      fontSize: '13px',
      fontWeight: '600',
      color: darkMode ? '#e2e8f0' : '#374151',
      marginBottom: '8px',
    },

    checkAllLabel: {
      display: 'flex',
      alignItems: 'center',
      gap: '6px',
      fontSize: '12px',
      fontWeight: '500',
      color: darkMode ? '#60a5fa' : '#3b82f6', // Couleur bleue harmonisée avec le thème
      cursor: 'pointer',
      transition: 'color 0.2s ease',
    },

    checkAllCheckbox: {
      width: '14px',
      height: '14px',
      cursor: 'pointer',
      accentColor: darkMode ? '#60a5fa' : '#3b82f6', // Couleur de la checkbox harmonisée
    },

    granularTitle: {
      fontSize: '13px',
      fontWeight: '600',
      color: darkMode ? '#e2e8f0' : '#374151',
    },

    granularContent: {
      display: 'flex',
      flexDirection: 'column' as const,
      gap: '12px',
    },

    granularSection: {
      display: 'flex',
      flexDirection: 'column' as const,
      gap: '6px',
    },

    granularSectionTitle: {
      fontSize: '12px',
      fontWeight: '600',
      color: darkMode ? '#94a3b8' : '#6b7280',
      textTransform: 'uppercase' as const,
      letterSpacing: '0.5px',
    },

    granularItems: {
      display: 'flex',
      flexDirection: 'column' as const,
      gap: '4px',
      paddingLeft: '8px',
    },

    granularItem: {
      display: 'flex',
      alignItems: 'center',
      gap: '8px',
      cursor: 'pointer',
      padding: '4px 0',
    },

    granularCheckbox: {
      width: '14px',
      height: '14px',
      cursor: 'pointer',
    },

    granularItemText: {
      fontSize: '12px',
      color: darkMode ? '#cbd5e1' : '#4b5563',
      fontFamily: 'monospace',
    }
  });

  const styles = getSectionStyles();

  if (!currentUser) return null;

  return (
    <div style={styles.container}>
      <div style={styles.header} onClick={handleToggleExpand}>
        <div style={styles.headerLeft}>
          <Zap size={18} color={darkMode ? '#60a5fa' : '#3b82f6'} />
          <h4 style={styles.title}>{t('historyAnalysis.title')}</h4>
          <span style={styles.badge}>{matches.length}</span>
        </div>
        <ChevronDown size={20} style={styles.expandIcon} />
      </div>
      
      {isExpanded && (
        <div style={styles.content}>
          {matches.length === 0 ? (
            <div style={styles.emptyState}>
              Aucun diagramme historique trouvé pour la classe "{targetClassName}"
            </div>
          ) : (
            <>
              <div style={styles.sortContainer}>
                <span style={styles.sortLabel}>Trier par:</span>
                <select 
                  style={styles.sortSelect}
                  value={`${sortOption.key}-${sortOption.direction}`}
                  onChange={(e) => {
                    const [key, direction] = e.target.value.split('-');
                    const option = HistoryAnalysisService.getSortOptions().find(
                      opt => opt.key === key && opt.direction === direction
                    );
                    if (option) setSortOption(option);
                  }}
                >
                  {HistoryAnalysisService.getSortOptions().map(option => (
                    <option key={`${option.key}-${option.direction}`} value={`${option.key}-${option.direction}`}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              {conflicts.attributes.length > 0 || conflicts.methods.length > 0 ? (
                <div style={styles.conflictsWarning}>
                  <AlertTriangle size={16} />
                  <span style={styles.conflictsText}>
                    Conflits détectés: {conflicts.attributes.length} attribut(s), {conflicts.methods.length} méthode(s)
                  </span>
                </div>
              ) : null}

              <div style={styles.matchesList}>
                {matches.map(match => (
                  <div key={match.historyItem.id} style={styles.matchItem} className="history-match-item">
                    <div style={styles.matchItemHeader}>
                      <input
                        type="checkbox"
                        style={styles.checkbox}
                        checked={selectedMatches.has(match.historyItem.id)}
                        onChange={(e) => handleMatchSelection(match.historyItem.id, e.target.checked)}
                      />
                      <div style={styles.matchInfo}>
                        <div style={styles.matchTitle}>{match.historyItem.title}</div>
                        <div style={styles.matchMeta} className="history-match-meta">
                          <Clock size={12} />
                          {match.historyItem.createdAt.toLocaleDateString('fr-FR')}
                          <span style={styles.similarityBadge} className="history-similarity-badge">
                            {Math.round(match.similarity)}%
                          </span>
                          {getAccessIcon(match)}
                          {match.isShared && <span style={{ fontSize: '11px', color: darkMode ? '#fbbf24' : '#d97706' }}>Partagé</span>}
                        </div>
                      </div>
                      <div style={styles.actionButtons}>
                        <button
                          style={styles.actionButton}
                          className="history-action-button"
                          onClick={() => handlePreview(match.historyItem.id)}
                          title="Voir aperçu"
                        >
                          <Eye size={14} />
                        </button>
                      </div>
                    </div>

                    {/* Aperçu de la classe */}
                    {showPreview === match.historyItem.id && previewData && (
                      <div style={styles.previewContainer} className="history-preview-container">
                        <div style={styles.previewHeader}>
                          <Eye size={16} />
                          Aperçu de la classe "{match.matchingClasses[0]?.name}"
                        </div>
                        <div style={styles.previewContent} className="history-preview-content">
                          <div style={styles.previewSection}>
                            <div style={styles.previewLabel}>Contenu de la classe:</div>
                            <div>{previewData.previewData?.classContext}</div>
                          </div>
                          <div style={styles.previewSection}>
                            <div style={styles.previewLabel}>Classes liées:</div>
                            <div style={styles.relatedClassesList} className="history-related-classes-list">
                              {previewData.previewData?.relatedClasses.map((className, idx) => (
                                <span key={idx} style={styles.relatedClassTag}>
                                  {className}
                                </span>
                              ))}
                              {(!previewData.previewData?.relatedClasses || previewData.previewData.relatedClasses.length === 0) && (
                                <span style={{ fontStyle: 'italic', color: darkMode ? '#64748b' : '#9ca3af' }}>
                                  Aucune classe liée détectée
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Sélection granulaire des attributs et méthodes */}
                    {selectedMatches.has(match.historyItem.id) && (
                      <div style={styles.granularSelection} className="history-granular-selection">
                        <div style={styles.granularHeader}>
                          <span style={styles.granularTitle}>Sélectionner les éléments à importer :</span>
                          <label
                            style={styles.checkAllLabel}
                            onMouseEnter={(e) => {
                              e.currentTarget.style.color = darkMode ? '#93c5fd' : '#2563eb';
                            }}
                            onMouseLeave={(e) => {
                              e.currentTarget.style.color = darkMode ? '#60a5fa' : '#3b82f6';
                            }}
                          >
                            <input
                              type="checkbox"
                              checked={isAllSelectedForMatch(match)}
                              onChange={(e) => handleCheckAll(match, e.target.checked)}
                              style={styles.checkAllCheckbox}
                            />
                            <span>Cocher tous</span>
                          </label>
                        </div>

                        <div style={styles.granularContent}>
                          {/* Attributs */}
                          {match.matchingClasses[0]?.attributes && match.matchingClasses[0].attributes.length > 0 && (
                            <div style={styles.granularSection}>
                              <div style={styles.granularSectionTitle}>Attributs :</div>
                              <div style={styles.granularItems}>
                                {match.matchingClasses[0].attributes.map((attribute, idx) => (
                                  <label key={idx} style={styles.granularItem}>
                                    <input
                                      type="checkbox"
                                      style={styles.granularCheckbox}
                                      checked={selectedAttributes.get(match.historyItem.id)?.has(attribute) || false}
                                      onChange={(e) => handleAttributeSelection(match.historyItem.id, attribute, e.target.checked)}
                                    />
                                    <span style={styles.granularItemText}>{attribute}</span>
                                  </label>
                                ))}
                              </div>
                            </div>
                          )}

                          {/* Méthodes */}
                          {match.matchingClasses[0]?.methods && match.matchingClasses[0].methods.length > 0 && (
                            <div style={styles.granularSection}>
                              <div style={styles.granularSectionTitle}>Méthodes :</div>
                              <div style={styles.granularItems}>
                                {match.matchingClasses[0].methods.map((method, idx) => (
                                  <label key={idx} style={styles.granularItem}>
                                    <input
                                      type="checkbox"
                                      style={styles.granularCheckbox}
                                      checked={selectedMethods.get(match.historyItem.id)?.has(method) || false}
                                      onChange={(e) => handleMethodSelection(match.historyItem.id, method, e.target.checked)}
                                    />
                                    <span style={styles.granularItemText}>{method}</span>
                                  </label>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>

              <button
                style={styles.importButton}
                onClick={handleImport}
                disabled={(() => {
                  const totalAttributes = Array.from(selectedAttributes.values()).reduce((sum, attrs) => sum + attrs.size, 0);
                  const totalMethods = Array.from(selectedMethods.values()).reduce((sum, methods) => sum + methods.size, 0);
                  return totalAttributes + totalMethods === 0;
                })()}
              >
                <Zap size={16} />
                {(() => {
                  const totalAttributes = Array.from(selectedAttributes.values()).reduce((sum, attrs) => sum + attrs.size, 0);
                  const totalMethods = Array.from(selectedMethods.values()).reduce((sum, methods) => sum + methods.size, 0);
                  const totalElements = totalAttributes + totalMethods;

                  if (totalElements === 0) {
                    return `Importer (${selectedMatches.size} diagramme${selectedMatches.size > 1 ? 's' : ''} sélectionné${selectedMatches.size > 1 ? 's' : ''})`;
                  }

                  return `Importer (${totalElements} élément${totalElements > 1 ? 's' : ''} sélectionné${totalElements > 1 ? 's' : ''})`;
                })()}
              </button>
            </>
          )}
        </div>
      )}
    </div>
  );
};

export default HistoryAnalysisSection;
