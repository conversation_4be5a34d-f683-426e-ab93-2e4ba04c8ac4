{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\FixTorchUMLDGM\\\\uml-detector\\\\src\\\\components\\\\DetectionArrow.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { useLanguage } from \"../context/LanguageContext\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst DetectionArrow = ({\n  darkMode,\n  annotatedImage\n}) => {\n  _s();\n  const [imageLoading, setImageLoading] = useState(true);\n  const [imageError, setImageError] = useState(false);\n  const {\n    t\n  } = useLanguage();\n  const styles = {\n    container: {\n      maxWidth: \"800px\",\n      margin: \"0 auto\",\n      padding: \"20px\",\n      color: darkMode ? \"#e0e0e0\" : \"#333\"\n    },\n    header: {\n      textAlign: \"center\",\n      marginBottom: \"20px\"\n    },\n    imageContainer: {\n      display: \"flex\",\n      justifyContent: \"center\",\n      marginBottom: \"20px\"\n    },\n    annotatedImage: {\n      maxWidth: \"100%\",\n      borderRadius: \"8px\",\n      boxShadow: darkMode ? \"0 4px 8px rgba(0,0,0,0.5)\" : \"0 4px 8px rgba(0,0,0,0.1)\"\n    },\n    loadingContainer: {\n      textAlign: \"center\",\n      padding: \"40px\",\n      backgroundColor: darkMode ? \"#2d2d2d\" : \"#f5f5f5\",\n      borderRadius: \"8px\",\n      margin: \"20px auto\",\n      maxWidth: \"600px\"\n    },\n    errorContainer: {\n      textAlign: \"center\",\n      padding: \"40px\",\n      backgroundColor: darkMode ? \"#402020\" : \"#fff5f5\",\n      borderRadius: \"8px\",\n      margin: \"20px auto\",\n      maxWidth: \"600px\",\n      border: `1px solid ${darkMode ? \"#ff6b6b\" : \"#f56565\"}`\n    },\n    tip: {\n      backgroundColor: darkMode ? \"#333\" : \"#f8f9fa\",\n      padding: \"15px\",\n      borderRadius: \"8px\",\n      marginTop: \"30px\",\n      border: `1px solid ${darkMode ? \"#444\" : \"#e0e0e0\"}`\n    },\n    tipTitle: {\n      fontWeight: \"600\",\n      marginBottom: \"8px\",\n      display: \"flex\",\n      alignItems: \"center\",\n      gap: \"8px\"\n    },\n    explanationContainer: {\n      backgroundColor: darkMode ? \"#333\" : \"#fff\",\n      borderRadius: \"8px\",\n      padding: \"20px\",\n      marginTop: \"30px\",\n      boxShadow: darkMode ? \"0 2px 10px rgba(0,0,0,0.5)\" : \"0 2px 10px rgba(0,0,0,0.1)\"\n    },\n    explanationTitle: {\n      fontSize: \"18px\",\n      fontWeight: \"600\",\n      marginBottom: \"15px\"\n    },\n    explanationText: {\n      lineHeight: \"1.6\"\n    },\n    legendContainer: {\n      display: \"flex\",\n      flexWrap: \"wrap\",\n      gap: \"10px\",\n      marginTop: \"20px\"\n    },\n    legendItem: {\n      display: \"flex\",\n      alignItems: \"center\",\n      gap: \"8px\"\n    },\n    legendColor: {\n      width: \"20px\",\n      height: \"20px\",\n      borderRadius: \"4px\"\n    }\n  };\n  const handleImageLoad = () => {\n    setImageLoading(false);\n    setImageError(false);\n  };\n  const handleImageError = () => {\n    setImageLoading(false);\n    setImageError(true);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: styles.container,\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      style: styles.header,\n      children: t('detectionArrow.title')\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 7\n    }, this), annotatedImage ? /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [imageLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.loadingContainer,\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: t('detectionArrow.loading')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 13\n      }, this), imageError ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.errorContainer,\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: t('detectionArrow.error')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"URL: \", annotatedImage]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.imageContainer,\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: annotatedImage,\n          alt: \"Diagramme UML annot\\xE9 avec relations d\\xE9tect\\xE9es\",\n          style: {\n            ...styles.annotatedImage,\n            display: imageLoading ? \"none\" : \"block\"\n          },\n          onLoad: handleImageLoad,\n          onError: handleImageError\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.explanationContainer,\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: styles.explanationTitle,\n          children: \"Analyse des Relations\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: styles.explanationText,\n          children: \"L'image ci-dessus montre les relations d\\xE9tect\\xE9es entre les diff\\xE9rentes classes de votre diagramme UML. Notre syst\\xE8me d'IA a identifi\\xE9 les connexions et les a marqu\\xE9es selon leur type.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.legendContainer,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.legendItem,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                ...styles.legendColor,\n                backgroundColor: \"#4CAF50\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Classe\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.legendItem,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                ...styles.legendColor,\n                backgroundColor: \"#FF0000\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Relation\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.legendItem,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                ...styles.legendColor,\n                backgroundColor: \"#0000FF\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Agr\\xE9gation\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.legendItem,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                ...styles.legendColor,\n                backgroundColor: \"#00FFFF\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Composition\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.legendItem,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                ...styles.legendColor,\n                backgroundColor: \"#9C27B0\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"G\\xE9n\\xE9ralisation\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.legendItem,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                ...styles.legendColor,\n                backgroundColor: \"rgb(173, 208, 149)\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Association\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.tip,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.tipTitle,\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"20\",\n            height: \"20\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: darkMode ? \"#e0e0e0\" : \"#333\",\n            strokeWidth: \"2\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n              cx: \"12\",\n              cy: \"12\",\n              r: \"10\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n              x1: \"12\",\n              y1: \"16\",\n              x2: \"12\",\n              y2: \"12\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n              x1: \"12\",\n              y1: \"8\",\n              x2: \"12.01\",\n              y2: \"8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 15\n          }, this), \"Astuce\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Pour une analyse plus d\\xE9taill\\xE9e, consultez l'onglet \\\"Extraction de texte UML\\\" qui contient les informations textuelles compl\\xE8tes de toutes les classes d\\xE9tect\\xE9es.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.loadingContainer,\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Aucune image annot\\xE9e n'est disponible. Veuillez d'abord t\\xE9l\\xE9charger et analyser une image de diagramme UML.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 111,\n    columnNumber: 5\n  }, this);\n};\n_s(DetectionArrow, \"/fzIe62V6Tszy7RB08X2GQXaSVQ=\", false, function () {\n  return [useLanguage];\n});\n_c = DetectionArrow;\nexport default DetectionArrow;\nvar _c;\n$RefreshReg$(_c, \"DetectionArrow\");", "map": {"version": 3, "names": ["React", "useState", "useLanguage", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "DetectionArrow", "darkMode", "annotatedImage", "_s", "imageLoading", "setImageLoading", "imageError", "setImageError", "t", "styles", "container", "max<PERSON><PERSON><PERSON>", "margin", "padding", "color", "header", "textAlign", "marginBottom", "imageContainer", "display", "justifyContent", "borderRadius", "boxShadow", "loadingContainer", "backgroundColor", "<PERSON><PERSON><PERSON><PERSON>", "border", "tip", "marginTop", "tipTitle", "fontWeight", "alignItems", "gap", "<PERSON><PERSON><PERSON><PERSON>", "explanationTitle", "fontSize", "explanationText", "lineHeight", "<PERSON><PERSON><PERSON><PERSON>", "flexWrap", "legendItem", "legendColor", "width", "height", "handleImageLoad", "handleImageError", "style", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "alt", "onLoad", "onError", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "cx", "cy", "r", "x1", "y1", "x2", "y2", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/FixTorchUMLDGM/uml-detector/src/components/DetectionArrow.tsx"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport { useLanguage } from \"../context/LanguageContext\";\r\n\r\ninterface DetectionArrowProps {\r\n  darkMode: boolean;\r\n  annotatedImage: string | null;\r\n}\r\n\r\nconst DetectionArrow: React.FC<DetectionArrowProps> = ({ darkMode, annotatedImage }) => {\r\n  const [imageLoading, setImageLoading] = useState(true);\r\n  const [imageError, setImageError] = useState(false);\r\n\r\n  const { t } = useLanguage();\r\n\r\n  const styles = {\r\n    container: {\r\n      maxWidth: \"800px\",\r\n      margin: \"0 auto\",\r\n      padding: \"20px\",\r\n      color: darkMode ? \"#e0e0e0\" : \"#333\"\r\n    },\r\n    header: {\r\n      textAlign: \"center\" as const,\r\n      marginBottom: \"20px\"\r\n    },\r\n    imageContainer: {\r\n      display: \"flex\",\r\n      justifyContent: \"center\",\r\n      marginBottom: \"20px\"\r\n    },\r\n    annotatedImage: {\r\n      maxWidth: \"100%\",\r\n      borderRadius: \"8px\",\r\n      boxShadow: darkMode ? \"0 4px 8px rgba(0,0,0,0.5)\" : \"0 4px 8px rgba(0,0,0,0.1)\"\r\n    },\r\n    loadingContainer: {\r\n      textAlign: \"center\" as const,\r\n      padding: \"40px\",\r\n      backgroundColor: darkMode ? \"#2d2d2d\" : \"#f5f5f5\",\r\n      borderRadius: \"8px\",\r\n      margin: \"20px auto\",\r\n      maxWidth: \"600px\"\r\n    },\r\n    errorContainer: {\r\n      textAlign: \"center\" as const,\r\n      padding: \"40px\",\r\n      backgroundColor: darkMode ? \"#402020\" : \"#fff5f5\",\r\n      borderRadius: \"8px\",\r\n      margin: \"20px auto\",\r\n      maxWidth: \"600px\",\r\n      border: `1px solid ${darkMode ? \"#ff6b6b\" : \"#f56565\"}`\r\n    },\r\n    tip: {\r\n      backgroundColor: darkMode ? \"#333\" : \"#f8f9fa\",\r\n      padding: \"15px\",\r\n      borderRadius: \"8px\",\r\n      marginTop: \"30px\",\r\n      border: `1px solid ${darkMode ? \"#444\" : \"#e0e0e0\"}`\r\n    },\r\n    tipTitle: {\r\n      fontWeight: \"600\",\r\n      marginBottom: \"8px\",\r\n      display: \"flex\",\r\n      alignItems: \"center\",\r\n      gap: \"8px\"\r\n    },\r\n    explanationContainer: {\r\n      backgroundColor: darkMode ? \"#333\" : \"#fff\",\r\n      borderRadius: \"8px\",\r\n      padding: \"20px\",\r\n      marginTop: \"30px\",\r\n      boxShadow: darkMode ? \"0 2px 10px rgba(0,0,0,0.5)\" : \"0 2px 10px rgba(0,0,0,0.1)\"\r\n    },\r\n    explanationTitle: {\r\n      fontSize: \"18px\",\r\n      fontWeight: \"600\",\r\n      marginBottom: \"15px\"\r\n    },\r\n    explanationText: {\r\n      lineHeight: \"1.6\"\r\n    },\r\n    legendContainer: {\r\n      display: \"flex\",\r\n      flexWrap: \"wrap\" as const,\r\n      gap: \"10px\",\r\n      marginTop: \"20px\"\r\n    },\r\n    legendItem: {\r\n      display: \"flex\",\r\n      alignItems: \"center\",\r\n      gap: \"8px\"\r\n    },\r\n    legendColor: {\r\n      width: \"20px\",\r\n      height: \"20px\",\r\n      borderRadius: \"4px\"\r\n    }\r\n  };\r\n\r\n  const handleImageLoad = () => {\r\n    setImageLoading(false);\r\n    setImageError(false);\r\n  };\r\n\r\n  const handleImageError = () => {\r\n    setImageLoading(false);\r\n    setImageError(true);\r\n  };\r\n\r\n  return (\r\n    <div style={styles.container}>\r\n      <h2 style={styles.header}>{t('detectionArrow.title')}</h2>\r\n\r\n      {annotatedImage ? (\r\n        <>\r\n          {imageLoading && (\r\n            <div style={styles.loadingContainer}>\r\n              <p>{t('detectionArrow.loading')}</p>\r\n            </div>\r\n          )}\r\n\r\n          {imageError ? (\r\n            <div style={styles.errorContainer}>\r\n              <p>{t('detectionArrow.error')}</p>\r\n              <p>URL: {annotatedImage}</p>\r\n            </div>\r\n          ) : (\r\n            <div style={styles.imageContainer}>\r\n              <img\r\n                src={annotatedImage}\r\n                alt=\"Diagramme UML annoté avec relations détectées\"\r\n                style={{\r\n                  ...styles.annotatedImage,\r\n                  display: imageLoading ? \"none\" : \"block\"\r\n                }}\r\n                onLoad={handleImageLoad}\r\n                onError={handleImageError}\r\n              />\r\n            </div>\r\n          )}\r\n\r\n          <div style={styles.explanationContainer}>\r\n            <h3 style={styles.explanationTitle}>Analyse des Relations</h3>\r\n            <p style={styles.explanationText}>\r\n              L'image ci-dessus montre les relations détectées entre les différentes classes de votre diagramme UML.\r\n              Notre système d'IA a identifié les connexions et les a marquées selon leur type.\r\n            </p>\r\n\r\n            <div style={styles.legendContainer}>\r\n              <div style={styles.legendItem}>\r\n                <div style={{...styles.legendColor, backgroundColor: \"#4CAF50\"}}></div>\r\n                <span>Classe</span>\r\n              </div>\r\n              <div style={styles.legendItem}>\r\n                <div style={{...styles.legendColor, backgroundColor: \"#FF0000\"}}></div>\r\n                <span>Relation</span>\r\n              </div>\r\n              <div style={styles.legendItem}>\r\n                <div style={{...styles.legendColor, backgroundColor: \"#0000FF\"}}></div>\r\n                <span>Agrégation</span>\r\n              </div>\r\n              <div style={styles.legendItem}>\r\n                <div style={{...styles.legendColor, backgroundColor: \"#00FFFF\"}}></div>\r\n                <span>Composition</span>\r\n              </div>\r\n              <div style={styles.legendItem}>\r\n                <div style={{...styles.legendColor, backgroundColor: \"#9C27B0\"}}></div>\r\n                <span>Généralisation</span>\r\n              </div>\r\n              <div style={styles.legendItem}>\r\n                <div style={{...styles.legendColor, backgroundColor: \"rgb(173, 208, 149)\"}}></div>\r\n                <span>Association</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div style={styles.tip}>\r\n            <div style={styles.tipTitle}>\r\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke={darkMode ? \"#e0e0e0\" : \"#333\"} strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\r\n                <circle cx=\"12\" cy=\"12\" r=\"10\"></circle>\r\n                <line x1=\"12\" y1=\"16\" x2=\"12\" y2=\"12\"></line>\r\n                <line x1=\"12\" y1=\"8\" x2=\"12.01\" y2=\"8\"></line>\r\n              </svg>\r\n              Astuce\r\n            </div>\r\n            <p>Pour une analyse plus détaillée, consultez l'onglet \"Extraction de texte UML\" qui contient les informations textuelles complètes de toutes les classes détectées.</p>\r\n          </div>\r\n        </>\r\n      ) : (\r\n        <div style={styles.loadingContainer}>\r\n          <p>Aucune image annotée n'est disponible. Veuillez d'abord télécharger et analyser une image de diagramme UML.</p>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DetectionArrow;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAOzD,MAAMC,cAA6C,GAAGA,CAAC;EAAEC,QAAQ;EAAEC;AAAe,CAAC,KAAK;EAAAC,EAAA;EACtF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACY,UAAU,EAAEC,aAAa,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EAEnD,MAAM;IAAEc;EAAE,CAAC,GAAGb,WAAW,CAAC,CAAC;EAE3B,MAAMc,MAAM,GAAG;IACbC,SAAS,EAAE;MACTC,QAAQ,EAAE,OAAO;MACjBC,MAAM,EAAE,QAAQ;MAChBC,OAAO,EAAE,MAAM;MACfC,KAAK,EAAEb,QAAQ,GAAG,SAAS,GAAG;IAChC,CAAC;IACDc,MAAM,EAAE;MACNC,SAAS,EAAE,QAAiB;MAC5BC,YAAY,EAAE;IAChB,CAAC;IACDC,cAAc,EAAE;MACdC,OAAO,EAAE,MAAM;MACfC,cAAc,EAAE,QAAQ;MACxBH,YAAY,EAAE;IAChB,CAAC;IACDf,cAAc,EAAE;MACdS,QAAQ,EAAE,MAAM;MAChBU,YAAY,EAAE,KAAK;MACnBC,SAAS,EAAErB,QAAQ,GAAG,2BAA2B,GAAG;IACtD,CAAC;IACDsB,gBAAgB,EAAE;MAChBP,SAAS,EAAE,QAAiB;MAC5BH,OAAO,EAAE,MAAM;MACfW,eAAe,EAAEvB,QAAQ,GAAG,SAAS,GAAG,SAAS;MACjDoB,YAAY,EAAE,KAAK;MACnBT,MAAM,EAAE,WAAW;MACnBD,QAAQ,EAAE;IACZ,CAAC;IACDc,cAAc,EAAE;MACdT,SAAS,EAAE,QAAiB;MAC5BH,OAAO,EAAE,MAAM;MACfW,eAAe,EAAEvB,QAAQ,GAAG,SAAS,GAAG,SAAS;MACjDoB,YAAY,EAAE,KAAK;MACnBT,MAAM,EAAE,WAAW;MACnBD,QAAQ,EAAE,OAAO;MACjBe,MAAM,EAAE,aAAazB,QAAQ,GAAG,SAAS,GAAG,SAAS;IACvD,CAAC;IACD0B,GAAG,EAAE;MACHH,eAAe,EAAEvB,QAAQ,GAAG,MAAM,GAAG,SAAS;MAC9CY,OAAO,EAAE,MAAM;MACfQ,YAAY,EAAE,KAAK;MACnBO,SAAS,EAAE,MAAM;MACjBF,MAAM,EAAE,aAAazB,QAAQ,GAAG,MAAM,GAAG,SAAS;IACpD,CAAC;IACD4B,QAAQ,EAAE;MACRC,UAAU,EAAE,KAAK;MACjBb,YAAY,EAAE,KAAK;MACnBE,OAAO,EAAE,MAAM;MACfY,UAAU,EAAE,QAAQ;MACpBC,GAAG,EAAE;IACP,CAAC;IACDC,oBAAoB,EAAE;MACpBT,eAAe,EAAEvB,QAAQ,GAAG,MAAM,GAAG,MAAM;MAC3CoB,YAAY,EAAE,KAAK;MACnBR,OAAO,EAAE,MAAM;MACfe,SAAS,EAAE,MAAM;MACjBN,SAAS,EAAErB,QAAQ,GAAG,4BAA4B,GAAG;IACvD,CAAC;IACDiC,gBAAgB,EAAE;MAChBC,QAAQ,EAAE,MAAM;MAChBL,UAAU,EAAE,KAAK;MACjBb,YAAY,EAAE;IAChB,CAAC;IACDmB,eAAe,EAAE;MACfC,UAAU,EAAE;IACd,CAAC;IACDC,eAAe,EAAE;MACfnB,OAAO,EAAE,MAAM;MACfoB,QAAQ,EAAE,MAAe;MACzBP,GAAG,EAAE,MAAM;MACXJ,SAAS,EAAE;IACb,CAAC;IACDY,UAAU,EAAE;MACVrB,OAAO,EAAE,MAAM;MACfY,UAAU,EAAE,QAAQ;MACpBC,GAAG,EAAE;IACP,CAAC;IACDS,WAAW,EAAE;MACXC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,MAAM;MACdtB,YAAY,EAAE;IAChB;EACF,CAAC;EAED,MAAMuB,eAAe,GAAGA,CAAA,KAAM;IAC5BvC,eAAe,CAAC,KAAK,CAAC;IACtBE,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,MAAMsC,gBAAgB,GAAGA,CAAA,KAAM;IAC7BxC,eAAe,CAAC,KAAK,CAAC;IACtBE,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,oBACEV,OAAA;IAAKiD,KAAK,EAAErC,MAAM,CAACC,SAAU;IAAAqC,QAAA,gBAC3BlD,OAAA;MAAIiD,KAAK,EAAErC,MAAM,CAACM,MAAO;MAAAgC,QAAA,EAAEvC,CAAC,CAAC,sBAAsB;IAAC;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,EAEzDjD,cAAc,gBACbL,OAAA,CAAAE,SAAA;MAAAgD,QAAA,GACG3C,YAAY,iBACXP,OAAA;QAAKiD,KAAK,EAAErC,MAAM,CAACc,gBAAiB;QAAAwB,QAAA,eAClClD,OAAA;UAAAkD,QAAA,EAAIvC,CAAC,CAAC,wBAAwB;QAAC;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CACN,EAEA7C,UAAU,gBACTT,OAAA;QAAKiD,KAAK,EAAErC,MAAM,CAACgB,cAAe;QAAAsB,QAAA,gBAChClD,OAAA;UAAAkD,QAAA,EAAIvC,CAAC,CAAC,sBAAsB;QAAC;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClCtD,OAAA;UAAAkD,QAAA,GAAG,OAAK,EAAC7C,cAAc;QAAA;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,gBAENtD,OAAA;QAAKiD,KAAK,EAAErC,MAAM,CAACS,cAAe;QAAA6B,QAAA,eAChClD,OAAA;UACEuD,GAAG,EAAElD,cAAe;UACpBmD,GAAG,EAAC,wDAA+C;UACnDP,KAAK,EAAE;YACL,GAAGrC,MAAM,CAACP,cAAc;YACxBiB,OAAO,EAAEf,YAAY,GAAG,MAAM,GAAG;UACnC,CAAE;UACFkD,MAAM,EAAEV,eAAgB;UACxBW,OAAO,EAAEV;QAAiB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN,eAEDtD,OAAA;QAAKiD,KAAK,EAAErC,MAAM,CAACwB,oBAAqB;QAAAc,QAAA,gBACtClD,OAAA;UAAIiD,KAAK,EAAErC,MAAM,CAACyB,gBAAiB;UAAAa,QAAA,EAAC;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9DtD,OAAA;UAAGiD,KAAK,EAAErC,MAAM,CAAC2B,eAAgB;UAAAW,QAAA,EAAC;QAGlC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAEJtD,OAAA;UAAKiD,KAAK,EAAErC,MAAM,CAAC6B,eAAgB;UAAAS,QAAA,gBACjClD,OAAA;YAAKiD,KAAK,EAAErC,MAAM,CAAC+B,UAAW;YAAAO,QAAA,gBAC5BlD,OAAA;cAAKiD,KAAK,EAAE;gBAAC,GAAGrC,MAAM,CAACgC,WAAW;gBAAEjB,eAAe,EAAE;cAAS;YAAE;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvEtD,OAAA;cAAAkD,QAAA,EAAM;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eACNtD,OAAA;YAAKiD,KAAK,EAAErC,MAAM,CAAC+B,UAAW;YAAAO,QAAA,gBAC5BlD,OAAA;cAAKiD,KAAK,EAAE;gBAAC,GAAGrC,MAAM,CAACgC,WAAW;gBAAEjB,eAAe,EAAE;cAAS;YAAE;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvEtD,OAAA;cAAAkD,QAAA,EAAM;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,eACNtD,OAAA;YAAKiD,KAAK,EAAErC,MAAM,CAAC+B,UAAW;YAAAO,QAAA,gBAC5BlD,OAAA;cAAKiD,KAAK,EAAE;gBAAC,GAAGrC,MAAM,CAACgC,WAAW;gBAAEjB,eAAe,EAAE;cAAS;YAAE;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvEtD,OAAA;cAAAkD,QAAA,EAAM;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,eACNtD,OAAA;YAAKiD,KAAK,EAAErC,MAAM,CAAC+B,UAAW;YAAAO,QAAA,gBAC5BlD,OAAA;cAAKiD,KAAK,EAAE;gBAAC,GAAGrC,MAAM,CAACgC,WAAW;gBAAEjB,eAAe,EAAE;cAAS;YAAE;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvEtD,OAAA;cAAAkD,QAAA,EAAM;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eACNtD,OAAA;YAAKiD,KAAK,EAAErC,MAAM,CAAC+B,UAAW;YAAAO,QAAA,gBAC5BlD,OAAA;cAAKiD,KAAK,EAAE;gBAAC,GAAGrC,MAAM,CAACgC,WAAW;gBAAEjB,eAAe,EAAE;cAAS;YAAE;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvEtD,OAAA;cAAAkD,QAAA,EAAM;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,eACNtD,OAAA;YAAKiD,KAAK,EAAErC,MAAM,CAAC+B,UAAW;YAAAO,QAAA,gBAC5BlD,OAAA;cAAKiD,KAAK,EAAE;gBAAC,GAAGrC,MAAM,CAACgC,WAAW;gBAAEjB,eAAe,EAAE;cAAoB;YAAE;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClFtD,OAAA;cAAAkD,QAAA,EAAM;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENtD,OAAA;QAAKiD,KAAK,EAAErC,MAAM,CAACkB,GAAI;QAAAoB,QAAA,gBACrBlD,OAAA;UAAKiD,KAAK,EAAErC,MAAM,CAACoB,QAAS;UAAAkB,QAAA,gBAC1BlD,OAAA;YAAK6C,KAAK,EAAC,IAAI;YAACC,MAAM,EAAC,IAAI;YAACa,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAACC,MAAM,EAAEzD,QAAQ,GAAG,SAAS,GAAG,MAAO;YAAC0D,WAAW,EAAC,GAAG;YAACC,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC,OAAO;YAAAd,QAAA,gBAC7JlD,OAAA;cAAQiE,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC,IAAI;cAACC,CAAC,EAAC;YAAI;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,eACxCtD,OAAA;cAAMoE,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC;YAAI;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7CtD,OAAA;cAAMoE,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC,GAAG;cAACC,EAAE,EAAC,OAAO;cAACC,EAAE,EAAC;YAAG;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,UAER;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNtD,OAAA;UAAAkD,QAAA,EAAG;QAAiK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrK,CAAC;IAAA,eACN,CAAC,gBAEHtD,OAAA;MAAKiD,KAAK,EAAErC,MAAM,CAACc,gBAAiB;MAAAwB,QAAA,eAClClD,OAAA;QAAAkD,QAAA,EAAG;MAA2G;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/G,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAChD,EAAA,CA3LIH,cAA6C;EAAA,QAInCL,WAAW;AAAA;AAAA0E,EAAA,GAJrBrE,cAA6C;AA6LnD,eAAeA,cAAc;AAAC,IAAAqE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}