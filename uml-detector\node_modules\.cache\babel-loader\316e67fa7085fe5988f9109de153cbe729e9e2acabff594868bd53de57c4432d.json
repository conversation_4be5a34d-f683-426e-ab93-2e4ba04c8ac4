{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\FixTorchUMLDGM\\\\uml-detector\\\\src\\\\context\\\\LanguageContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\n\n// Types pour les langues supportées\n\n// Interface pour le contexte de langue\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// Traductions\nconst translations = {\n  fr: {\n    // Header\n    'app.title': 'AI UML Class Analyzer',\n    'button.login': 'Connexion',\n    // Tabs\n    'tab.upload': 'Import d\\'image',\n    'tab.relations': 'Analyse de relations',\n    'tab.text': 'Extraction de diagramme',\n    'tab.documentation': 'Documentation',\n    // Content headers\n    'content.upload.title': 'Import de diagramme UML',\n    'content.upload.subtitle': 'Téléchargez et analysez vos diagrammes de classes UML avec notre IA avancée',\n    'content.relations.title': 'Analyse de relations UML',\n    'content.relations.subtitle': 'Visualisez les relations détectées entre les classes de votre diagramme',\n    'content.text.title': 'Extraction de diagramme',\n    'content.text.subtitle': 'Extrayez et exportez des diagrammes UML',\n    'content.documentation.title': 'Documentation & Ressources',\n    'content.documentation.subtitle': 'Découvrez les fonctionnalités de UML Class Analyzer',\n    // Footer\n    'footer.text': '© 2025 UML Class Analyzer | Édition Enterprise v2.5.3',\n    // Language button\n    'language.current': 'FR',\n    'language.switch': 'Passer en anglais',\n    // ImageUploader\n    'imageUploader.dropZone': 'Glissez-déposez votre diagramme UML ou cliquez pour sélectionner',\n    'imageUploader.selectFile': 'Sélectionner un fichier',\n    'imageUploader.analyzing': 'Analyse en cours...',\n    'imageUploader.progress.waiting': 'En attente de l\\'image...',\n    'imageUploader.progress.converting': 'Conversion et optimisation de l\\'image...',\n    'imageUploader.progress.detecting': 'Détection des classes et relations avec notre modèle IA...',\n    'imageUploader.progress.analyzing': 'Analyse en cours ... Cette étape peut prendre quelques instants.',\n    'imageUploader.progress.fetching': 'Récupération des résultats...',\n    'imageUploader.progress.completed': 'Analyse terminée',\n    'imageUploader.progress.error': 'Erreur pendant l\\'analyse de l\\'image',\n    'imageUploader.progress.fetchError': 'Erreur lors de la récupération des résultats',\n    'imageUploader.error.processing': 'Erreur lors du traitement de l\\'image',\n    'imageUploader.error.connection': 'Erreur de connexion ou analyse',\n    'imageUploader.error.fetchText': 'Impossible de récupérer le texte extrait',\n    'imageUploader.error.thumbnail': 'Miniature non disponible',\n    'imageUploader.button.newAnalysis': 'Nouvelle analyse',\n    'imageUploader.button.viewRelations': 'Voir les relations détectées',\n    'imageUploader.button.extractDiagram': 'Extraire le diagramme',\n    // DetectionArrow\n    'detectionArrow.title': 'Détection de Relations UML',\n    'detectionArrow.loading': 'Chargement de l\\'image annotée...',\n    'detectionArrow.error': 'Impossible de charger l\\'image annotée. Veuillez vérifier que le serveur est en cours d\\'exécution.',\n    'detectionArrow.noImage': 'Aucune image annotée n\\'est disponible. Veuillez d\\'abord télécharger et analyser une image de diagramme UML.',\n    'detectionArrow.imageAlt': 'Diagramme UML annoté avec relations détectées',\n    'detectionArrow.legend.class': 'Classe',\n    'detectionArrow.legend.relation': 'Relation',\n    'detectionArrow.legend.aggregation': 'Agrégation',\n    'detectionArrow.legend.composition': 'Composition',\n    'detectionArrow.legend.generalization': 'Généralisation',\n    'detectionArrow.legend.association': 'Association',\n    'detectionArrow.tip.title': 'Astuce',\n    'detectionArrow.tip.text': 'Pour une analyse plus détaillée, consultez l\\'onglet \"Extraction de texte UML\" qui contient les informations textuelles complètes de toutes les classes détectées.',\n    // UMLDiagrameExtractor\n    'umlExtractor.title': 'Extracteur de Diagramme UML',\n    'umlExtractor.format.diagram': 'Code de diagramme',\n    'umlExtractor.format.java': 'Code Java',\n    'umlExtractor.format.python': 'Code Python',\n    'umlExtractor.placeholder.diagram': 'Entrez votre code diagramme ici...',\n    'umlExtractor.placeholder.java': 'Entrez votre code Java ici...',\n    'umlExtractor.placeholder.python': 'Entrez votre code Python ici...',\n    'umlExtractor.preview.empty': 'Entrez votre code {format} pour le visualiser ici',\n    'umlExtractor.error.syntax': 'Erreur de syntaxe:',\n    'umlExtractor.button.copy': 'Copier',\n    'umlExtractor.button.export': 'Exporter',\n    'umlExtractor.button.fullscreen': 'Plein écran',\n    'umlExtractor.button.zoomIn': 'Zoom +',\n    'umlExtractor.button.zoomOut': 'Zoom -',\n    'umlExtractor.button.resetZoom': 'Réinitialiser zoom',\n    'umlExtractor.copied': 'Copié !',\n    // Authentication\n    'auth.login.title': 'Connexion à votre compte',\n    'auth.signup.title': 'Créer un compte',\n    'auth.field.name': 'Nom',\n    'auth.field.email': 'Email',\n    'auth.field.password': 'Mot de passe',\n    'auth.field.confirmPassword': 'Confirmer le mot de passe',\n    'auth.placeholder.name': 'Votre nom',\n    'auth.placeholder.email': '<EMAIL>',\n    'auth.placeholder.password': 'Créer un mot de passe',\n    'auth.placeholder.confirmPassword': 'Confirmez votre mot de passe',\n    'auth.rememberMe': 'Se souvenir de moi',\n    'auth.forgotPassword': 'Mot de passe oublié ?',\n    'auth.acceptTerms': 'J\\'accepte les conditions d\\'utilisation',\n    'auth.button.login': 'Se connecter',\n    'auth.button.signup': 'S\\'inscrire',\n    'auth.button.createAccount': 'Créer un compte',\n    'auth.button.loggingIn': 'Connexion...',\n    'auth.button.creatingAccount': 'Création du compte...',\n    'auth.button.continueWithGoogle': 'Continuer avec Google',\n    'auth.button.logout': 'Se déconnecter',\n    'auth.or': 'OU',\n    'auth.noAccount': 'Vous n\\'avez pas de compte ?',\n    'auth.haveAccount': 'Vous avez déjà un compte ?',\n    'auth.error.emailRequired': 'L\\'email est requis',\n    'auth.error.emailInvalid': 'Veuillez entrer un email valide',\n    'auth.error.passwordRequired': 'Le mot de passe est requis',\n    'auth.error.passwordTooShort': 'Le mot de passe doit contenir au moins 6 caractères',\n    'auth.error.nameRequired': 'Le nom est requis',\n    'auth.error.confirmPasswordRequired': 'Veuillez confirmer votre mot de passe',\n    'auth.error.passwordMismatch': 'Les mots de passe ne correspondent pas',\n    'auth.error.authFailed': 'Échec de l\\'authentification',\n    'auth.error.invalidCredentials': 'Email ou mot de passe invalide',\n    'auth.error.emailInUse': 'Cet email est déjà utilisé',\n    'auth.error.weakPassword': 'Le mot de passe est trop faible',\n    'auth.error.tooManyRequests': 'Trop de tentatives échouées. Réessayez plus tard.',\n    'auth.error.googleLoginFailed': 'Connexion Google échouée. Veuillez réessayer.',\n    // HistorySidebar\n    'history.title': 'Historique UML',\n    'history.newProject': 'Nouveau projet',\n    'history.empty': 'Aucun historique',\n    'history.loading': 'Chargement...',\n    'history.error': 'Erreur lors du chargement de l\\'historique',\n    'history.deleteConfirm': 'Êtes-vous sûr de vouloir supprimer cet élément ?',\n    'history.deleteAllConfirm': 'Êtes-vous sûr de vouloir supprimer tout votre historique ? Cette action est irréversible.',\n    'history.delete': 'Supprimer',\n    'history.deleteAll': 'Supprimer tout l\\'historique',\n    'history.cancel': 'Annuler',\n    'history.loginPrompt': 'Connectez-vous pour voir votre historique',\n    'history.searchPlaceholder': 'Rechercher...',\n    'history.noResults': 'Aucun résultat trouvé',\n    'history.thumbnail': 'Miniature',\n    'history.reload': 'Recharger',\n    // Analysis Results\n    'analysis.error.updateFailed': 'Échec de la mise à jour du texte. Veuillez réessayer.',\n    'analysis.diagramAlt': 'Diagramme UML',\n    'analysis.tooltip.viewAnnotated': 'Cliquez pour voir l\\'image annotée',\n    'analysis.tooltip.convertDiagram': 'Si votre diagramme est manuscrit et vous souhaitez le convertir vers un diagramme imprimé, cliquez ici',\n    // Conflict Resolution Modal\n    'conflict.title': 'Résolution des Conflits',\n    'conflict.attribute': 'Attribut',\n    'conflict.method': 'Méthode',\n    'conflict.currentVersion': 'Version Actuelle',\n    'conflict.importedVersions': 'Versions Importées',\n    'conflict.keepCurrent': 'Garder l\\'actuel',\n    'conflict.replace': 'Remplacer',\n    'conflict.merge': 'Fusionner',\n    'conflict.cancel': 'Annuler',\n    'conflict.applyResolutions': 'Appliquer les Résolutions',\n    // Entity Popup\n    'entity.subtitle': 'UML IA propose plusieurs choix possibles pour cette entité.',\n    'entity.attributes': 'Attributs',\n    'entity.methods': 'Méthodes',\n    'entity.noAdditionalAttributes': 'Aucun attribut supplémentaire',\n    'entity.noAdditionalMethods': 'Aucune méthode supplémentaire',\n    'entity.modifyEntity': 'Modifier l\\'entité',\n    'entity.cancel': 'Annuler',\n    // Login/Auth\n    'auth.login': 'Connexion',\n    'auth.signup': 'Inscription',\n    'auth.email': 'Email',\n    'auth.password': 'Mot de passe',\n    'auth.confirmPassword': 'Confirmer le mot de passe',\n    'auth.forgotPassword': 'Mot de passe oublié ?',\n    'auth.loginButton': 'Se connecter',\n    'auth.signupButton': 'S\\'inscrire',\n    'auth.logout': 'Déconnexion',\n    'auth.profile': 'Profil',\n    'auth.welcome': 'Bienvenue',\n    'auth.error.invalidCredentials': 'Identifiants invalides',\n    'auth.error.emailExists': 'Cet email existe déjà',\n    'auth.error.passwordMismatch': 'Les mots de passe ne correspondent pas',\n    'auth.error.weakPassword': 'Le mot de passe est trop faible',\n    'auth.success.login': 'Connexion réussie',\n    'auth.success.signup': 'Inscription réussie',\n    'auth.success.logout': 'Déconnexion réussie',\n    // Documentation\n    'doc.title': 'Documentation UML Class Analyzer',\n    'doc.subtitle': 'Guide complet pour l\\'utilisation de notre plateforme d\\'analyse UML alimentée par l\\'IA',\n    'doc.version': 'v2.5.3 Enterprise',\n    'doc.overview': 'Vue d\\'ensemble',\n    'doc.features': 'Fonctionnalités',\n    'doc.guide': 'Guide d\\'utilisation',\n    'doc.technologies': 'Technologies',\n    'doc.api': 'API',\n    'doc.faq': 'FAQ',\n    'doc.projectOverview': 'Vue d\\'ensemble du projet',\n    'doc.appTitle': 'UML Class Analyzer - Édition Enterprise v2.5.3',\n    'doc.gettingStarted': 'Commencer',\n    'doc.tutorials': 'Tutoriels',\n    'doc.support': 'Support',\n    'doc.lastUpdate': 'Dernière mise à jour',\n    // Common buttons and actions\n    'common.ok': 'OK',\n    'common.cancel': 'Annuler',\n    'common.save': 'Sauvegarder',\n    'common.delete': 'Supprimer',\n    'common.edit': 'Modifier',\n    'common.close': 'Fermer',\n    'common.back': 'Retour',\n    'common.next': 'Suivant',\n    'common.previous': 'Précédent',\n    'common.loading': 'Chargement...',\n    'common.error': 'Erreur',\n    'common.success': 'Succès',\n    'common.warning': 'Attention',\n    'common.info': 'Information'\n  },\n  en: {\n    // Header\n    'app.title': 'AI UML Class Analyzer',\n    'button.login': 'Login',\n    // Tabs\n    'tab.upload': 'Image Import',\n    'tab.relations': 'Relations Analysis',\n    'tab.text': 'Diagram Extraction',\n    'tab.documentation': 'Documentation',\n    // Content headers\n    'content.upload.title': 'UML Diagram Import',\n    'content.upload.subtitle': 'Upload and analyze your UML class diagrams with our advanced AI',\n    'content.relations.title': 'UML Relations Analysis',\n    'content.relations.subtitle': 'Visualize detected relationships between classes in your diagram',\n    'content.text.title': 'Diagram Extraction',\n    'content.text.subtitle': 'Extract and export UML diagrams',\n    'content.documentation.title': 'Documentation & Resources',\n    'content.documentation.subtitle': 'Discover UML Class Analyzer features',\n    // Footer\n    'footer.text': '© 2025 UML Class Analyzer | Enterprise Edition v2.5.3',\n    // Language button\n    'language.current': 'EN',\n    'language.switch': 'Switch to French',\n    // ImageUploader\n    'imageUploader.dropZone': 'Drag and drop your UML diagram or click to select',\n    'imageUploader.selectFile': 'Select a file',\n    'imageUploader.analyzing': 'Analyzing...',\n    'imageUploader.progress.waiting': 'Waiting for image...',\n    'imageUploader.progress.converting': 'Converting and optimizing image...',\n    'imageUploader.progress.detecting': 'Detecting classes and relationships with our AI model...',\n    'imageUploader.progress.analyzing': 'Analysis in progress... This step may take a few moments.',\n    'imageUploader.progress.fetching': 'Fetching results...',\n    'imageUploader.progress.completed': 'Analysis completed',\n    'imageUploader.progress.error': 'Error during image analysis',\n    'imageUploader.progress.fetchError': 'Error fetching results',\n    'imageUploader.error.processing': 'Error processing image',\n    'imageUploader.error.connection': 'Connection or analysis error',\n    'imageUploader.error.fetchText': 'Unable to fetch extracted text',\n    'imageUploader.error.thumbnail': 'Thumbnail not available',\n    'imageUploader.button.newAnalysis': 'New Analysis',\n    'imageUploader.button.viewRelations': 'View Detected Relations',\n    'imageUploader.button.extractDiagram': 'Extract Diagram',\n    // DetectionArrow\n    'detectionArrow.title': 'UML Relations Detection',\n    'detectionArrow.loading': 'Loading annotated image...',\n    'detectionArrow.error': 'Unable to load annotated image. Please check that the server is running.',\n    'detectionArrow.noImage': 'No annotated image is available. Please first upload and analyze a UML diagram image.',\n    'detectionArrow.imageAlt': 'UML diagram annotated with detected relationships',\n    'detectionArrow.legend.class': 'Class',\n    'detectionArrow.legend.relation': 'Relation',\n    'detectionArrow.legend.aggregation': 'Aggregation',\n    'detectionArrow.legend.composition': 'Composition',\n    'detectionArrow.legend.generalization': 'Generalization',\n    'detectionArrow.legend.association': 'Association',\n    'detectionArrow.tip.title': 'Tip',\n    'detectionArrow.tip.text': 'For more detailed analysis, check the \"UML Text Extraction\" tab which contains complete textual information of all detected classes.',\n    // UMLDiagrameExtractor\n    'umlExtractor.title': 'UML Diagram Extractor',\n    'umlExtractor.format.diagram': 'Diagram Code',\n    'umlExtractor.format.java': 'Java Code',\n    'umlExtractor.format.python': 'Python Code',\n    'umlExtractor.placeholder.diagram': 'Enter your diagram code here...',\n    'umlExtractor.placeholder.java': 'Enter your Java code here...',\n    'umlExtractor.placeholder.python': 'Enter your Python code here...',\n    'umlExtractor.preview.empty': 'Enter your {format} code to visualize it here',\n    'umlExtractor.error.syntax': 'Syntax error:',\n    'umlExtractor.button.copy': 'Copy',\n    'umlExtractor.button.export': 'Export',\n    'umlExtractor.button.fullscreen': 'Fullscreen',\n    'umlExtractor.button.zoomIn': 'Zoom In',\n    'umlExtractor.button.zoomOut': 'Zoom Out',\n    'umlExtractor.button.resetZoom': 'Reset Zoom',\n    'umlExtractor.copied': 'Copied!',\n    // Authentication\n    'auth.login.title': 'Log in to your account',\n    'auth.signup.title': 'Create an account',\n    'auth.field.name': 'Name',\n    'auth.field.email': 'Email',\n    'auth.field.password': 'Password',\n    'auth.field.confirmPassword': 'Confirm Password',\n    'auth.placeholder.name': 'Your name',\n    'auth.placeholder.email': '<EMAIL>',\n    'auth.placeholder.password': 'Create a password',\n    'auth.placeholder.confirmPassword': 'Confirm your password',\n    'auth.rememberMe': 'Remember me',\n    'auth.forgotPassword': 'Forgot password?',\n    'auth.acceptTerms': 'I accept the Terms of Service',\n    'auth.button.login': 'Log in',\n    'auth.button.signup': 'Sign up',\n    'auth.button.createAccount': 'Create account',\n    'auth.button.loggingIn': 'Logging in...',\n    'auth.button.creatingAccount': 'Creating account...',\n    'auth.button.continueWithGoogle': 'Continue with Google',\n    'auth.button.logout': 'Log out',\n    'auth.or': 'OR',\n    'auth.noAccount': 'Don\\'t have an account?',\n    'auth.haveAccount': 'Already have an account?',\n    'auth.error.emailRequired': 'Email is required',\n    'auth.error.emailInvalid': 'Please enter a valid email',\n    'auth.error.passwordRequired': 'Password is required',\n    'auth.error.passwordTooShort': 'Password must be at least 6 characters',\n    'auth.error.nameRequired': 'Name is required',\n    'auth.error.confirmPasswordRequired': 'Please confirm your password',\n    'auth.error.passwordMismatch': 'Passwords do not match',\n    'auth.error.authFailed': 'Authentication failed',\n    'auth.error.invalidCredentials': 'Invalid email or password',\n    'auth.error.emailInUse': 'Email is already in use',\n    'auth.error.weakPassword': 'Password is too weak',\n    'auth.error.tooManyRequests': 'Too many failed attempts. Try again later.',\n    'auth.error.googleLoginFailed': 'Google login failed. Please try again.',\n    // HistorySidebar\n    'history.title': 'UML History',\n    'history.newProject': 'New Project',\n    'history.empty': 'No history',\n    'history.loading': 'Loading...',\n    'history.error': 'Error loading history',\n    'history.deleteConfirm': 'Are you sure you want to delete this item?',\n    'history.deleteAllConfirm': 'Are you sure you want to delete all your history? This action is irreversible.',\n    'history.delete': 'Delete',\n    'history.deleteAll': 'Delete all history',\n    'history.cancel': 'Cancel',\n    'history.loginPrompt': 'Log in to see your history',\n    'history.searchPlaceholder': 'Search...',\n    'history.noResults': 'No results found',\n    'history.thumbnail': 'Thumbnail',\n    'history.reload': 'Reload',\n    // Analysis Results\n    'analysis.error.updateFailed': 'Failed to update text. Please try again.',\n    'analysis.diagramAlt': 'UML Diagram',\n    'analysis.tooltip.viewAnnotated': 'Click to view annotated image',\n    'analysis.tooltip.convertDiagram': 'If your diagram is handwritten and you want to convert it to a printed diagram, click here',\n    // Conflict Resolution Modal\n    'conflict.title': 'Conflict Resolution',\n    'conflict.attribute': 'Attribute',\n    'conflict.method': 'Method',\n    'conflict.currentVersion': 'Current Version',\n    'conflict.importedVersions': 'Imported Versions',\n    'conflict.keepCurrent': 'Keep Current',\n    'conflict.replace': 'Replace',\n    'conflict.merge': 'Merge',\n    'conflict.cancel': 'Cancel',\n    'conflict.applyResolutions': 'Apply Resolutions',\n    // Entity Popup\n    'entity.subtitle': 'UML AI suggests several possible choices for this entity.',\n    'entity.attributes': 'Attributes',\n    'entity.methods': 'Methods',\n    'entity.noAdditionalAttributes': 'No additional attributes',\n    'entity.noAdditionalMethods': 'No additional methods',\n    'entity.modifyEntity': 'Modify Entity',\n    'entity.cancel': 'Cancel',\n    // Login/Auth\n    'auth.login': 'Login',\n    'auth.signup': 'Sign Up',\n    'auth.email': 'Email',\n    'auth.password': 'Password',\n    'auth.confirmPassword': 'Confirm Password',\n    'auth.forgotPassword': 'Forgot Password?',\n    'auth.loginButton': 'Log In',\n    'auth.signupButton': 'Sign Up',\n    'auth.logout': 'Logout',\n    'auth.profile': 'Profile',\n    'auth.welcome': 'Welcome',\n    'auth.error.invalidCredentials': 'Invalid credentials',\n    'auth.error.emailExists': 'This email already exists',\n    'auth.error.passwordMismatch': 'Passwords do not match',\n    'auth.error.weakPassword': 'Password is too weak',\n    'auth.success.login': 'Login successful',\n    'auth.success.signup': 'Sign up successful',\n    'auth.success.logout': 'Logout successful',\n    // Documentation\n    'doc.title': 'Documentation',\n    'doc.gettingStarted': 'Getting Started',\n    'doc.features': 'Features',\n    'doc.tutorials': 'Tutorials',\n    'doc.faq': 'FAQ',\n    'doc.support': 'Support',\n    'doc.version': 'Version',\n    'doc.lastUpdate': 'Last Update',\n    // Common buttons and actions\n    'common.ok': 'OK',\n    'common.cancel': 'Cancel',\n    'common.save': 'Save',\n    'common.delete': 'Delete',\n    'common.edit': 'Edit',\n    'common.close': 'Close',\n    'common.back': 'Back',\n    'common.next': 'Next',\n    'common.previous': 'Previous',\n    'common.loading': 'Loading...',\n    'common.error': 'Error',\n    'common.success': 'Success',\n    'common.warning': 'Warning',\n    'common.info': 'Information'\n  }\n};\n\n// Création du contexte\nconst LanguageContext = /*#__PURE__*/createContext(undefined);\n\n// Provider du contexte\n\nexport const LanguageProvider = ({\n  children\n}) => {\n  _s();\n  const [language, setLanguageState] = useState('fr');\n\n  // Charger la langue depuis le localStorage au démarrage\n  useEffect(() => {\n    const savedLanguage = localStorage.getItem('app-language');\n    if (savedLanguage && (savedLanguage === 'fr' || savedLanguage === 'en')) {\n      setLanguageState(savedLanguage);\n    }\n  }, []);\n\n  // Sauvegarder la langue dans le localStorage\n  const setLanguage = lang => {\n    setLanguageState(lang);\n    localStorage.setItem('app-language', lang);\n  };\n\n  // Fonction de traduction\n  const t = key => {\n    return translations[language][key] || key;\n  };\n  const value = {\n    language,\n    setLanguage,\n    t\n  };\n  return /*#__PURE__*/_jsxDEV(LanguageContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 483,\n    columnNumber: 5\n  }, this);\n};\n\n// Hook pour utiliser le contexte\n_s(LanguageProvider, \"zJ9zF9Hb+G/qZ3+OQ94tpM7AV7o=\");\n_c = LanguageProvider;\nexport const useLanguage = () => {\n  _s2();\n  const context = useContext(LanguageContext);\n  if (!context) {\n    throw new Error('useLanguage must be used within a LanguageProvider');\n  }\n  return context;\n};\n_s2(useLanguage, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"LanguageProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "jsxDEV", "_jsxDEV", "translations", "fr", "en", "LanguageContext", "undefined", "LanguageProvider", "children", "_s", "language", "setLanguageState", "savedLanguage", "localStorage", "getItem", "setLanguage", "lang", "setItem", "t", "key", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useLanguage", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["C:/Users/<USER>/FixTorchUMLDGM/uml-detector/src/context/LanguageContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\n\n// Types pour les langues supportées\nexport type Language = 'fr' | 'en';\n\n// Interface pour le contexte de langue\ninterface LanguageContextType {\n  language: Language;\n  setLanguage: (lang: Language) => void;\n  t: (key: string) => string;\n}\n\n// Traductions\nconst translations = {\n  fr: {\n    // Header\n    'app.title': 'AI UML Class Analyzer',\n    'button.login': 'Connexion',\n\n    // Tabs\n    'tab.upload': 'Import d\\'image',\n    'tab.relations': 'Analyse de relations',\n    'tab.text': 'Extraction de diagramme',\n    'tab.documentation': 'Documentation',\n\n    // Content headers\n    'content.upload.title': 'Import de diagramme UML',\n    'content.upload.subtitle': 'Téléchargez et analysez vos diagrammes de classes UML avec notre IA avancée',\n    'content.relations.title': 'Analyse de relations UML',\n    'content.relations.subtitle': 'Visualisez les relations détectées entre les classes de votre diagramme',\n    'content.text.title': 'Extraction de diagramme',\n    'content.text.subtitle': 'Extrayez et exportez des diagrammes UML',\n    'content.documentation.title': 'Documentation & Ressources',\n    'content.documentation.subtitle': 'Découvrez les fonctionnalités de UML Class Analyzer',\n\n    // Footer\n    'footer.text': '© 2025 UML Class Analyzer | Édition Enterprise v2.5.3',\n\n    // Language button\n    'language.current': 'FR',\n    'language.switch': 'Passer en anglais',\n\n    // ImageUploader\n    'imageUploader.dropZone': 'Glissez-déposez votre diagramme UML ou cliquez pour sélectionner',\n    'imageUploader.selectFile': 'Sélectionner un fichier',\n    'imageUploader.analyzing': 'Analyse en cours...',\n    'imageUploader.progress.waiting': 'En attente de l\\'image...',\n    'imageUploader.progress.converting': 'Conversion et optimisation de l\\'image...',\n    'imageUploader.progress.detecting': 'Détection des classes et relations avec notre modèle IA...',\n    'imageUploader.progress.analyzing': 'Analyse en cours ... Cette étape peut prendre quelques instants.',\n    'imageUploader.progress.fetching': 'Récupération des résultats...',\n    'imageUploader.progress.completed': 'Analyse terminée',\n    'imageUploader.progress.error': 'Erreur pendant l\\'analyse de l\\'image',\n    'imageUploader.progress.fetchError': 'Erreur lors de la récupération des résultats',\n    'imageUploader.error.processing': 'Erreur lors du traitement de l\\'image',\n    'imageUploader.error.connection': 'Erreur de connexion ou analyse',\n    'imageUploader.error.fetchText': 'Impossible de récupérer le texte extrait',\n    'imageUploader.error.thumbnail': 'Miniature non disponible',\n    'imageUploader.button.newAnalysis': 'Nouvelle analyse',\n    'imageUploader.button.viewRelations': 'Voir les relations détectées',\n    'imageUploader.button.extractDiagram': 'Extraire le diagramme',\n\n    // DetectionArrow\n    'detectionArrow.title': 'Détection de Relations UML',\n    'detectionArrow.loading': 'Chargement de l\\'image annotée...',\n    'detectionArrow.error': 'Impossible de charger l\\'image annotée. Veuillez vérifier que le serveur est en cours d\\'exécution.',\n    'detectionArrow.noImage': 'Aucune image annotée n\\'est disponible. Veuillez d\\'abord télécharger et analyser une image de diagramme UML.',\n    'detectionArrow.imageAlt': 'Diagramme UML annoté avec relations détectées',\n    'detectionArrow.legend.class': 'Classe',\n    'detectionArrow.legend.relation': 'Relation',\n    'detectionArrow.legend.aggregation': 'Agrégation',\n    'detectionArrow.legend.composition': 'Composition',\n    'detectionArrow.legend.generalization': 'Généralisation',\n    'detectionArrow.legend.association': 'Association',\n    'detectionArrow.tip.title': 'Astuce',\n    'detectionArrow.tip.text': 'Pour une analyse plus détaillée, consultez l\\'onglet \"Extraction de texte UML\" qui contient les informations textuelles complètes de toutes les classes détectées.',\n\n    // UMLDiagrameExtractor\n    'umlExtractor.title': 'Extracteur de Diagramme UML',\n    'umlExtractor.format.diagram': 'Code de diagramme',\n    'umlExtractor.format.java': 'Code Java',\n    'umlExtractor.format.python': 'Code Python',\n    'umlExtractor.placeholder.diagram': 'Entrez votre code diagramme ici...',\n    'umlExtractor.placeholder.java': 'Entrez votre code Java ici...',\n    'umlExtractor.placeholder.python': 'Entrez votre code Python ici...',\n    'umlExtractor.preview.empty': 'Entrez votre code {format} pour le visualiser ici',\n    'umlExtractor.error.syntax': 'Erreur de syntaxe:',\n    'umlExtractor.button.copy': 'Copier',\n    'umlExtractor.button.export': 'Exporter',\n    'umlExtractor.button.fullscreen': 'Plein écran',\n    'umlExtractor.button.zoomIn': 'Zoom +',\n    'umlExtractor.button.zoomOut': 'Zoom -',\n    'umlExtractor.button.resetZoom': 'Réinitialiser zoom',\n    'umlExtractor.copied': 'Copié !',\n\n    // Authentication\n    'auth.login.title': 'Connexion à votre compte',\n    'auth.signup.title': 'Créer un compte',\n    'auth.field.name': 'Nom',\n    'auth.field.email': 'Email',\n    'auth.field.password': 'Mot de passe',\n    'auth.field.confirmPassword': 'Confirmer le mot de passe',\n    'auth.placeholder.name': 'Votre nom',\n    'auth.placeholder.email': '<EMAIL>',\n    'auth.placeholder.password': 'Créer un mot de passe',\n    'auth.placeholder.confirmPassword': 'Confirmez votre mot de passe',\n    'auth.rememberMe': 'Se souvenir de moi',\n    'auth.forgotPassword': 'Mot de passe oublié ?',\n    'auth.acceptTerms': 'J\\'accepte les conditions d\\'utilisation',\n    'auth.button.login': 'Se connecter',\n    'auth.button.signup': 'S\\'inscrire',\n    'auth.button.createAccount': 'Créer un compte',\n    'auth.button.loggingIn': 'Connexion...',\n    'auth.button.creatingAccount': 'Création du compte...',\n    'auth.button.continueWithGoogle': 'Continuer avec Google',\n    'auth.button.logout': 'Se déconnecter',\n    'auth.or': 'OU',\n    'auth.noAccount': 'Vous n\\'avez pas de compte ?',\n    'auth.haveAccount': 'Vous avez déjà un compte ?',\n    'auth.error.emailRequired': 'L\\'email est requis',\n    'auth.error.emailInvalid': 'Veuillez entrer un email valide',\n    'auth.error.passwordRequired': 'Le mot de passe est requis',\n    'auth.error.passwordTooShort': 'Le mot de passe doit contenir au moins 6 caractères',\n    'auth.error.nameRequired': 'Le nom est requis',\n    'auth.error.confirmPasswordRequired': 'Veuillez confirmer votre mot de passe',\n    'auth.error.passwordMismatch': 'Les mots de passe ne correspondent pas',\n    'auth.error.authFailed': 'Échec de l\\'authentification',\n    'auth.error.invalidCredentials': 'Email ou mot de passe invalide',\n    'auth.error.emailInUse': 'Cet email est déjà utilisé',\n    'auth.error.weakPassword': 'Le mot de passe est trop faible',\n    'auth.error.tooManyRequests': 'Trop de tentatives échouées. Réessayez plus tard.',\n    'auth.error.googleLoginFailed': 'Connexion Google échouée. Veuillez réessayer.',\n\n    // HistorySidebar\n    'history.title': 'Historique UML',\n    'history.newProject': 'Nouveau projet',\n    'history.empty': 'Aucun historique',\n    'history.loading': 'Chargement...',\n    'history.error': 'Erreur lors du chargement de l\\'historique',\n    'history.deleteConfirm': 'Êtes-vous sûr de vouloir supprimer cet élément ?',\n    'history.deleteAllConfirm': 'Êtes-vous sûr de vouloir supprimer tout votre historique ? Cette action est irréversible.',\n    'history.delete': 'Supprimer',\n    'history.deleteAll': 'Supprimer tout l\\'historique',\n    'history.cancel': 'Annuler',\n    'history.loginPrompt': 'Connectez-vous pour voir votre historique',\n    'history.searchPlaceholder': 'Rechercher...',\n    'history.noResults': 'Aucun résultat trouvé',\n    'history.thumbnail': 'Miniature',\n    'history.reload': 'Recharger',\n\n    // Analysis Results\n    'analysis.error.updateFailed': 'Échec de la mise à jour du texte. Veuillez réessayer.',\n    'analysis.diagramAlt': 'Diagramme UML',\n    'analysis.tooltip.viewAnnotated': 'Cliquez pour voir l\\'image annotée',\n    'analysis.tooltip.convertDiagram': 'Si votre diagramme est manuscrit et vous souhaitez le convertir vers un diagramme imprimé, cliquez ici',\n\n    // Conflict Resolution Modal\n    'conflict.title': 'Résolution des Conflits',\n    'conflict.attribute': 'Attribut',\n    'conflict.method': 'Méthode',\n    'conflict.currentVersion': 'Version Actuelle',\n    'conflict.importedVersions': 'Versions Importées',\n    'conflict.keepCurrent': 'Garder l\\'actuel',\n    'conflict.replace': 'Remplacer',\n    'conflict.merge': 'Fusionner',\n    'conflict.cancel': 'Annuler',\n    'conflict.applyResolutions': 'Appliquer les Résolutions',\n\n    // Entity Popup\n    'entity.subtitle': 'UML IA propose plusieurs choix possibles pour cette entité.',\n    'entity.attributes': 'Attributs',\n    'entity.methods': 'Méthodes',\n    'entity.noAdditionalAttributes': 'Aucun attribut supplémentaire',\n    'entity.noAdditionalMethods': 'Aucune méthode supplémentaire',\n    'entity.modifyEntity': 'Modifier l\\'entité',\n    'entity.cancel': 'Annuler',\n\n    // Login/Auth\n    'auth.login': 'Connexion',\n    'auth.signup': 'Inscription',\n    'auth.email': 'Email',\n    'auth.password': 'Mot de passe',\n    'auth.confirmPassword': 'Confirmer le mot de passe',\n    'auth.forgotPassword': 'Mot de passe oublié ?',\n    'auth.loginButton': 'Se connecter',\n    'auth.signupButton': 'S\\'inscrire',\n    'auth.logout': 'Déconnexion',\n    'auth.profile': 'Profil',\n    'auth.welcome': 'Bienvenue',\n    'auth.error.invalidCredentials': 'Identifiants invalides',\n    'auth.error.emailExists': 'Cet email existe déjà',\n    'auth.error.passwordMismatch': 'Les mots de passe ne correspondent pas',\n    'auth.error.weakPassword': 'Le mot de passe est trop faible',\n    'auth.success.login': 'Connexion réussie',\n    'auth.success.signup': 'Inscription réussie',\n    'auth.success.logout': 'Déconnexion réussie',\n\n\n\n    // Documentation\n    'doc.title': 'Documentation UML Class Analyzer',\n    'doc.subtitle': 'Guide complet pour l\\'utilisation de notre plateforme d\\'analyse UML alimentée par l\\'IA',\n    'doc.version': 'v2.5.3 Enterprise',\n    'doc.overview': 'Vue d\\'ensemble',\n    'doc.features': 'Fonctionnalités',\n    'doc.guide': 'Guide d\\'utilisation',\n    'doc.technologies': 'Technologies',\n    'doc.api': 'API',\n    'doc.faq': 'FAQ',\n    'doc.projectOverview': 'Vue d\\'ensemble du projet',\n    'doc.appTitle': 'UML Class Analyzer - Édition Enterprise v2.5.3',\n    'doc.gettingStarted': 'Commencer',\n    'doc.tutorials': 'Tutoriels',\n    'doc.support': 'Support',\n    'doc.lastUpdate': 'Dernière mise à jour',\n\n    // Common buttons and actions\n    'common.ok': 'OK',\n    'common.cancel': 'Annuler',\n    'common.save': 'Sauvegarder',\n    'common.delete': 'Supprimer',\n    'common.edit': 'Modifier',\n    'common.close': 'Fermer',\n    'common.back': 'Retour',\n    'common.next': 'Suivant',\n    'common.previous': 'Précédent',\n    'common.loading': 'Chargement...',\n    'common.error': 'Erreur',\n    'common.success': 'Succès',\n    'common.warning': 'Attention',\n    'common.info': 'Information',\n  },\n  en: {\n    // Header\n    'app.title': 'AI UML Class Analyzer',\n    'button.login': 'Login',\n\n    // Tabs\n    'tab.upload': 'Image Import',\n    'tab.relations': 'Relations Analysis',\n    'tab.text': 'Diagram Extraction',\n    'tab.documentation': 'Documentation',\n\n    // Content headers\n    'content.upload.title': 'UML Diagram Import',\n    'content.upload.subtitle': 'Upload and analyze your UML class diagrams with our advanced AI',\n    'content.relations.title': 'UML Relations Analysis',\n    'content.relations.subtitle': 'Visualize detected relationships between classes in your diagram',\n    'content.text.title': 'Diagram Extraction',\n    'content.text.subtitle': 'Extract and export UML diagrams',\n    'content.documentation.title': 'Documentation & Resources',\n    'content.documentation.subtitle': 'Discover UML Class Analyzer features',\n\n    // Footer\n    'footer.text': '© 2025 UML Class Analyzer | Enterprise Edition v2.5.3',\n\n    // Language button\n    'language.current': 'EN',\n    'language.switch': 'Switch to French',\n\n    // ImageUploader\n    'imageUploader.dropZone': 'Drag and drop your UML diagram or click to select',\n    'imageUploader.selectFile': 'Select a file',\n    'imageUploader.analyzing': 'Analyzing...',\n    'imageUploader.progress.waiting': 'Waiting for image...',\n    'imageUploader.progress.converting': 'Converting and optimizing image...',\n    'imageUploader.progress.detecting': 'Detecting classes and relationships with our AI model...',\n    'imageUploader.progress.analyzing': 'Analysis in progress... This step may take a few moments.',\n    'imageUploader.progress.fetching': 'Fetching results...',\n    'imageUploader.progress.completed': 'Analysis completed',\n    'imageUploader.progress.error': 'Error during image analysis',\n    'imageUploader.progress.fetchError': 'Error fetching results',\n    'imageUploader.error.processing': 'Error processing image',\n    'imageUploader.error.connection': 'Connection or analysis error',\n    'imageUploader.error.fetchText': 'Unable to fetch extracted text',\n    'imageUploader.error.thumbnail': 'Thumbnail not available',\n    'imageUploader.button.newAnalysis': 'New Analysis',\n    'imageUploader.button.viewRelations': 'View Detected Relations',\n    'imageUploader.button.extractDiagram': 'Extract Diagram',\n\n    // DetectionArrow\n    'detectionArrow.title': 'UML Relations Detection',\n    'detectionArrow.loading': 'Loading annotated image...',\n    'detectionArrow.error': 'Unable to load annotated image. Please check that the server is running.',\n    'detectionArrow.noImage': 'No annotated image is available. Please first upload and analyze a UML diagram image.',\n    'detectionArrow.imageAlt': 'UML diagram annotated with detected relationships',\n    'detectionArrow.legend.class': 'Class',\n    'detectionArrow.legend.relation': 'Relation',\n    'detectionArrow.legend.aggregation': 'Aggregation',\n    'detectionArrow.legend.composition': 'Composition',\n    'detectionArrow.legend.generalization': 'Generalization',\n    'detectionArrow.legend.association': 'Association',\n    'detectionArrow.tip.title': 'Tip',\n    'detectionArrow.tip.text': 'For more detailed analysis, check the \"UML Text Extraction\" tab which contains complete textual information of all detected classes.',\n\n    // UMLDiagrameExtractor\n    'umlExtractor.title': 'UML Diagram Extractor',\n    'umlExtractor.format.diagram': 'Diagram Code',\n    'umlExtractor.format.java': 'Java Code',\n    'umlExtractor.format.python': 'Python Code',\n    'umlExtractor.placeholder.diagram': 'Enter your diagram code here...',\n    'umlExtractor.placeholder.java': 'Enter your Java code here...',\n    'umlExtractor.placeholder.python': 'Enter your Python code here...',\n    'umlExtractor.preview.empty': 'Enter your {format} code to visualize it here',\n    'umlExtractor.error.syntax': 'Syntax error:',\n    'umlExtractor.button.copy': 'Copy',\n    'umlExtractor.button.export': 'Export',\n    'umlExtractor.button.fullscreen': 'Fullscreen',\n    'umlExtractor.button.zoomIn': 'Zoom In',\n    'umlExtractor.button.zoomOut': 'Zoom Out',\n    'umlExtractor.button.resetZoom': 'Reset Zoom',\n    'umlExtractor.copied': 'Copied!',\n\n    // Authentication\n    'auth.login.title': 'Log in to your account',\n    'auth.signup.title': 'Create an account',\n    'auth.field.name': 'Name',\n    'auth.field.email': 'Email',\n    'auth.field.password': 'Password',\n    'auth.field.confirmPassword': 'Confirm Password',\n    'auth.placeholder.name': 'Your name',\n    'auth.placeholder.email': '<EMAIL>',\n    'auth.placeholder.password': 'Create a password',\n    'auth.placeholder.confirmPassword': 'Confirm your password',\n    'auth.rememberMe': 'Remember me',\n    'auth.forgotPassword': 'Forgot password?',\n    'auth.acceptTerms': 'I accept the Terms of Service',\n    'auth.button.login': 'Log in',\n    'auth.button.signup': 'Sign up',\n    'auth.button.createAccount': 'Create account',\n    'auth.button.loggingIn': 'Logging in...',\n    'auth.button.creatingAccount': 'Creating account...',\n    'auth.button.continueWithGoogle': 'Continue with Google',\n    'auth.button.logout': 'Log out',\n    'auth.or': 'OR',\n    'auth.noAccount': 'Don\\'t have an account?',\n    'auth.haveAccount': 'Already have an account?',\n    'auth.error.emailRequired': 'Email is required',\n    'auth.error.emailInvalid': 'Please enter a valid email',\n    'auth.error.passwordRequired': 'Password is required',\n    'auth.error.passwordTooShort': 'Password must be at least 6 characters',\n    'auth.error.nameRequired': 'Name is required',\n    'auth.error.confirmPasswordRequired': 'Please confirm your password',\n    'auth.error.passwordMismatch': 'Passwords do not match',\n    'auth.error.authFailed': 'Authentication failed',\n    'auth.error.invalidCredentials': 'Invalid email or password',\n    'auth.error.emailInUse': 'Email is already in use',\n    'auth.error.weakPassword': 'Password is too weak',\n    'auth.error.tooManyRequests': 'Too many failed attempts. Try again later.',\n    'auth.error.googleLoginFailed': 'Google login failed. Please try again.',\n\n    // HistorySidebar\n    'history.title': 'UML History',\n    'history.newProject': 'New Project',\n    'history.empty': 'No history',\n    'history.loading': 'Loading...',\n    'history.error': 'Error loading history',\n    'history.deleteConfirm': 'Are you sure you want to delete this item?',\n    'history.deleteAllConfirm': 'Are you sure you want to delete all your history? This action is irreversible.',\n    'history.delete': 'Delete',\n    'history.deleteAll': 'Delete all history',\n    'history.cancel': 'Cancel',\n    'history.loginPrompt': 'Log in to see your history',\n    'history.searchPlaceholder': 'Search...',\n    'history.noResults': 'No results found',\n    'history.thumbnail': 'Thumbnail',\n    'history.reload': 'Reload',\n\n    // Analysis Results\n    'analysis.error.updateFailed': 'Failed to update text. Please try again.',\n    'analysis.diagramAlt': 'UML Diagram',\n    'analysis.tooltip.viewAnnotated': 'Click to view annotated image',\n    'analysis.tooltip.convertDiagram': 'If your diagram is handwritten and you want to convert it to a printed diagram, click here',\n\n    // Conflict Resolution Modal\n    'conflict.title': 'Conflict Resolution',\n    'conflict.attribute': 'Attribute',\n    'conflict.method': 'Method',\n    'conflict.currentVersion': 'Current Version',\n    'conflict.importedVersions': 'Imported Versions',\n    'conflict.keepCurrent': 'Keep Current',\n    'conflict.replace': 'Replace',\n    'conflict.merge': 'Merge',\n    'conflict.cancel': 'Cancel',\n    'conflict.applyResolutions': 'Apply Resolutions',\n\n    // Entity Popup\n    'entity.subtitle': 'UML AI suggests several possible choices for this entity.',\n    'entity.attributes': 'Attributes',\n    'entity.methods': 'Methods',\n    'entity.noAdditionalAttributes': 'No additional attributes',\n    'entity.noAdditionalMethods': 'No additional methods',\n    'entity.modifyEntity': 'Modify Entity',\n    'entity.cancel': 'Cancel',\n\n    // Login/Auth\n    'auth.login': 'Login',\n    'auth.signup': 'Sign Up',\n    'auth.email': 'Email',\n    'auth.password': 'Password',\n    'auth.confirmPassword': 'Confirm Password',\n    'auth.forgotPassword': 'Forgot Password?',\n    'auth.loginButton': 'Log In',\n    'auth.signupButton': 'Sign Up',\n    'auth.logout': 'Logout',\n    'auth.profile': 'Profile',\n    'auth.welcome': 'Welcome',\n    'auth.error.invalidCredentials': 'Invalid credentials',\n    'auth.error.emailExists': 'This email already exists',\n    'auth.error.passwordMismatch': 'Passwords do not match',\n    'auth.error.weakPassword': 'Password is too weak',\n    'auth.success.login': 'Login successful',\n    'auth.success.signup': 'Sign up successful',\n    'auth.success.logout': 'Logout successful',\n\n\n\n    // Documentation\n    'doc.title': 'Documentation',\n    'doc.gettingStarted': 'Getting Started',\n    'doc.features': 'Features',\n    'doc.tutorials': 'Tutorials',\n    'doc.faq': 'FAQ',\n    'doc.support': 'Support',\n    'doc.version': 'Version',\n    'doc.lastUpdate': 'Last Update',\n\n    // Common buttons and actions\n    'common.ok': 'OK',\n    'common.cancel': 'Cancel',\n    'common.save': 'Save',\n    'common.delete': 'Delete',\n    'common.edit': 'Edit',\n    'common.close': 'Close',\n    'common.back': 'Back',\n    'common.next': 'Next',\n    'common.previous': 'Previous',\n    'common.loading': 'Loading...',\n    'common.error': 'Error',\n    'common.success': 'Success',\n    'common.warning': 'Warning',\n    'common.info': 'Information',\n  }\n};\n\n// Création du contexte\nconst LanguageContext = createContext<LanguageContextType | undefined>(undefined);\n\n// Provider du contexte\ninterface LanguageProviderProps {\n  children: ReactNode;\n}\n\nexport const LanguageProvider: React.FC<LanguageProviderProps> = ({ children }) => {\n  const [language, setLanguageState] = useState<Language>('fr');\n\n  // Charger la langue depuis le localStorage au démarrage\n  useEffect(() => {\n    const savedLanguage = localStorage.getItem('app-language') as Language;\n    if (savedLanguage && (savedLanguage === 'fr' || savedLanguage === 'en')) {\n      setLanguageState(savedLanguage);\n    }\n  }, []);\n\n  // Sauvegarder la langue dans le localStorage\n  const setLanguage = (lang: Language) => {\n    setLanguageState(lang);\n    localStorage.setItem('app-language', lang);\n  };\n\n  // Fonction de traduction\n  const t = (key: string): string => {\n    return translations[language][key as keyof typeof translations[typeof language]] || key;\n  };\n\n  const value: LanguageContextType = {\n    language,\n    setLanguage,\n    t\n  };\n\n  return (\n    <LanguageContext.Provider value={value}>\n      {children}\n    </LanguageContext.Provider>\n  );\n};\n\n// Hook pour utiliser le contexte\nexport const useLanguage = (): LanguageContextType => {\n  const context = useContext(LanguageContext);\n  if (!context) {\n    throw new Error('useLanguage must be used within a LanguageProvider');\n  }\n  return context;\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAmB,OAAO;;AAExF;;AAGA;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAOA;AACA,MAAMC,YAAY,GAAG;EACnBC,EAAE,EAAE;IACF;IACA,WAAW,EAAE,uBAAuB;IACpC,cAAc,EAAE,WAAW;IAE3B;IACA,YAAY,EAAE,iBAAiB;IAC/B,eAAe,EAAE,sBAAsB;IACvC,UAAU,EAAE,yBAAyB;IACrC,mBAAmB,EAAE,eAAe;IAEpC;IACA,sBAAsB,EAAE,yBAAyB;IACjD,yBAAyB,EAAE,6EAA6E;IACxG,yBAAyB,EAAE,0BAA0B;IACrD,4BAA4B,EAAE,yEAAyE;IACvG,oBAAoB,EAAE,yBAAyB;IAC/C,uBAAuB,EAAE,yCAAyC;IAClE,6BAA6B,EAAE,4BAA4B;IAC3D,gCAAgC,EAAE,qDAAqD;IAEvF;IACA,aAAa,EAAE,uDAAuD;IAEtE;IACA,kBAAkB,EAAE,IAAI;IACxB,iBAAiB,EAAE,mBAAmB;IAEtC;IACA,wBAAwB,EAAE,kEAAkE;IAC5F,0BAA0B,EAAE,yBAAyB;IACrD,yBAAyB,EAAE,qBAAqB;IAChD,gCAAgC,EAAE,2BAA2B;IAC7D,mCAAmC,EAAE,2CAA2C;IAChF,kCAAkC,EAAE,4DAA4D;IAChG,kCAAkC,EAAE,kEAAkE;IACtG,iCAAiC,EAAE,+BAA+B;IAClE,kCAAkC,EAAE,kBAAkB;IACtD,8BAA8B,EAAE,uCAAuC;IACvE,mCAAmC,EAAE,8CAA8C;IACnF,gCAAgC,EAAE,uCAAuC;IACzE,gCAAgC,EAAE,gCAAgC;IAClE,+BAA+B,EAAE,0CAA0C;IAC3E,+BAA+B,EAAE,0BAA0B;IAC3D,kCAAkC,EAAE,kBAAkB;IACtD,oCAAoC,EAAE,8BAA8B;IACpE,qCAAqC,EAAE,uBAAuB;IAE9D;IACA,sBAAsB,EAAE,4BAA4B;IACpD,wBAAwB,EAAE,mCAAmC;IAC7D,sBAAsB,EAAE,qGAAqG;IAC7H,wBAAwB,EAAE,+GAA+G;IACzI,yBAAyB,EAAE,+CAA+C;IAC1E,6BAA6B,EAAE,QAAQ;IACvC,gCAAgC,EAAE,UAAU;IAC5C,mCAAmC,EAAE,YAAY;IACjD,mCAAmC,EAAE,aAAa;IAClD,sCAAsC,EAAE,gBAAgB;IACxD,mCAAmC,EAAE,aAAa;IAClD,0BAA0B,EAAE,QAAQ;IACpC,yBAAyB,EAAE,oKAAoK;IAE/L;IACA,oBAAoB,EAAE,6BAA6B;IACnD,6BAA6B,EAAE,mBAAmB;IAClD,0BAA0B,EAAE,WAAW;IACvC,4BAA4B,EAAE,aAAa;IAC3C,kCAAkC,EAAE,oCAAoC;IACxE,+BAA+B,EAAE,+BAA+B;IAChE,iCAAiC,EAAE,iCAAiC;IACpE,4BAA4B,EAAE,mDAAmD;IACjF,2BAA2B,EAAE,oBAAoB;IACjD,0BAA0B,EAAE,QAAQ;IACpC,4BAA4B,EAAE,UAAU;IACxC,gCAAgC,EAAE,aAAa;IAC/C,4BAA4B,EAAE,QAAQ;IACtC,6BAA6B,EAAE,QAAQ;IACvC,+BAA+B,EAAE,oBAAoB;IACrD,qBAAqB,EAAE,SAAS;IAEhC;IACA,kBAAkB,EAAE,0BAA0B;IAC9C,mBAAmB,EAAE,iBAAiB;IACtC,iBAAiB,EAAE,KAAK;IACxB,kBAAkB,EAAE,OAAO;IAC3B,qBAAqB,EAAE,cAAc;IACrC,4BAA4B,EAAE,2BAA2B;IACzD,uBAAuB,EAAE,WAAW;IACpC,wBAAwB,EAAE,yBAAyB;IACnD,2BAA2B,EAAE,uBAAuB;IACpD,kCAAkC,EAAE,8BAA8B;IAClE,iBAAiB,EAAE,oBAAoB;IACvC,qBAAqB,EAAE,uBAAuB;IAC9C,kBAAkB,EAAE,0CAA0C;IAC9D,mBAAmB,EAAE,cAAc;IACnC,oBAAoB,EAAE,aAAa;IACnC,2BAA2B,EAAE,iBAAiB;IAC9C,uBAAuB,EAAE,cAAc;IACvC,6BAA6B,EAAE,uBAAuB;IACtD,gCAAgC,EAAE,uBAAuB;IACzD,oBAAoB,EAAE,gBAAgB;IACtC,SAAS,EAAE,IAAI;IACf,gBAAgB,EAAE,8BAA8B;IAChD,kBAAkB,EAAE,4BAA4B;IAChD,0BAA0B,EAAE,qBAAqB;IACjD,yBAAyB,EAAE,iCAAiC;IAC5D,6BAA6B,EAAE,4BAA4B;IAC3D,6BAA6B,EAAE,qDAAqD;IACpF,yBAAyB,EAAE,mBAAmB;IAC9C,oCAAoC,EAAE,uCAAuC;IAC7E,6BAA6B,EAAE,wCAAwC;IACvE,uBAAuB,EAAE,8BAA8B;IACvD,+BAA+B,EAAE,gCAAgC;IACjE,uBAAuB,EAAE,4BAA4B;IACrD,yBAAyB,EAAE,iCAAiC;IAC5D,4BAA4B,EAAE,mDAAmD;IACjF,8BAA8B,EAAE,+CAA+C;IAE/E;IACA,eAAe,EAAE,gBAAgB;IACjC,oBAAoB,EAAE,gBAAgB;IACtC,eAAe,EAAE,kBAAkB;IACnC,iBAAiB,EAAE,eAAe;IAClC,eAAe,EAAE,4CAA4C;IAC7D,uBAAuB,EAAE,kDAAkD;IAC3E,0BAA0B,EAAE,2FAA2F;IACvH,gBAAgB,EAAE,WAAW;IAC7B,mBAAmB,EAAE,8BAA8B;IACnD,gBAAgB,EAAE,SAAS;IAC3B,qBAAqB,EAAE,2CAA2C;IAClE,2BAA2B,EAAE,eAAe;IAC5C,mBAAmB,EAAE,uBAAuB;IAC5C,mBAAmB,EAAE,WAAW;IAChC,gBAAgB,EAAE,WAAW;IAE7B;IACA,6BAA6B,EAAE,uDAAuD;IACtF,qBAAqB,EAAE,eAAe;IACtC,gCAAgC,EAAE,oCAAoC;IACtE,iCAAiC,EAAE,wGAAwG;IAE3I;IACA,gBAAgB,EAAE,yBAAyB;IAC3C,oBAAoB,EAAE,UAAU;IAChC,iBAAiB,EAAE,SAAS;IAC5B,yBAAyB,EAAE,kBAAkB;IAC7C,2BAA2B,EAAE,oBAAoB;IACjD,sBAAsB,EAAE,kBAAkB;IAC1C,kBAAkB,EAAE,WAAW;IAC/B,gBAAgB,EAAE,WAAW;IAC7B,iBAAiB,EAAE,SAAS;IAC5B,2BAA2B,EAAE,2BAA2B;IAExD;IACA,iBAAiB,EAAE,6DAA6D;IAChF,mBAAmB,EAAE,WAAW;IAChC,gBAAgB,EAAE,UAAU;IAC5B,+BAA+B,EAAE,+BAA+B;IAChE,4BAA4B,EAAE,+BAA+B;IAC7D,qBAAqB,EAAE,oBAAoB;IAC3C,eAAe,EAAE,SAAS;IAE1B;IACA,YAAY,EAAE,WAAW;IACzB,aAAa,EAAE,aAAa;IAC5B,YAAY,EAAE,OAAO;IACrB,eAAe,EAAE,cAAc;IAC/B,sBAAsB,EAAE,2BAA2B;IACnD,qBAAqB,EAAE,uBAAuB;IAC9C,kBAAkB,EAAE,cAAc;IAClC,mBAAmB,EAAE,aAAa;IAClC,aAAa,EAAE,aAAa;IAC5B,cAAc,EAAE,QAAQ;IACxB,cAAc,EAAE,WAAW;IAC3B,+BAA+B,EAAE,wBAAwB;IACzD,wBAAwB,EAAE,uBAAuB;IACjD,6BAA6B,EAAE,wCAAwC;IACvE,yBAAyB,EAAE,iCAAiC;IAC5D,oBAAoB,EAAE,mBAAmB;IACzC,qBAAqB,EAAE,qBAAqB;IAC5C,qBAAqB,EAAE,qBAAqB;IAI5C;IACA,WAAW,EAAE,kCAAkC;IAC/C,cAAc,EAAE,0FAA0F;IAC1G,aAAa,EAAE,mBAAmB;IAClC,cAAc,EAAE,iBAAiB;IACjC,cAAc,EAAE,iBAAiB;IACjC,WAAW,EAAE,sBAAsB;IACnC,kBAAkB,EAAE,cAAc;IAClC,SAAS,EAAE,KAAK;IAChB,SAAS,EAAE,KAAK;IAChB,qBAAqB,EAAE,2BAA2B;IAClD,cAAc,EAAE,gDAAgD;IAChE,oBAAoB,EAAE,WAAW;IACjC,eAAe,EAAE,WAAW;IAC5B,aAAa,EAAE,SAAS;IACxB,gBAAgB,EAAE,sBAAsB;IAExC;IACA,WAAW,EAAE,IAAI;IACjB,eAAe,EAAE,SAAS;IAC1B,aAAa,EAAE,aAAa;IAC5B,eAAe,EAAE,WAAW;IAC5B,aAAa,EAAE,UAAU;IACzB,cAAc,EAAE,QAAQ;IACxB,aAAa,EAAE,QAAQ;IACvB,aAAa,EAAE,SAAS;IACxB,iBAAiB,EAAE,WAAW;IAC9B,gBAAgB,EAAE,eAAe;IACjC,cAAc,EAAE,QAAQ;IACxB,gBAAgB,EAAE,QAAQ;IAC1B,gBAAgB,EAAE,WAAW;IAC7B,aAAa,EAAE;EACjB,CAAC;EACDC,EAAE,EAAE;IACF;IACA,WAAW,EAAE,uBAAuB;IACpC,cAAc,EAAE,OAAO;IAEvB;IACA,YAAY,EAAE,cAAc;IAC5B,eAAe,EAAE,oBAAoB;IACrC,UAAU,EAAE,oBAAoB;IAChC,mBAAmB,EAAE,eAAe;IAEpC;IACA,sBAAsB,EAAE,oBAAoB;IAC5C,yBAAyB,EAAE,iEAAiE;IAC5F,yBAAyB,EAAE,wBAAwB;IACnD,4BAA4B,EAAE,kEAAkE;IAChG,oBAAoB,EAAE,oBAAoB;IAC1C,uBAAuB,EAAE,iCAAiC;IAC1D,6BAA6B,EAAE,2BAA2B;IAC1D,gCAAgC,EAAE,sCAAsC;IAExE;IACA,aAAa,EAAE,uDAAuD;IAEtE;IACA,kBAAkB,EAAE,IAAI;IACxB,iBAAiB,EAAE,kBAAkB;IAErC;IACA,wBAAwB,EAAE,mDAAmD;IAC7E,0BAA0B,EAAE,eAAe;IAC3C,yBAAyB,EAAE,cAAc;IACzC,gCAAgC,EAAE,sBAAsB;IACxD,mCAAmC,EAAE,oCAAoC;IACzE,kCAAkC,EAAE,0DAA0D;IAC9F,kCAAkC,EAAE,2DAA2D;IAC/F,iCAAiC,EAAE,qBAAqB;IACxD,kCAAkC,EAAE,oBAAoB;IACxD,8BAA8B,EAAE,6BAA6B;IAC7D,mCAAmC,EAAE,wBAAwB;IAC7D,gCAAgC,EAAE,wBAAwB;IAC1D,gCAAgC,EAAE,8BAA8B;IAChE,+BAA+B,EAAE,gCAAgC;IACjE,+BAA+B,EAAE,yBAAyB;IAC1D,kCAAkC,EAAE,cAAc;IAClD,oCAAoC,EAAE,yBAAyB;IAC/D,qCAAqC,EAAE,iBAAiB;IAExD;IACA,sBAAsB,EAAE,yBAAyB;IACjD,wBAAwB,EAAE,4BAA4B;IACtD,sBAAsB,EAAE,0EAA0E;IAClG,wBAAwB,EAAE,uFAAuF;IACjH,yBAAyB,EAAE,mDAAmD;IAC9E,6BAA6B,EAAE,OAAO;IACtC,gCAAgC,EAAE,UAAU;IAC5C,mCAAmC,EAAE,aAAa;IAClD,mCAAmC,EAAE,aAAa;IAClD,sCAAsC,EAAE,gBAAgB;IACxD,mCAAmC,EAAE,aAAa;IAClD,0BAA0B,EAAE,KAAK;IACjC,yBAAyB,EAAE,sIAAsI;IAEjK;IACA,oBAAoB,EAAE,uBAAuB;IAC7C,6BAA6B,EAAE,cAAc;IAC7C,0BAA0B,EAAE,WAAW;IACvC,4BAA4B,EAAE,aAAa;IAC3C,kCAAkC,EAAE,iCAAiC;IACrE,+BAA+B,EAAE,8BAA8B;IAC/D,iCAAiC,EAAE,gCAAgC;IACnE,4BAA4B,EAAE,+CAA+C;IAC7E,2BAA2B,EAAE,eAAe;IAC5C,0BAA0B,EAAE,MAAM;IAClC,4BAA4B,EAAE,QAAQ;IACtC,gCAAgC,EAAE,YAAY;IAC9C,4BAA4B,EAAE,SAAS;IACvC,6BAA6B,EAAE,UAAU;IACzC,+BAA+B,EAAE,YAAY;IAC7C,qBAAqB,EAAE,SAAS;IAEhC;IACA,kBAAkB,EAAE,wBAAwB;IAC5C,mBAAmB,EAAE,mBAAmB;IACxC,iBAAiB,EAAE,MAAM;IACzB,kBAAkB,EAAE,OAAO;IAC3B,qBAAqB,EAAE,UAAU;IACjC,4BAA4B,EAAE,kBAAkB;IAChD,uBAAuB,EAAE,WAAW;IACpC,wBAAwB,EAAE,wBAAwB;IAClD,2BAA2B,EAAE,mBAAmB;IAChD,kCAAkC,EAAE,uBAAuB;IAC3D,iBAAiB,EAAE,aAAa;IAChC,qBAAqB,EAAE,kBAAkB;IACzC,kBAAkB,EAAE,+BAA+B;IACnD,mBAAmB,EAAE,QAAQ;IAC7B,oBAAoB,EAAE,SAAS;IAC/B,2BAA2B,EAAE,gBAAgB;IAC7C,uBAAuB,EAAE,eAAe;IACxC,6BAA6B,EAAE,qBAAqB;IACpD,gCAAgC,EAAE,sBAAsB;IACxD,oBAAoB,EAAE,SAAS;IAC/B,SAAS,EAAE,IAAI;IACf,gBAAgB,EAAE,yBAAyB;IAC3C,kBAAkB,EAAE,0BAA0B;IAC9C,0BAA0B,EAAE,mBAAmB;IAC/C,yBAAyB,EAAE,4BAA4B;IACvD,6BAA6B,EAAE,sBAAsB;IACrD,6BAA6B,EAAE,wCAAwC;IACvE,yBAAyB,EAAE,kBAAkB;IAC7C,oCAAoC,EAAE,8BAA8B;IACpE,6BAA6B,EAAE,wBAAwB;IACvD,uBAAuB,EAAE,uBAAuB;IAChD,+BAA+B,EAAE,2BAA2B;IAC5D,uBAAuB,EAAE,yBAAyB;IAClD,yBAAyB,EAAE,sBAAsB;IACjD,4BAA4B,EAAE,4CAA4C;IAC1E,8BAA8B,EAAE,wCAAwC;IAExE;IACA,eAAe,EAAE,aAAa;IAC9B,oBAAoB,EAAE,aAAa;IACnC,eAAe,EAAE,YAAY;IAC7B,iBAAiB,EAAE,YAAY;IAC/B,eAAe,EAAE,uBAAuB;IACxC,uBAAuB,EAAE,4CAA4C;IACrE,0BAA0B,EAAE,gFAAgF;IAC5G,gBAAgB,EAAE,QAAQ;IAC1B,mBAAmB,EAAE,oBAAoB;IACzC,gBAAgB,EAAE,QAAQ;IAC1B,qBAAqB,EAAE,4BAA4B;IACnD,2BAA2B,EAAE,WAAW;IACxC,mBAAmB,EAAE,kBAAkB;IACvC,mBAAmB,EAAE,WAAW;IAChC,gBAAgB,EAAE,QAAQ;IAE1B;IACA,6BAA6B,EAAE,0CAA0C;IACzE,qBAAqB,EAAE,aAAa;IACpC,gCAAgC,EAAE,+BAA+B;IACjE,iCAAiC,EAAE,4FAA4F;IAE/H;IACA,gBAAgB,EAAE,qBAAqB;IACvC,oBAAoB,EAAE,WAAW;IACjC,iBAAiB,EAAE,QAAQ;IAC3B,yBAAyB,EAAE,iBAAiB;IAC5C,2BAA2B,EAAE,mBAAmB;IAChD,sBAAsB,EAAE,cAAc;IACtC,kBAAkB,EAAE,SAAS;IAC7B,gBAAgB,EAAE,OAAO;IACzB,iBAAiB,EAAE,QAAQ;IAC3B,2BAA2B,EAAE,mBAAmB;IAEhD;IACA,iBAAiB,EAAE,2DAA2D;IAC9E,mBAAmB,EAAE,YAAY;IACjC,gBAAgB,EAAE,SAAS;IAC3B,+BAA+B,EAAE,0BAA0B;IAC3D,4BAA4B,EAAE,uBAAuB;IACrD,qBAAqB,EAAE,eAAe;IACtC,eAAe,EAAE,QAAQ;IAEzB;IACA,YAAY,EAAE,OAAO;IACrB,aAAa,EAAE,SAAS;IACxB,YAAY,EAAE,OAAO;IACrB,eAAe,EAAE,UAAU;IAC3B,sBAAsB,EAAE,kBAAkB;IAC1C,qBAAqB,EAAE,kBAAkB;IACzC,kBAAkB,EAAE,QAAQ;IAC5B,mBAAmB,EAAE,SAAS;IAC9B,aAAa,EAAE,QAAQ;IACvB,cAAc,EAAE,SAAS;IACzB,cAAc,EAAE,SAAS;IACzB,+BAA+B,EAAE,qBAAqB;IACtD,wBAAwB,EAAE,2BAA2B;IACrD,6BAA6B,EAAE,wBAAwB;IACvD,yBAAyB,EAAE,sBAAsB;IACjD,oBAAoB,EAAE,kBAAkB;IACxC,qBAAqB,EAAE,oBAAoB;IAC3C,qBAAqB,EAAE,mBAAmB;IAI1C;IACA,WAAW,EAAE,eAAe;IAC5B,oBAAoB,EAAE,iBAAiB;IACvC,cAAc,EAAE,UAAU;IAC1B,eAAe,EAAE,WAAW;IAC5B,SAAS,EAAE,KAAK;IAChB,aAAa,EAAE,SAAS;IACxB,aAAa,EAAE,SAAS;IACxB,gBAAgB,EAAE,aAAa;IAE/B;IACA,WAAW,EAAE,IAAI;IACjB,eAAe,EAAE,QAAQ;IACzB,aAAa,EAAE,MAAM;IACrB,eAAe,EAAE,QAAQ;IACzB,aAAa,EAAE,MAAM;IACrB,cAAc,EAAE,OAAO;IACvB,aAAa,EAAE,MAAM;IACrB,aAAa,EAAE,MAAM;IACrB,iBAAiB,EAAE,UAAU;IAC7B,gBAAgB,EAAE,YAAY;IAC9B,cAAc,EAAE,OAAO;IACvB,gBAAgB,EAAE,SAAS;IAC3B,gBAAgB,EAAE,SAAS;IAC3B,aAAa,EAAE;EACjB;AACF,CAAC;;AAED;AACA,MAAMC,eAAe,gBAAGT,aAAa,CAAkCU,SAAS,CAAC;;AAEjF;;AAKA,OAAO,MAAMC,gBAAiD,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACjF,MAAM,CAACC,QAAQ,EAAEC,gBAAgB,CAAC,GAAGb,QAAQ,CAAW,IAAI,CAAC;;EAE7D;EACAC,SAAS,CAAC,MAAM;IACd,MAAMa,aAAa,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAa;IACtE,IAAIF,aAAa,KAAKA,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,IAAI,CAAC,EAAE;MACvED,gBAAgB,CAACC,aAAa,CAAC;IACjC;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMG,WAAW,GAAIC,IAAc,IAAK;IACtCL,gBAAgB,CAACK,IAAI,CAAC;IACtBH,YAAY,CAACI,OAAO,CAAC,cAAc,EAAED,IAAI,CAAC;EAC5C,CAAC;;EAED;EACA,MAAME,CAAC,GAAIC,GAAW,IAAa;IACjC,OAAOjB,YAAY,CAACQ,QAAQ,CAAC,CAACS,GAAG,CAA+C,IAAIA,GAAG;EACzF,CAAC;EAED,MAAMC,KAA0B,GAAG;IACjCV,QAAQ;IACRK,WAAW;IACXG;EACF,CAAC;EAED,oBACEjB,OAAA,CAACI,eAAe,CAACgB,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAZ,QAAA,EACpCA;EAAQ;IAAAc,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACe,CAAC;AAE/B,CAAC;;AAED;AAAAhB,EAAA,CAnCaF,gBAAiD;AAAAmB,EAAA,GAAjDnB,gBAAiD;AAoC9D,OAAO,MAAMoB,WAAW,GAAGA,CAAA,KAA2B;EAAAC,GAAA;EACpD,MAAMC,OAAO,GAAGhC,UAAU,CAACQ,eAAe,CAAC;EAC3C,IAAI,CAACwB,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,oDAAoD,CAAC;EACvE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,WAAW;AAAA,IAAAD,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}